import type { HttpContextContract } from '@ioc:Adonis/Core/HttpContext'
import Database from '@ioc:Adonis/Lucid/Database'
import Exam from 'App/Models/Exam'
import Role from 'App/Models/Role'
import Specialty from 'App/Models/Specialty'
import User from 'App/Models/User'
import UserInfo from 'App/Models/UserInfo'
import ActionLogChanges from 'App/Services/ActionLogChanges'

import { StoreValidator } from 'App/Validators/Admin/Accredited/Import'

export default class ImportController {
	public async store({ request, response, auth }: HttpContextContract) {
		const { users } = await request.validate(StoreValidator)

		const userLogged = auth.user!

		await Database.transaction(async (trx) => {
			for await (const currentUser of users) {
				const {
					specialtiesNames,
					examsNames,
					queryValue,
					type,
					email,
					password,
					...dataUserInfo
				} = currentUser

				const queryValueAtt = queryValue * 100

				const user = await User.query()
					.where('email', email)
					.preload('roles')
					.preload('userInfo')
					.first()

				const specialties = specialtiesNames
					? await Specialty.query()
						.select('id', 'name', 'secure_id')
						.whereIn('name', specialtiesNames.split(/,\s*|\s*,\s*/).map(specialty => specialty.trim()))
					: undefined

				const exams = examsNames
					? await Exam.query()
						.select('id', 'name', 'secure_id')
						.whereIn('name', examsNames.split(/,\s*|\s*,\s*/).map(exam => exam.trim()))
					: undefined

				if (specialties) {
					currentUser['specialtiesSecureIds'] = specialties.map(specialty => specialty.secureId)
				}

				if (exams) {
					currentUser['examsSecureIds'] = exams.map(specialty => specialty.secureId)
				}

				if (user) {
					const role = await Role.query().where('name', 'ACCREDITED').firstOrFail()

					if (!user.roles.find((role) => role.name === 'ACCREDITED')) {
						await user.useTransaction(trx).related('roles').attach([role.id])
					}

					await ActionLogChanges.saveLogUserChanges({
						userChange: user,
						userChangedData: {
							...currentUser
						},
						userLogged,
						trx
					})

					user.merge({
						type,
						password: password ? password : (dataUserInfo.legalDocumentNumber?.replace(/[^0-9]/g, '') ?? '123456')
					})
					user.useTransaction(trx)
					await user.save()

					const userInfo = user.userInfo
					userInfo.merge({
						...dataUserInfo,
						queryValue: queryValueAtt,
					})
					userInfo.useTransaction(trx)
					await userInfo.save()

					if (specialties) {
						await user
							.useTransaction(trx)
							.related('specialties')
							.sync(specialties.map((specialty) => specialty.id))
					}

					if (exams) {
						await user
							.useTransaction(trx)
							.related('exams')
							.sync(exams.map((exam) => exam.id))
					}
				} else {
					const newUser = new User()

					newUser.merge({
						email,
						type,
						password: password ? password : (dataUserInfo.legalDocumentNumber?.replace(/[^0-9]/g, '') ?? '123456'),
						isFirstAccess: password ? false : true
					})
					newUser.useTransaction(trx)
					await newUser.save()

					const newUserInfo = new UserInfo()
					newUserInfo.merge({
						userId: newUser.id,
						...dataUserInfo,
						queryValue: queryValueAtt
					})
					newUserInfo.useTransaction(trx)
					await newUserInfo.save()

					const rolesSearch = await Role.query().where('name', 'ACCREDITED')
					await newUser
						.useTransaction(trx)
						.related('roles')
						.sync(rolesSearch.map((role) => role.id))

					if (specialties) {
						await newUser
							.useTransaction(trx)
							.related('specialties')
							.sync(specialties.map((specialty) => specialty.id))
					}

					if (exams) {
						await newUser
							.useTransaction(trx)
							.related('exams')
							.sync(exams.map((exam) => exam.id))
					}
				}
			}
		})

		return response.ok({
			type: 'success',
			message: 'Credenciados importados com sucesso!',
		})
	}
}
