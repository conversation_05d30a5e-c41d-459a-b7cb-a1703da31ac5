import Event from '@ioc:Adonis/Core/Event'
import Logger from '@ioc:Adonis/Core/Logger'
import Notification from 'App/Models/Notification'
import TemplateNotifications from 'App/Models/TemplateNotifications'
import { format } from 'date-fns'
import { DateTime } from 'luxon'

type TemplateProps = "consultSolicitation" |
  "examSolicitation" |
  "consultConfirmation" |
  "examConfirmation" |
  "consultOptions" |
  "examOptions" |
  "consult3h" |
  "consult24h" |
  "exam3h" |
  "exam24h" |
  "examCancelForPatient" |
  "consultCancelForPatient" |
  "examCancelForPatientSendBackoffice" |
  "consultCancelForPatientSendBackoffice"

type DataNotificationProps = {
  patient?: {
    id: number
    name: string
    email: string
    dddCell: number
    cell: number
    oneSignalKey?: string
  }
  doctor?: {
    id: number;
    name: string;
    specialty: string;
    email: string;
  }
  laboratory?: {
    name: string
    exam: string
    preparation?: string
    email: string
  }
  schedule?: {
    typeAppointment: string
    date: string
    realizationDate: string | null
    cancelDate: string | null
  }
  exams?: string
  place?: {
    state: string
    neighborhood: string
    city: string
  }
  payment?: {
    date: string
    amount: string
  }
}

export default {
  async getNotification(data: DataNotificationProps, type: string, notificationName: TemplateProps) {
    const namePatient = data.patient && data.patient.name
    const nameDoctor = data.doctor!.name
    const specialty = data.doctor && data.doctor.specialty
    const nameLaboratory = data.laboratory && data.laboratory.name
    const exam = data.laboratory && data.laboratory.exam
    const preparations =
      data.laboratory && !!data.laboratory?.preparation && data.laboratory.preparation
    const dateSchedule = data.schedule && format(new Date(data.schedule.date), 'dd/MM/yyyy H:mm:ss')
    const datePayment = data.payment && format(new Date(data.payment.date), 'dd/MM/yyyy')
    const amountPayment = data.payment && (Number(data.payment.amount) / 100).toLocaleString('pt-br', { style: 'currency', currency: 'BRL' })
    const dateExecution = (data.schedule && data.schedule.realizationDate) ? format(new Date(data.schedule.realizationDate), 'dd/MM/yyyy H:mm:ss') : undefined
    const localization =
      data.place && data.schedule
        ? data.schedule.typeAppointment === 'video_call'
          ? 'Online'
          : `${data.place.state}, - ${data.place.neighborhood} - ${data.place.city}`
        : ''
    const dateCancel = data.schedule && format(new Date(data.schedule.cancelDate!), 'dd/MM/yyyy H:mm:ss')
    const examsPay = data.exams

    const notification = await TemplateNotifications.query()
      .where('type', type)
      .andWhere('name', notificationName)
      .firstOrFail()

    let subject = notification.subject
    if (subject) {
      if (namePatient) subject = subject.replace('{{nomePaciente}}', namePatient)
      if (nameDoctor) subject = subject.replace('{{nomeMedico}}', nameDoctor)
      if (specialty) subject = subject.replace('{{especialidade}}', specialty)
      if (nameLaboratory) subject = subject.replace('{{laboratorio}}', nameLaboratory)
      if (exam) subject = subject.replace('{{exam}}', exam)
      if (preparations) subject = subject.replace('{{preparations}}', preparations)
      if (dateSchedule) subject = subject.replace('{{dataConsulta}}', dateSchedule)
      if (datePayment) subject = subject.replace('{{dataPagamento}}', datePayment)
      if (amountPayment) subject = subject.replace('{{valorPago}}', amountPayment)
      if (dateExecution) subject = subject.replace('{{dataRealizacao}}', dateExecution)
      if (localization) subject = subject.replace('{{local}}', localization)
      if (dateCancel) subject = subject.replace('{{dataCancelamento}}', dateCancel)
      if (examsPay) subject = subject.replace('{{examsPay}}', examsPay)
    }

    let content = notification.content
    if (namePatient) content = content.replace('{{nomePaciente}}', namePatient)
    if (nameDoctor) content = content.replace('{{nomeMedico}}', nameDoctor)
    if (specialty) content = content.replace('{{especialidade}}', specialty)
    if (nameLaboratory) content = content.replace('{{laboratorio}}', nameLaboratory)
    if (exam) content = content.replace('{{exam}}', exam)
    if (preparations) content = content.replace('{{preparations}}', preparations)
    if (dateSchedule) content = content.replace('{{dataConsulta}}', dateSchedule)
    if (datePayment) content = content.replace('{{dataPagamento}}', datePayment)
    if (amountPayment) content = content.replace('{{valorPago}}', amountPayment)
    if (dateExecution) content = content.replace('{{dataRealizacao}}', dateExecution)
    if (localization) content = content.replace('{{local}}', localization)
    if (dateCancel) content = content.replace('{{dataCancelamento}}', dateCancel)
    if (examsPay) content = content.replace('{{examsPay}}', examsPay)

    return { ...data, subject, content, email: notification.email }
  },

  async createNotificationDB(data: DataNotificationProps, notificationName: TemplateProps) {
    try {
      if (data.patient && (notificationName !== 'examCancelForPatientSendBackoffice' && notificationName !== 'consultCancelForPatientSendBackoffice')) {
        const userId = data.patient.id
        const dataNotification = await this.getNotification(data, 'push', notificationName)

        await Notification.create({
          userId,
          title: dataNotification.subject,
          text: dataNotification.content
        });
      }
    } catch (error) {
      Logger.error(error)
    }
  },

  async sendSMS(data: DataNotificationProps, notificationName: TemplateProps, date?: DateTime) {
    try {
      if (data.patient && date == undefined && (notificationName !== 'examCancelForPatientSendBackoffice' && notificationName !== 'consultCancelForPatientSendBackoffice')) {
        const dataNotification = await this.getNotification(data, 'sms', notificationName)

        const phone = `55${data.patient.dddCell}${data.patient.cell}`

        Event.emit('new:zenviaSms', {
          content: dataNotification.content,
          phone: phone
        });
      }
    } catch (error) {
      Logger.error(error)
    }
  },

  async sendPUSH(data: DataNotificationProps, notificationName: TemplateProps, date?: DateTime) {
    try {
      if (data.patient && (notificationName !== 'examCancelForPatientSendBackoffice' && notificationName !== 'consultCancelForPatientSendBackoffice')) {
        const dataNotification = await this.getNotification(data, 'push', notificationName)

        if (data.patient.oneSignalKey) {
          const playerIds: string[] = [data.patient.oneSignalKey]

          Event.emit('new:oneSignalNotification', {
            title: 'HelloMed',
            content: dataNotification.content,
            ids: playerIds,
            date: date
          })
        }
      }
    } catch (error) {
      Logger.error(error)
    }
  },

  async sendEMAIL(data: DataNotificationProps, notificationName: TemplateProps, date?: DateTime) {
    try {
      if (data.patient && (notificationName !== 'examCancelForPatientSendBackoffice' && notificationName !== 'consultCancelForPatientSendBackoffice')) {
        const email = data.patient.email

        const dataNotification = await this.getNotification(data, 'email', notificationName)

        Event.emit('new:oneSignalEmail', {
          subject: `HelloMed | ${dataNotification.subject}`,
          content: dataNotification.content,
          emails: [email],
          date: date
        })
      } else if (notificationName === 'examCancelForPatientSendBackoffice' || notificationName === 'consultCancelForPatientSendBackoffice') {
        const dataNotification = await this.getNotification(data, 'email', notificationName)
        const email = dataNotification.email

        Event.emit('new:oneSignalEmail', {
          subject: `HelloMed | ${dataNotification.subject}`,
          content: dataNotification.content,
          emails: [email],
          date: date
        })
      }
    } catch (error) {
      Logger.error(error)
    }
  },

  async sendNotifications(data: DataNotificationProps, notificationName: TemplateProps, date?: DateTime) {
    await this.sendSMS(data, notificationName, date)
    await this.sendPUSH(data, notificationName, date)
    await this.sendEMAIL(data, notificationName, date)
    await this.createNotificationDB(data, notificationName)
  }
}
