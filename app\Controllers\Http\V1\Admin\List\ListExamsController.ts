import type { HttpContextContract } from '@ioc:Adonis/Core/HttpContext'
import { schema } from '@ioc:Adonis/Core/Validator'

import Exam from 'App/Models/Exam'

export default class ListExamsController {
	public async index({ response, request }: HttpContextContract) {
		const schemaValidator = schema.create({
			search: schema.string.optional(),
			status: schema.array.optional().members(schema.boolean())
		})

		const {
			search,
			status = [true]
		} = await request.validate({
			schema: schemaValidator
		})

		const exams = await Exam.query()
			.select('id', 'secure_id', 'name', 'active')
			.whereIn('active', status)
			.andWhere(builder => {
				if (search) {
					builder.whereILike('name', `%${search}%`)
				}
			})

		return response.ok(exams)
	}
}
