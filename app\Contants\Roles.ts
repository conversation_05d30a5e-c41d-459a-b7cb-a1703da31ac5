import { string } from '@ioc:Adonis/Core/Helpers'

export const SYSTEM_ROLES = ['MASTER', 'ADMIN', 'PARTNER', 'PATIENT', 'ACCREDITED'] as const

export type SystemRoles = (typeof SYSTEM_ROLES)[number]

type Role = { name: SystemRoles; slug: string }

type Roles = { [key in SystemRoles]: Role }

export const ROLES: Roles = SYSTEM_ROLES.reduce((roles, role) => {
	return Object.assign(roles, {
		[role]: {
			name: string.snakeCase(role).toUpperCase(),
			slug: string.dashCase(role, { capitalize: false }),
		},
	})
}, {}) as Roles

export function isRoles(rolesNames: SystemRoles[]) {
	const rolesSlugs = rolesNames.map((roleName) => ROLES[roleName].name)

	return `is:${rolesSlugs.join(',')}`
}

export function allRoles() {
	const allRolesSlugs = Object.values(ROLES).map(({ name }) => name)

	return `is:${allRolesSlugs.join(',')}`
}
