import BaseSchema from '@ioc:Adonis/Lucid/Schema'

export default class extends BaseSchema {
  protected tableName = 'appointments'

  public async up () {
		this.schema.alterTable(this.tableName, (table) => {
			table.enu('status', [
				'waiting',
				'approved',
				'did_not_attend',
				'realized',
				'finalized',
				'canceled',
				'canceled_by_patient',
				'canceled_at_patient_request',
				'canceled_by_backoffice'
			]).alter()
		})
  }

  public async down () {
    this.schema.alterTable(this.tableName, (table) => {
			table.enu('status', [
				'waiting',
				'approved',
				'did_not_attend',
				'realized',
				'canceled',
				'canceled_by_patient',
				'canceled_at_patient_request',
				'canceled_by_backoffice'
			]).alter()
		})
  }
}
