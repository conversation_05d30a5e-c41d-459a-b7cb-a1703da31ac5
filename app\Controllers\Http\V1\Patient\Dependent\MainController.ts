import type { HttpContextContract } from '@ioc:Adonis/Core/HttpContext'
import Database from '@ioc:Adonis/Lucid/Database'
import Role from 'App/Models/Role'
import Upload from 'App/Models/Upload'
import User from 'App/Models/User'
import UserInfo from 'App/Models/UserInfo'

import { StoreValidator, UpdateValidator } from 'App/Validators/Patient/Dependent'

export default class DependentController {
	public async index({ response, request, auth }: HttpContextContract) {
		const { page = 1, limit = 15, search } = request.only(['page', 'limit', 'search'])
		const userLogged = auth.user!
		const patients = await User.query()
			.select('users.id', 'users.parent_id', 'users.secure_id')
			.whereHas('roles', (builder) => {
				builder.whereIn('slug', ['patient'])
			})
			.andWhere((builder) => {
				if (search) {
					builder.whereILike('users.email', `%${search}%`)
					builder.orWhereHas('userInfo', builder => {
						builder.whereILike('name', `%${search}%`)
					})
				}
			})
			.andWhereNot('users.id', userLogged.id)
			.andWhere('users.parent_id', userLogged.id)
			.andWhereNot('users.deleted', true)
			.preload('userInfo', (builder) => {
				builder.select(
					'id',
					'name',
					Database.raw('SUBSTRING(legal_document_number, 1, 3) as legal_document_number')
				)
			})
			.join('user_infos', 'user_infos.user_id', 'users.id')
			.orderBy('user_infos.name', 'asc')
			.paginate(page, limit)

		return response.ok(patients)
	}

	public async store({ request, response, auth }: HttpContextContract) {
		const { avatarSecureId, ...data } = await request.validate(StoreValidator)

		const parent = auth.user!

		await Database.transaction(async (trx) => {
			const newUser = new User()

			if (avatarSecureId) {
				const avatar = await Upload.query().where('secure_id', avatarSecureId).firstOrFail()

				newUser.merge({
					avatarId: avatar.id,
				})
			}

			newUser.merge({
				email: parent.email,
				type: 'dependent',
				password: String(Date.now()),
				parentId: parent.id,
			})
			newUser.useTransaction(trx)
			await newUser.save()

			const newUserInfo = new UserInfo()
			newUserInfo.merge({
				...data,
				userId: newUser.id,
			})
			newUserInfo.useTransaction(trx)
			await newUserInfo.save()

			const rolesSearch = await Role.query().where('name', 'PATIENT')
			await newUser
				.useTransaction(trx)
				.related('roles')
				.sync(rolesSearch.map((role) => role.id))
		})

		return response.ok({
			type: 'success',
			message: 'Dependente criado com sucesso!',
		})
	}

	public async show({ response, params, auth }: HttpContextContract) {
		const userLogged = auth.user!

		const patient = await User.query()
			.where('secure_id', params.id)
			.andWhereHas('roles', (builder) => {
				builder.whereIn('slug', ['patient'])
			})
			.andWhere('parent_id', userLogged.id)
			.preload('userInfo', (builder) => {
				builder.select('name', 'legal_document_number')
			})
			.preload('permissions')
			.preload('parent')
			.firstOrFail()

		return response.ok(patient)
	}

	public async update({ response, request, params }: HttpContextContract) {
		const { avatarSecureId, ...data } = await request.validate(UpdateValidator)

		const user = await User.query()
			.where('secure_id', params.id)
			.andWhereHas('roles', (builder) => {
				builder.whereIn('slug', ['patient'])
			})
			.andHas('parent')
			.firstOrFail()

		await Database.transaction(async (trx) => {
			if (avatarSecureId) {
				const avatar = await Upload.query().where('secure_id', avatarSecureId).firstOrFail()

				user.merge({
					avatarId: avatar.id,
				})
			}

			user.merge({
				email: user.email
			})

			user.useTransaction(trx)
			await user.save()

			const userInfo = await UserInfo.query()
				.where('userId', user.id)
				.firstOrFail()
			userInfo.merge({
				...data
			})

			userInfo.useTransaction(trx)
			await userInfo.save()
		})

		return response.ok({
			type: 'success',
			message: 'Dependente atualizado com sucesso!',
		})
	}

	public async destroy({ response, params, auth }: HttpContextContract) {
		const userLogged = auth.user!

		const patient = await User.query()
			.where('secure_id', params.id)
			.andWhereHas('roles', (builder) => {
				builder.whereIn('slug', ['patient'])
			})
			.andWhere('parent_id', userLogged.id)
			.firstOrFail()

		patient.merge({ deleted: true })
		await patient.save()

		return response.ok({
			type: 'success',
			message: 'Dependente removido com sucesso!',
		})
	}
}
