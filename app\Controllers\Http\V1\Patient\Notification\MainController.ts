import type { HttpContextContract } from '@ioc:Adonis/Core/HttpContext'
import Database from '@ioc:Adonis/Lucid/Database'
import { DateTime } from 'luxon'

import Notification from 'App/Models/Notification'

export default class NotificationController {
	public async index({ response, request, auth }: HttpContextContract) {
		const {
			page = 1,
			limit = 15,
			search,
			status = ['read', 'not_read'],
		} = request.only(['page', 'limit', 'search', 'status'])
		const userLogged = auth.user!
		const notifications = await Notification.query()
			.where('user_id', userLogged.id)
			.andWhere((builder) => {
				if (!!search) {
					builder.whereILike('name', `%${search}%`)
					builder.orWhereILike('email', `%${search}%`)
				}
			})
			.andWhere('user_id', userLogged.id)
			.andWhereIn('status', status)
			.orderBy('created_at', 'desc')
			.paginate(page, limit)

		return response.ok(notifications)
	}

	public async show({ response, params, auth }: HttpContextContract) {
		const userLogged = auth.user!

		const notification = await Notification.query()
			.where('secure_id', params.id)
			.andWhere('user_id', userLogged.id)
			.firstOrFail()

		return response.ok(notification)
	}

	public async update({ response, params, auth }: HttpContextContract) {
		const userLogged = auth.user!

		const notification = await Notification.query()
			.where('secure_id', params.id)
			.andWhere('user_id', userLogged.id)
			.firstOrFail()

		await Database.transaction(async (trx) => {
			notification.merge({
				status: 'read',
				readAt: DateTime.now(),
			})
			notification.useTransaction(trx)
			await notification.save()
		})

		return response.ok({
			type: 'success',
			message: 'Notificação visualizada!',
		})
	}
}
