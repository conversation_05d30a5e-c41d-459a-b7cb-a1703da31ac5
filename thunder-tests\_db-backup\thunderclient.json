[{"_id": "fb7c107f-a769-48f5-b7eb-2f79de91e73d", "colId": "90e9227e-7a36-45f1-a8bf-55dc59313a5b", "containerId": "", "name": "<PERSON><PERSON>", "url": "{{url}}/v1/sessions", "method": "POST", "sortNum": 10000, "created": "2023-04-04T20:03:28.376Z", "modified": "2023-06-20T19:03:12.360Z", "headers": [], "params": [], "body": {"type": "formencoded", "raw": "", "form": [{"name": "email", "value": "{{email}}"}, {"name": "password", "value": "{{password}}"}]}, "auth": {"type": "bearer", "bearer": "{{token}}"}, "tests": [{"type": "set-env-var", "custom": "json.token.token", "action": "setto", "value": "{{token,local}}"}], "docs": "# Autenticação na plataforma\n\nPara autenticação devem ser enviados os seguintes campos:\nAtributo | Tipo | Descrição\n:------ | :------: | :------\n```email``` <font color='#dd1e2e'>required</font>  | ```string``` | Emaild do usuário\n```password``` <font color='#dd1e2e'>required</font>| ```string``` | Senha do usuário"}, {"_id": "121921c5-5f19-484c-a1ea-593366581019", "colId": "90e9227e-7a36-45f1-a8bf-55dc59313a5b", "containerId": "", "name": "Logout", "url": "{{url}}/v1/sessions", "method": "DELETE", "sortNum": 20000, "created": "2023-04-04T20:11:22.127Z", "modified": "2023-04-04T20:11:55.371Z", "headers": [], "params": [], "auth": {"type": "bearer", "bearer": "{{token}}"}, "tests": []}, {"_id": "88e05de2-4622-43aa-a8b6-845e4f64cf33", "colId": "90e9227e-7a36-45f1-a8bf-55dc59313a5b", "containerId": "", "name": "Me", "url": "{{url}}/v1/me", "method": "GET", "sortNum": 30000, "created": "2023-04-04T20:12:15.527Z", "modified": "2023-04-04T20:13:30.832Z", "headers": [{"name": "accountSecureId", "value": "{{accountSecureId}}"}], "params": [], "auth": {"type": "bearer", "bearer": "{{token}}"}, "tests": []}, {"_id": "b68a4d7e-9c05-429a-9133-1b83f3a5cfd1", "colId": "9b339f86-f4b9-4ee2-a15a-6879f2842ff7", "containerId": "", "name": "SignUp", "url": "{{url}}/v1/public/sign-up", "method": "POST", "sortNum": 10000, "created": "2023-04-12T16:53:13.105Z", "modified": "2023-06-20T18:47:24.281Z", "headers": [], "params": [], "body": {"type": "formencoded", "raw": "", "form": [{"name": "email", "value": "<EMAIL>"}, {"name": "name", "value": "<PERSON>"}, {"name": "password", "value": "3z2io23m"}, {"name": "ddd_cell", "value": "14"}, {"name": "cell", "value": "*********"}]}, "auth": {"type": "bearer", "bearer": "{{token}}"}, "tests": [], "docs": "# Cria usuário do tipo paciente\n\nPara criaçao devem ser enviados os seguintes campos:\nAtributo | Tipo | Descrição\n:------ | :------: | :------\n```email``` <font color='#dd1e2e'>required</font>  | ```string``` | Email do usuário\n```name``` <font color='#dd1e2e'>required</font>  | ```string``` | Nome do usu<PERSON>rio\n```password``` <font color='#dd1e2e'>required</font>| ```string``` | Senha do usuário\n```ddd_cell``` <font color='#dd1e2e'>required</font>| ```number``` | DDD do Celular do usuário\n```cell``` <font color='#dd1e2e'>required</font>| ```number``` | Celular do usuário"}, {"_id": "75f7eb14-0f89-433e-bd48-260e4f900b3c", "colId": "ed0e5beb-4ea2-4dd6-b136-ec6ceeb4e975", "containerId": "f055abff-f1b1-4070-8793-5265e2064dfa", "name": "List", "url": "{{url}}/v1/admin/users", "method": "GET", "sortNum": 10000, "created": "2023-05-08T21:48:54.526Z", "modified": "2023-05-08T21:49:59.700Z", "headers": [], "params": [], "auth": {"type": "bearer", "bearer": "{{token}}"}, "tests": [], "docs": "# Lista de usuários Admin\n- Lista usuários do tipo admin\n\n## Query Parameters aceitos\n\n- **page**: Página. O padrão é 1\n- **limit**: Limite de dados por página. Padrão é 15\n- **search**: pesquisa por nome, email ou nome fantasia\n\n\n"}, {"_id": "32f22ce5-4930-4c1a-9353-bf5b9ab05cd2", "colId": "ed0e5beb-4ea2-4dd6-b136-ec6ceeb4e975", "containerId": "f055abff-f1b1-4070-8793-5265e2064dfa", "name": "Create", "url": "{{url}}/v1/admin/users", "method": "POST", "sortNum": 20000, "created": "2023-05-08T21:50:02.704Z", "modified": "2023-06-20T19:26:55.609Z", "headers": [], "params": [], "body": {"type": "json", "raw": "{\n  \"name\": \"UserAdmin\",\n  \"email\": \"<EMAIL>\",\n  \"password\": \"3z2io23m\",\n  \"legal_document_number\": \"295.585.562-00\"\n}", "form": []}, "auth": {"type": "bearer", "bearer": "{{token}}"}, "tests": [], "docs": "# Cadastro de Usuários admin\n- Cria usuário admin\n\nPara criação podem ser enviados os seguintes campos:\nAtributo | Tipo | Descrição\n:------ | :------: | :------\n```name``` <font color='#dd1e2e'>required</font>  | ```string``` | Nome do usuário\n```email``` <font color='#dd1e2e'>required</font>  | ```string``` | Email do usuário\n```password``` <font color='#dd1e2e'>required</font>  | ```string``` | Senha do usuário\n```phone``` <font color='#dd1e2e'>required</font>  | ```string``` | Telefone do usuário\n```birthDate``` <font color='#dd1e2e'>required</font>  | ```Date``` | Data de nascimento do usuário\n```cpf``` <font color='#dd1e2e'>required</font>  | ```string``` | CPF do usuário\n```avatarSecureId``` | ```string``` | SecureId do avatar do usuário"}, {"_id": "033b5815-5ec2-4a0b-b412-805b6710424d", "colId": "ed0e5beb-4ea2-4dd6-b136-ec6ceeb4e975", "containerId": "f055abff-f1b1-4070-8793-5265e2064dfa", "name": "Update", "url": "{{url}}/v1/admin/users/924626e8-7c89-432b-8758-2b51ec8335e0", "method": "PUT", "sortNum": 30000, "created": "2023-05-08T21:53:26.668Z", "modified": "2023-06-20T19:48:45.575Z", "headers": [], "params": [{"name": "name", "value": "User Admin", "isDisabled": true, "isPath": false}], "body": {"type": "json", "raw": "{\n  \"name\": \"User Admin\"\n}", "form": []}, "auth": {"type": "bearer", "bearer": "{{token}}"}, "tests": [], "docs": "# Atualização de Usuários admin\n- Atualiza usuário\n- Envie o secureId do usuário como parâmetro\n\nPara atualização podem ser enviados os seguintes campos:\nAtributo | Tipo | Descrição\n:------ | :------: | :------\n```name``` <font color='#dd1e2e'>required</font>  | ```string``` | Nome do usuário\n```email``` <font color='#dd1e2e'>required</font>  | ```string``` | Email do usuário\n```password```  | ```string``` | Senha do usuário\n```phone``` <font color='#dd1e2e'>required</font>  | ```string``` | Telefone do usuário\n```birthDate``` <font color='#dd1e2e'>required</font>  | ```Date``` | Data de nascimento do usuário\n```cpf``` <font color='#dd1e2e'>required</font>  | ```string``` | CPF do usuário\n```avatarSecureId``` | ```string``` | SecureId do avatar do usuário"}, {"_id": "0fbe9b37-dd64-4024-bc7f-16469a01367f", "colId": "ed0e5beb-4ea2-4dd6-b136-ec6ceeb4e975", "containerId": "f055abff-f1b1-4070-8793-5265e2064dfa", "name": "Delete", "url": "{{url}}/v1/admin/users/20170069-1700-4b69-941e-885176f32dbd", "method": "DELETE", "sortNum": 40000, "created": "2023-05-08T21:55:35.483Z", "modified": "2023-06-20T19:37:53.367Z", "headers": [], "params": [], "auth": {"type": "bearer", "bearer": "{{token}}"}, "tests": [], "docs": "# Deletar Usuário <PERSON>\n- Envie o secureId do usuário como parâmetro\n"}, {"_id": "09758973-204a-4928-a649-3132082499fc", "colId": "ed0e5beb-4ea2-4dd6-b136-ec6ceeb4e975", "containerId": "bad37225-0e6d-49f0-9602-1c5cf9d86669", "name": "List", "url": "{{url}}/v1/admin/partners", "method": "GET", "sortNum": 10000, "created": "2023-05-08T21:58:20.393Z", "modified": "2023-06-20T19:59:25.681Z", "headers": [], "params": [], "auth": {"type": "bearer", "bearer": "{{token}}"}, "tests": [], "docs": "# Lista de usuários Doctor\n- Lista usuários do tipo doctor\n\n## Query Parameters aceitos\n\n- **page**: Página. O padrão é 1\n- **limit**: Limite de dados por página. <PERSON>dr<PERSON> é 15\n- **search**: pesquisa por nome, email ou nome fantasia\n\n\n"}, {"_id": "bb73b84a-0eab-4df9-89a6-a83a1e65e021", "colId": "ed0e5beb-4ea2-4dd6-b136-ec6ceeb4e975", "containerId": "bad37225-0e6d-49f0-9602-1c5cf9d86669", "name": "Create", "url": "{{url}}/v1/admin/partners", "method": "POST", "sortNum": 20000, "created": "2023-05-08T21:58:20.394Z", "modified": "2023-06-20T21:04:46.548Z", "headers": [], "params": [], "body": {"type": "json", "raw": "{\n  \"name\": \"Doctor\",\n  \"email\": \"<EMAIL>\",\n  \"password\": \"3z2io23m\",\n \"legal_document_number\": \"295.585.562-01\",\n  \"birth_date\": \"1800-11-25\",\n  \"adviceRegister\": \"1\",\n  \"paymentMethods\": \"card\",\n  \"typeOfCare\": \"in_person\",\n  \"type\": \"doctor\"\n}", "form": []}, "auth": {"type": "bearer", "bearer": "{{token}}"}, "tests": [], "docs": "# Cadastro de Usuários Doctor\n- Cria usuário docctor\n\nPara criação podem ser enviados os seguintes campos:\nAtributo | Tipo | Descrição\n:------ | :------: | :------\n```name``` <font color='#dd1e2e'>required</font>  | ```string``` | Nome do usuário\n```email``` <font color='#dd1e2e'>required</font>  | ```string``` | Email do usuário\n```password``` <font color='#dd1e2e'>required</font>  | ```string``` | Senha do usuário\n```phone``` <font color='#dd1e2e'>required</font>  | ```string``` | Telefone do usuário\n```birthDate``` <font color='#dd1e2e'>required</font>  | ```Date``` | Data de nascimento do usuário\n```avatarSecureId``` | ```string``` | SecureId do avatar do usuário\n```adviceRegister``` <font color='#dd1e2e'>required</font> | ```string``` | adviceRegister do usuário\n```paymentMethods``` <font color='#dd1e2e'>required</font> | ```string``` | paymentMethods do usuário\n```typeOfCare``` <font color='#dd1e2e'>required</font> | ```enum``` | in_person, video_call, both\n```cpf``` <font color='#dd1e2e'>required</font>  | ```string``` | CPF do usuário\n```specialtiesSecureIds```  | ```string[]``` | secureIds de especialidades"}, {"_id": "e977c876-2a21-4634-a7ea-72d9ef4a793e", "colId": "ed0e5beb-4ea2-4dd6-b136-ec6ceeb4e975", "containerId": "bad37225-0e6d-49f0-9602-1c5cf9d86669", "name": "Update", "url": "{{url}}/v1/admin/partners/123", "method": "PUT", "sortNum": 30000, "created": "2023-05-08T21:58:20.395Z", "modified": "2023-06-20T19:59:47.943Z", "headers": [], "params": [], "auth": {"type": "bearer", "bearer": "{{token}}"}, "tests": [], "docs": "# Atualização de Usuários Doctor\n- Atualiza usuário\n- Envie o secureId do usuário como parâmetro\n\nPara atualização podem ser enviados os seguintes campos:\nAtributo | Tipo | Descrição\n:------ | :------: | :------\n```name``` <font color='#dd1e2e'>required</font>  | ```string``` | Nome do usuário\n```email``` <font color='#dd1e2e'>required</font>  | ```string``` | Email do usuário\n```password```  | ```string``` | Senha do usuário\n```phone``` <font color='#dd1e2e'>required</font>  | ```string``` | Telefone do usuário\n```birthDate``` <font color='#dd1e2e'>required</font>  | ```Date``` | Data de nascimento do usuário\n```avatarSecureId``` | ```string``` | SecureId do avatar do usuário\n```adviceRegister``` <font color='#dd1e2e'>required</font> | ```string``` | adviceRegister do usuário\n```paymentMethods``` <font color='#dd1e2e'>required</font> | ```string``` | paymentMethods do usuário\n```typeOfCare``` <font color='#dd1e2e'>required</font> | ```enum``` | in_person, video_call, both\n```cpf``` <font color='#dd1e2e'>required</font>  | ```string``` | CPF do usuário\n```specialtiesSecureIds```  | ```string[]``` | secureIds de especialidades"}, {"_id": "027cca1c-a5dd-477d-b041-d54f60e3481c", "colId": "ed0e5beb-4ea2-4dd6-b136-ec6ceeb4e975", "containerId": "bad37225-0e6d-49f0-9602-1c5cf9d86669", "name": "Delete", "url": "{{url}}/v1/admin/partners/123", "method": "DELETE", "sortNum": 40000, "created": "2023-05-08T21:58:20.396Z", "modified": "2023-06-20T19:59:53.318Z", "headers": [], "params": [], "auth": {"type": "bearer", "bearer": "{{token}}"}, "tests": [], "docs": "# Deletar Usuário Doctor\n- Envie o secureId do usuário como parâmetro\n"}, {"_id": "abd94748-3cb0-469e-a08c-36ca76dd0511", "colId": "ed0e5beb-4ea2-4dd6-b136-ec6ceeb4e975", "containerId": "fee058e0-7616-4605-a331-96b478f6da99", "name": "List", "url": "{{url}}/v1/admin/patients", "method": "GET", "sortNum": 10000, "created": "2023-05-08T22:03:00.677Z", "modified": "2023-05-08T22:03:22.168Z", "headers": [], "params": [], "auth": {"type": "bearer", "bearer": "{{token}}"}, "tests": [], "docs": "# Lista de usuários Patient\n- Lista usuários do tipo patient\n\n## Query Parameters aceitos\n\n- **page**: Página. O padrão é 1\n- **limit**: Limite de dados por página. Padr<PERSON> é 15\n- **search**: pesquisa por nome, email ou nome fantasia\n\n\n"}, {"_id": "a82cb2f2-66e6-4b89-9374-c8fb44a3a6f6", "colId": "ed0e5beb-4ea2-4dd6-b136-ec6ceeb4e975", "containerId": "fee058e0-7616-4605-a331-96b478f6da99", "name": "Create", "url": "{{url}}/v1/admin/patients", "method": "POST", "sortNum": 20000, "created": "2023-05-08T22:03:00.678Z", "modified": "2023-05-18T21:39:29.532Z", "headers": [], "params": [], "body": {"type": "json", "raw": "{\n  \"name\": \"Patient\",\n  \"email\": \"<EMAIL>\",\n  \"password\": \"pedro\",\n  \"phone\": \"9\",\n  \"birthDate\": \"1800-11-25\",\n  \"cpf\": \"4\"\n}", "form": []}, "auth": {"type": "bearer", "bearer": "{{token}}"}, "tests": [], "docs": "# Cadastro de Usuários Doctor\n- Cria usuário docctor\n\nPara criação podem ser enviados os seguintes campos:\nAtributo | Tipo | Descrição\n:------ | :------: | :------\n```name``` <font color='#dd1e2e'>required</font>  | ```string``` | Nome do usuário\n```email``` <font color='#dd1e2e'>required</font>  | ```string``` | Email do usuário\n```password``` <font color='#dd1e2e'>required</font>  | ```string``` | Senha do usuário\n```phone``` <font color='#dd1e2e'>required</font>  | ```string``` | Telefone do usuário\n```birthDate``` <font color='#dd1e2e'>required</font>  | ```Date``` | Data de nascimento do usuário\n```cpf``` <font color='#dd1e2e'>required</font>  | ```string``` | CPF do usuário\n```avatarSecureId``` | ```string``` | SecureId do avatar do usuário"}, {"_id": "521ed5f9-43d5-43bb-a2db-69de81023a8b", "colId": "ed0e5beb-4ea2-4dd6-b136-ec6ceeb4e975", "containerId": "fee058e0-7616-4605-a331-96b478f6da99", "name": "Update", "url": "{{url}}/v1/admin/patients/123", "method": "PUT", "sortNum": 30000, "created": "2023-05-08T22:03:00.679Z", "modified": "2023-05-18T21:18:57.382Z", "headers": [], "params": [], "auth": {"type": "bearer", "bearer": "{{token}}"}, "tests": [], "docs": "# Atualização de Usuários Patient\n- Atualiza usuário\n- Envie o secureId do usuário como parâmetro\n\nPara atualização podem ser enviados os seguintes campos:\nAtributo | Tipo | Descrição\n:------ | :------: | :------\n```name``` <font color='#dd1e2e'>required</font>  | ```string``` | Nome do usuário\n```email``` <font color='#dd1e2e'>required</font>  | ```string``` | Email do usuário\n```password```  | ```string``` | Senha do usuário\n```phone``` <font color='#dd1e2e'>required</font>  | ```string``` | Telefone do usuário\n```birthDate``` <font color='#dd1e2e'>required</font>  | ```Date``` | Data de nascimento do usuário\n```cpf``` <font color='#dd1e2e'>required</font>  | ```string``` | CPF do usuário\n```avatarSecureId``` | ```string``` | SecureId do avatar do usuário"}, {"_id": "67881c5f-9380-4c56-aa23-d2da1b7bb30d", "colId": "ed0e5beb-4ea2-4dd6-b136-ec6ceeb4e975", "containerId": "fee058e0-7616-4605-a331-96b478f6da99", "name": "Delete", "url": "{{url}}/v1/admin/patients/123", "method": "DELETE", "sortNum": 40000, "created": "2023-05-08T22:03:00.680Z", "modified": "2023-05-08T22:04:03.135Z", "headers": [], "params": [], "auth": {"type": "bearer", "bearer": "{{token}}"}, "tests": [], "docs": "# Deletar Usuário <PERSON>\n- Envie o secureId do usuário como parâmetro\n"}, {"_id": "e5552215-1e71-4594-8ab1-fe3f94cfc540", "colId": "ed0e5beb-4ea2-4dd6-b136-ec6ceeb4e975", "containerId": "9aa564da-8c0b-4f95-bde2-c0785d3ccba7", "name": "List", "url": "{{url}}/v1/admin/dependents?parent=43f49e9b-8e48-45da-aeb8-292d14600fd1", "method": "GET", "sortNum": 10000, "created": "2023-05-08T22:04:10.956Z", "modified": "2023-06-22T18:58:21.071Z", "headers": [], "params": [{"name": "parent", "value": "43f49e9b-8e48-45da-aeb8-292d14600fd1", "isPath": false}], "auth": {"type": "bearer", "bearer": "{{token}}"}, "tests": [], "docs": "# Lista de usuários Dependent\n- Lista usuários do tipo Dependent\n\n## Query Parameters aceitos\n\n- **page**: Página. O padrão é 1\n- **limit**: Limite de dados por página. Padrão é 15\n- **search**: pesquisa por nome, email ou nome fantasia\n\n\n"}, {"_id": "c2608c09-3cfb-4ff1-b4ea-c644afcb7d54", "colId": "ed0e5beb-4ea2-4dd6-b136-ec6ceeb4e975", "containerId": "9aa564da-8c0b-4f95-bde2-c0785d3ccba7", "name": "Create", "url": "{{url}}/v1/patients/dependents", "method": "POST", "sortNum": 20000, "created": "2023-05-08T22:04:10.957Z", "modified": "2023-05-25T18:02:53.664Z", "headers": [], "params": [], "auth": {"type": "bearer", "bearer": "{{token}}"}, "tests": [], "docs": "# Cadastro de Usuários Dependent\n- Cria usuário Dependent\n\nPara criação podem ser enviados os seguintes campos:\nAtributo | Tipo | Descrição\n:------ | :------: | :------\n```name``` <font color='#dd1e2e'>required</font>  | ```string``` | Nome do usuário\n```email``` <font color='#dd1e2e'>required</font>  | ```string``` | Email do usuário\n```password``` <font color='#dd1e2e'>required</font>  | ```string``` | Senha do usuário\n```phone``` <font color='#dd1e2e'>required</font>  | ```string``` | Telefone do usuário\n```birthDate``` <font color='#dd1e2e'>required</font>  | ```Date``` | Data de nascimento do usuário\n```cpf``` <font color='#dd1e2e'>required</font>  | ```string``` | CPF do usuário\n```avatarSecureId``` | ```string``` | SecureId do avatar do usuário\n```parentSecureId``` <font color='#dd1e2e'>required</font> | ```string``` | SecureId do parente do usuário"}, {"_id": "92008a13-e7d9-4f1a-b342-f22e7c604a45", "colId": "ed0e5beb-4ea2-4dd6-b136-ec6ceeb4e975", "containerId": "9aa564da-8c0b-4f95-bde2-c0785d3ccba7", "name": "Update", "url": "{{url}}/v1/admin/dependents/123", "method": "PUT", "sortNum": 30000, "created": "2023-05-08T22:04:10.958Z", "modified": "2023-05-25T18:03:04.733Z", "headers": [], "params": [], "auth": {"type": "bearer", "bearer": "{{token}}"}, "tests": [], "docs": "# Atualização de Usuários Dependent\n- Atualiza usuário\n- Envie o secureId do usuário como parâmetro\n\nPara atualização podem ser enviados os seguintes campos:\nAtributo | Tipo | Descrição\n:------ | :------: | :------\n```name``` <font color='#dd1e2e'>required</font>  | ```string``` | Nome do usuário\n```email``` <font color='#dd1e2e'>required</font>  | ```string``` | Email do usuário\n```password```  | ```string``` | Senha do usuário\n```phone``` <font color='#dd1e2e'>required</font>  | ```string``` | Telefone do usuário\n```birthDate``` <font color='#dd1e2e'>required</font>  | ```Date``` | Data de nascimento do usuário\n```cpf``` <font color='#dd1e2e'>required</font>  | ```string``` | CPF do usuário\n```avatarSecureId``` | ```string``` | SecureId do avatar do usuário\n```parentSecureId``` <font color='#dd1e2e'>required</font> | ```string``` | SecureId do parente do usuário"}, {"_id": "496eddcb-412e-4724-9f2a-9d47b2432fbb", "colId": "ed0e5beb-4ea2-4dd6-b136-ec6ceeb4e975", "containerId": "9aa564da-8c0b-4f95-bde2-c0785d3ccba7", "name": "Delete", "url": "{{url}}/v1/admin/dependents/123", "method": "DELETE", "sortNum": 40000, "created": "2023-05-08T22:04:10.960Z", "modified": "2023-05-25T18:03:18.206Z", "headers": [], "params": [], "auth": {"type": "bearer", "bearer": "{{token}}"}, "tests": [], "docs": "# Deletar Usuário <PERSON>dent\n- Envie o secureId do usuário como parâmetro\n"}, {"_id": "094e69ea-48ad-4f6d-94a0-d0d9dae5754d", "colId": "ed0e5beb-4ea2-4dd6-b136-ec6ceeb4e975", "containerId": "04e71d2d-364b-4831-b660-d92c5526c386", "name": "List", "url": "{{url}}/v1/admin/specialties", "method": "GET", "sortNum": 10000, "created": "2023-05-08T22:10:43.845Z", "modified": "2023-05-08T22:11:42.748Z", "headers": [], "params": [], "auth": {"type": "bearer", "bearer": "{{token}}"}, "tests": [], "docs": "# Lista de especialidades\n- Lista usuários do tipo admin\n\n## Query Parameters aceitos\n\n- **page**: Página. O padrão é 1\n- **limit**: Limite de dados por página. Padrão é 15\n- **search**: pesquisa por nome, email ou nome fantasia\n\n\n"}, {"_id": "68d91e6b-7f3e-4465-a82c-b2b8285253ef", "colId": "ed0e5beb-4ea2-4dd6-b136-ec6ceeb4e975", "containerId": "04e71d2d-364b-4831-b660-d92c5526c386", "name": "Create", "url": "{{url}}/v1/admin/specialties", "method": "POST", "sortNum": 20000, "created": "2023-05-08T22:10:43.846Z", "modified": "2023-05-08T22:12:40.542Z", "headers": [], "params": [], "auth": {"type": "bearer", "bearer": "{{token}}"}, "tests": [], "docs": "# Cadastro de especialidades\n\nPara criação podem ser enviados os seguintes campos:\nAtributo | Tipo | Descrição\n:------ | :------: | :------\n```name``` <font color='#dd1e2e'>required</font>  | ```string``` | Nome\n```labelAdvice``` <font color='#dd1e2e'>required</font>  | ```string``` | labelAdvice\n```thumbSecureId``` <font color='#dd1e2e'>required</font> | ```string``` | Thumb"}, {"_id": "c4a41a7a-4dad-403a-b610-d025a2988b30", "colId": "ed0e5beb-4ea2-4dd6-b136-ec6ceeb4e975", "containerId": "04e71d2d-364b-4831-b660-d92c5526c386", "name": "Update", "url": "{{url}}/v1/admin/specialties/123", "method": "PUT", "sortNum": 30000, "created": "2023-05-08T22:10:43.847Z", "modified": "2023-05-08T22:13:17.393Z", "headers": [], "params": [], "auth": {"type": "bearer", "bearer": "{{token}}"}, "tests": [], "docs": "# Atualização de especialidades\n- Envie o secureId da especialidade como parâmetro\n\nPara atualização podem ser enviados os seguintes campos:\nAtributo | Tipo | Descrição\n:------ | :------: | :------\n```name``` <font color='#dd1e2e'>required</font>  | ```string``` | Nome\n```labelAdvice``` <font color='#dd1e2e'>required</font>  | ```string``` | labelAdvice\n```thumbSecureId``` <font color='#dd1e2e'>required</font> | ```string``` | Thumb"}, {"_id": "ed09a5a3-407b-4745-ad14-830ce7e83210", "colId": "ed0e5beb-4ea2-4dd6-b136-ec6ceeb4e975", "containerId": "04e71d2d-364b-4831-b660-d92c5526c386", "name": "Delete", "url": "{{url}}/v1/admin/specialties/123", "method": "DELETE", "sortNum": 40000, "created": "2023-05-08T22:10:43.848Z", "modified": "2023-05-08T22:13:33.750Z", "headers": [], "params": [], "auth": {"type": "bearer", "bearer": "{{token}}"}, "tests": [], "docs": "# Deletar especialidade\n- Envie o secureId da especialidade como parâmetro\n"}, {"_id": "c11bc920-f106-4af2-8c0c-90a541274c6f", "colId": "5ec0110f-0e9f-4b56-a2fa-678f9b12cb83", "containerId": "b9ea7a77-832e-437e-b2ad-b90451ffb5af", "name": "List", "url": "{{url}}/v1/patient/dependents", "method": "GET", "sortNum": 10000, "created": "2023-05-08T22:14:10.083Z", "modified": "2023-05-25T18:01:09.426Z", "headers": [], "params": [], "auth": {"type": "bearer", "bearer": "{{token}}"}, "tests": [], "docs": "# Lista de usuários dependents\n- Lista usuários do tipo dependent do usuário logado\n\n## Query Parameters aceitos\n\n- **page**: Página. O padrão é 1\n- **limit**: Limite de dados por página. Padrão é 15\n- **search**: pesquisa por nome, email ou nome fantasia\n\n\n"}, {"_id": "40c916d5-663c-4f2f-81fa-10056cdb9609", "colId": "5ec0110f-0e9f-4b56-a2fa-678f9b12cb83", "containerId": "b9ea7a77-832e-437e-b2ad-b90451ffb5af", "name": "Create", "url": "{{url}}/v1/patient/dependents", "method": "POST", "sortNum": 20000, "created": "2023-05-08T22:14:10.084Z", "modified": "2023-05-25T18:01:41.145Z", "headers": [], "params": [], "auth": {"type": "bearer", "bearer": "{{token}}"}, "tests": [], "docs": "# Cadastro de Usuários Dependents\n- Cria usuário Dependent relacionado ao usuário logado\n\nPara criação podem ser enviados os seguintes campos:\nAtributo | Tipo | Descrição\n:------ | :------: | :------\n```name``` <font color='#dd1e2e'>required</font>  | ```string``` | Nome do usuário\n```email``` <font color='#dd1e2e'>required</font>  | ```string``` | Email do usuário\n```password``` <font color='#dd1e2e'>required</font>  | ```string``` | Senha do usuário\n```phone``` <font color='#dd1e2e'>required</font>  | ```string``` | Telefone do usuário\n```birthDate``` <font color='#dd1e2e'>required</font>  | ```Date``` | Data de nascimento do usuário\n```cpf``` <font color='#dd1e2e'>required</font>  | ```string``` | CPF do usuário\n```avatarSecureId``` | ```string``` | SecureId do avatar do usuário\n```parentSecureId``` <font color='#dd1e2e'>required</font> | ```string``` | SecureId do parente do usuário"}, {"_id": "c4597671-3082-413c-9f65-4095d28694f8", "colId": "5ec0110f-0e9f-4b56-a2fa-678f9b12cb83", "containerId": "b9ea7a77-832e-437e-b2ad-b90451ffb5af", "name": "Update", "url": "{{url}}/v1/patient/dependents/123", "method": "PUT", "sortNum": 30000, "created": "2023-05-08T22:14:10.085Z", "modified": "2023-05-25T18:01:55.361Z", "headers": [], "params": [], "auth": {"type": "bearer", "bearer": "{{token}}"}, "tests": [], "docs": "# Atualização de Usuários Dependent\n- Atualiza usuário\n- Envie o secureId do usuário como parâmetro\n\nPara atualização podem ser enviados os seguintes campos:\nAtributo | Tipo | Descrição\n:------ | :------: | :------\n```name``` <font color='#dd1e2e'>required</font>  | ```string``` | Nome do usuário\n```email``` <font color='#dd1e2e'>required</font>  | ```string``` | Email do usuário\n```password```  | ```string``` | Senha do usuário\n```phone``` <font color='#dd1e2e'>required</font>  | ```string``` | Telefone do usuário\n```birthDate``` <font color='#dd1e2e'>required</font>  | ```Date``` | Data de nascimento do usuário\n```cpf``` <font color='#dd1e2e'>required</font>  | ```string``` | CPF do usuário\n```avatarSecureId``` | ```string``` | SecureId do avatar do usuário"}, {"_id": "787d28de-91df-4041-8e34-6467ed3e96bf", "colId": "5ec0110f-0e9f-4b56-a2fa-678f9b12cb83", "containerId": "b9ea7a77-832e-437e-b2ad-b90451ffb5af", "name": "Delete", "url": "{{url}}/v1/patient/dependents/123", "method": "DELETE", "sortNum": 40000, "created": "2023-05-08T22:14:10.086Z", "modified": "2023-05-25T18:02:11.552Z", "headers": [], "params": [], "auth": {"type": "bearer", "bearer": "{{token}}"}, "tests": [], "docs": "# Deletar Usuário <PERSON>dent\n- Envie o secureId do usuário como parâmetro\n"}, {"_id": "1b33b7d5-55c6-4d3f-a490-dc605de05f99", "colId": "78492f2c-8c7b-40a9-a329-204fe6fef852", "containerId": "23680139-650b-48db-b50a-4ae23f80b051", "name": "List", "url": "{{url}}/v1/uploads", "method": "GET", "sortNum": 10000, "created": "2023-05-08T22:19:04.980Z", "modified": "2023-05-08T22:20:40.997Z", "headers": [], "params": [], "auth": {"type": "bearer", "bearer": "{{token}}"}, "tests": [], "docs": "# Lista de uploads\n- <PERSON><PERSON> uplodas, se usu<PERSON>rio não for admin só irão aparecer os uploads do usuário logado\n\n## Query Parameters aceitos\n\n- **page**: Página. O padrão é 1\n- **limit**: Limite de dados por página. Padrão é 15\n- **search**: pesquisa por nome, email ou nome fantasia\n- **type**: image ou pdf\n\n\n"}, {"_id": "917a2178-4391-4b63-9a6c-7ea04474f54b", "colId": "78492f2c-8c7b-40a9-a329-204fe6fef852", "containerId": "23680139-650b-48db-b50a-4ae23f80b051", "name": "Create", "url": "{{url}}/v1/uploads", "method": "POST", "sortNum": 20000, "created": "2023-05-08T22:19:04.981Z", "modified": "2023-05-08T22:21:37.716Z", "headers": [], "params": [], "auth": {"type": "bearer", "bearer": "{{token}}"}, "tests": [], "docs": "# Salva upload\n\nPara criação podem ser enviados os seguintes campos:\nAtributo | Tipo | Descrição\n:------ | :------: | :------\n```file``` <font color='#dd1e2e'>required</font>  | ```File``` | Arquivo\n```name``` <font color='#dd1e2e'>required</font>  | ```string``` | Nome do upload"}, {"_id": "bc44fa3e-18fc-4c2f-8170-2d1dd303399a", "colId": "ed0e5beb-4ea2-4dd6-b136-ec6ceeb4e975", "containerId": "f055abff-f1b1-4070-8793-5265e2064dfa", "name": "Show", "url": "{{url}}/v1/admin/users/dbf55452-8bfc-4eba-8c95-b84fb6e210d7", "method": "GET", "sortNum": 15000, "created": "2023-05-08T22:22:45.823Z", "modified": "2023-06-23T14:22:52.766Z", "headers": [], "params": [], "auth": {"type": "bearer", "bearer": "{{token}}"}, "tests": [], "docs": "# Visualizar usuário admin\n- Envie o secureId como parâmetro\n"}, {"_id": "1e66fffc-c691-4246-857f-7ec53273ef58", "colId": "ed0e5beb-4ea2-4dd6-b136-ec6ceeb4e975", "containerId": "bad37225-0e6d-49f0-9602-1c5cf9d86669", "name": "Show", "url": "{{url}}/v1/admin/partners/4ec6b457-d24b-493d-bd0f-e63ed0c01136", "method": "GET", "sortNum": 15000, "created": "2023-05-08T22:23:45.434Z", "modified": "2023-06-23T14:19:33.574Z", "headers": [], "params": [], "auth": {"type": "bearer", "bearer": "{{token}}"}, "tests": [], "docs": "# Visualizar usuário doctor\n- <PERSON><PERSON> o secureId como parâmetro\n"}, {"_id": "96975da7-608d-4798-899e-bdd6e693cbc7", "colId": "ed0e5beb-4ea2-4dd6-b136-ec6ceeb4e975", "containerId": "fee058e0-7616-4605-a331-96b478f6da99", "name": "Show", "url": "{{url}}/v1/admin/patients/123", "method": "GET", "sortNum": 15000, "created": "2023-05-08T22:24:26.209Z", "modified": "2023-05-08T22:24:46.084Z", "headers": [], "params": [], "auth": {"type": "bearer", "bearer": "{{token}}"}, "tests": [], "docs": "# Visualizar usuário patient\n- Envie o secureId como parâmetro\n"}, {"_id": "8f22ced9-2ae4-423f-b47d-a580b3a5fe30", "colId": "ed0e5beb-4ea2-4dd6-b136-ec6ceeb4e975", "containerId": "9aa564da-8c0b-4f95-bde2-c0785d3ccba7", "name": "Show", "url": "{{url}}/v1/admin/dependents/43f49e9b-8e48-45da-aeb8-292d146sdfdsfd1", "method": "GET", "sortNum": 15000, "created": "2023-05-08T22:24:49.939Z", "modified": "2023-06-22T19:08:52.258Z", "headers": [], "params": [], "auth": {"type": "bearer", "bearer": "{{token}}"}, "tests": [], "docs": "# Visualizar usuá<PERSON>\n- Envie o secureId como parâmetro\n"}, {"_id": "03b9f030-54e6-41d6-be74-ea49c1540282", "colId": "ed0e5beb-4ea2-4dd6-b136-ec6ceeb4e975", "containerId": "04e71d2d-364b-4831-b660-d92c5526c386", "name": "Show", "url": "{{url}}/v1/admin/specialties/123", "method": "GET", "sortNum": 15000, "created": "2023-05-08T22:25:22.815Z", "modified": "2023-05-08T22:25:45.916Z", "headers": [], "params": [], "auth": {"type": "bearer", "bearer": "{{token}}"}, "tests": [], "docs": "# Visualizar especialidade\n- Envie o secureId como parâmetro\n"}, {"_id": "6359a2d2-100a-4a17-a26f-6c65b0e25bda", "colId": "5ec0110f-0e9f-4b56-a2fa-678f9b12cb83", "containerId": "b9ea7a77-832e-437e-b2ad-b90451ffb5af", "name": "Show", "url": "{{url}}/v1/admin/dependents/123", "method": "GET", "sortNum": 15000, "created": "2023-05-08T22:25:53.196Z", "modified": "2023-05-25T18:01:20.599Z", "headers": [], "params": [], "auth": {"type": "bearer", "bearer": "{{token}}"}, "tests": [], "docs": "# Visualizar usuário\n- Envie o secureId como parâmetro\n"}, {"_id": "9f92bd9e-4ff7-4321-93b8-b4372ff9b4c9", "colId": "78492f2c-8c7b-40a9-a329-204fe6fef852", "containerId": "23680139-650b-48db-b50a-4ae23f80b051", "name": "Show", "url": "{{url}}/v1/uploads/123", "method": "GET", "sortNum": 15000, "created": "2023-05-08T22:26:23.389Z", "modified": "2023-05-08T22:26:51.038Z", "headers": [], "params": [], "auth": {"type": "bearer", "bearer": "{{token}}"}, "tests": [], "docs": "# Visualizar upload\n- Envie o secureId como parâmetro\n"}, {"_id": "9aaa4c16-c450-4e01-be17-0b1d18fae288", "colId": "5ec0110f-0e9f-4b56-a2fa-678f9b12cb83", "containerId": "40514497-e338-4e5b-9bc4-dccbd1d3b20d", "name": "List", "url": "{{url}}/v1/patient/notifications", "method": "GET", "sortNum": 10000, "created": "2023-05-17T20:36:59.091Z", "modified": "2023-05-17T20:38:35.848Z", "headers": [], "params": [], "auth": {"type": "bearer", "bearer": "{{token}}"}, "tests": [], "docs": "# Lista notificações do usuário\n- Lista notificações do usuário logado\n\n## Query Parameters aceitos\n\n- **page**: Página. O padrão é 1\n- **limit**: Limite de dados por página. Padrão é 15\n- **search**: pesquisa por nome, email ou nome fantasia\n- **status**: Os status das mensagens podem ser: 'read' ou 'not_read'\n\n\n"}, {"_id": "998a2568-8f3d-4ece-a855-07662d9892fe", "colId": "5ec0110f-0e9f-4b56-a2fa-678f9b12cb83", "containerId": "40514497-e338-4e5b-9bc4-dccbd1d3b20d", "name": "Show", "url": "{{url}}/v1/admin/notifications/123", "method": "GET", "sortNum": 15000, "created": "2023-05-17T20:36:59.092Z", "modified": "2023-05-17T20:39:01.653Z", "headers": [], "params": [], "auth": {"type": "bearer", "bearer": "{{token}}"}, "tests": [], "docs": "# Visualizar notificação\n- Envie o secureId como parâmetro\n"}, {"_id": "15d72f96-b869-4dd5-acb1-d20164ecc81f", "colId": "5ec0110f-0e9f-4b56-a2fa-678f9b12cb83", "containerId": "40514497-e338-4e5b-9bc4-dccbd1d3b20d", "name": "Update", "url": "{{url}}/v1/patient/notifications/123", "method": "PUT", "sortNum": 30000, "created": "2023-05-17T20:36:59.094Z", "modified": "2023-05-17T20:40:28.813Z", "headers": [], "params": [], "auth": {"type": "bearer", "bearer": "{{token}}"}, "tests": [], "docs": "# Marcar notificação como lida\n- Atualiza notificação\n- Envie o secureId como parâmetro"}, {"_id": "1e946736-d582-4553-a2ef-ee311ab72cb6", "colId": "90e9227e-7a36-45f1-a8bf-55dc59313a5b", "containerId": "", "name": "ForgotPassword", "url": "{{url}}/v1/recovery_password", "method": "POST", "sortNum": 40000, "created": "2023-06-21T21:27:36.591Z", "modified": "2023-06-21T21:37:13.128Z", "headers": [], "params": [], "body": {"type": "formencoded", "raw": "", "form": [{"name": "email", "value": "{{email}}", "isDisabled": true}, {"name": "redirectUrl", "value": "https://hellomed.com.br/forgotpassord"}, {"name": "email", "value": "<EMAIL>"}]}, "auth": {"type": "bearer", "bearer": "{{token}}"}, "tests": [{"type": "set-env-var", "custom": "json.token.token", "action": "setto", "value": "{{token,local}}"}], "docs": "# Autenticação na plataforma\n\nPara autenticação devem ser enviados os seguintes campos:\nAtributo | Tipo | Descrição\n:------ | :------: | :------\n```email``` <font color='#dd1e2e'>required</font>  | ```string``` | Emaild do usuário\n```password``` <font color='#dd1e2e'>required</font>| ```string``` | Senha do usuário"}]