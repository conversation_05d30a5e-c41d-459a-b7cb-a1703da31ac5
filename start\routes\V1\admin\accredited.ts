import Route from '@ioc:Adonis/Core/Route'
import { isPermissions } from 'App/Contants/Permissions'
import { isRoles } from 'App/Contants/Roles'

Route.group(() => {

	Route.resource('accrediteds', 'V1/Admin/Accredited/MainController').middleware({
		index: ['auth', `${isRoles(['MASTER', 'ADMIN'])}`, isPermissions(['accrediteds_view'])],
		store: ['auth', `${isRoles(['MASTER', 'ADMIN'])}`, isPermissions(['accrediteds_create'])],
		update: ['auth', `${isRoles(['MASTER', 'ADMIN'])}`, isPermissions(['accrediteds_edit'])],
		destroy: ['auth', `${isRoles(['MASTER', 'ADMIN'])}`, isPermissions(['accrediteds_delete'])],
	})

	Route.resource('accrediteds/:secure_id/doctors', 'V1/Admin/Accredited/ListDoctorsController').middleware({
		index: ['auth', `${isRoles(['MASTER', 'ADMIN'])}`, isPermissions(['accrediteds_view'])],
		store: ['auth', `${isRoles(['MASTER', 'ADMIN'])}`, isPermissions(['accrediteds_create'])],
		destroy: ['auth', `${isRoles(['MASTER', 'ADMIN'])}`, isPermissions(['accrediteds_delete'])],
	})

	Route.put('visibility-accrediteds', 'V1/Admin/Accredited/ChangeVisibilityController.update').middleware(
		['auth', `${isRoles(['MASTER', 'ADMIN'])}`, isPermissions(['accrediteds_edit'])]
	)

	Route.resource('import-accrediteds', 'V1/Admin/Accredited/ImportController').middleware({
		store: ['auth', `${isRoles(['MASTER', 'ADMIN'])}`, isPermissions(['accrediteds_create'])],
	})
})
