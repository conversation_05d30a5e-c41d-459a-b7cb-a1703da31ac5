import BaseSchema from '@ioc:Adonis/Lucid/Schema'

export default class extends BaseSchema {
	protected tableName = 'user_exams'

	public async up() {
		this.schema.createTable(this.tableName, (table) => {
			table.increments('id')
			table
				.integer('user_id')
				.unsigned()
				.references('id')
				.inTable('users')
				.onUpdate('CASCADE')
				.onDelete('CASCADE')
			table
				.integer('exam_id')
				.unsigned()
				.references('id')
				.inTable('exams')
				.onUpdate('CASCADE')
				.onDelete('CASCADE')
			table.dateTime('created_at', { useTz: true })
			table.dateTime('updated_at', { useTz: true })
		})
	}

	public async down() {
		this.schema.dropTable(this.tableName)
	}
}
