# Documentação do Evento OneSignal Notificações Push (AdonisJs)
1. [Introdução](#introdução) 📖
2. [Emitindo o Evento](#emitindo-o-evento) 🚀
3. [Envio de Notificações Push para Múltiplos Usuários com Corpos Diferentes](#envio-de-notificações-push-para-múltiplos-usuários-com-corpos-diferentes) ✉️
4. [Notificação para Vários Usuários](#notificação-para-vários-usuários) 🔔

[Voltar](index)

## Introdução

Este documento descreve o evento "new:oneSignalNotification" e os parâmetros necessários para emitir esse evento usando o AdonisJs. O evento "new:oneSignalNotification" é utilizado para enviar notificações "push" usando o serviço OneSignal. Este evento permite que você envie notificações push com diferentes títulos, corpos e destinatários.

## Emitindo o Evento

Para emitir o evento "new:oneSignalNotification", você deve utilizar o seguinte código:

```javascript
Event.emit('new:oneSignalNotification', {
    title: data.title,
    content: data.content,
    ids: data.ids,
    date?: data.date
})
```
Onde:

- title (string): Recebe o título da notificação.
- content (string): Recebe o corpo da notificação.
- ids (array): Recebe um ou muitos ids de usuários para envio da notificação.
- date (DateTime): Recebe uma data para agendar o envio (campo opcional)

## Envio de Notificações Push para Múltiplos Usuários com Corpos Diferentes

Se você deseja enviar notificações para vários destinatários com corpos diferentes, é necessário usar um loop e emitir o evento dentro desse loop. Cada iteração do loop deve incluir um objeto com os parâmetros apropriados (título, corpo e id) e emitir o evento "new:oneSignalNotification" separadamente para cada destinatário.

Aqui está um exemplo de como fazer isso em um loop:

```javascript
for (const recipient of recipients) {
    Event.emit('new:oneSignalNotification', {
        title: recipient.title,
        content: recipient.content,
        ids: [recipient.ids]
    })
}
```

Onde recipients é um array que contém objetos com os campos title, content, e ids para cada destinatário.

## Notificação para Vários Usuários

Se você deseja enviar a mesma notificação para vários usuários, basta passar todos os ids separados por virgula no array ids. O evento "new:oneSignalNotification" cuidará de enviar a mesma notificação para todos os destinatários listados no array.

```javascript
Event.emit('new:oneSignalNotification', {
    title: data.title,
    content: data.content,
    ids: ['95a870a6-98db...', '4f8f88d7-71af...', 'e2f3b5e9-6a74...']
})
```

Isso enviará a mesma notificação para os três ids listados no array.

Certifique-se de que os parâmetros estejam configurados corretamente de acordo com suas necessidades para garantir o envio correto das notificações push usando o OneSignal.
