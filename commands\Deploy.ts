import { BaseCommand } from '@adonisjs/core/build/standalone'
import Database from '@ioc:Adonis/Lucid/Database'

export default class Deploy extends BaseCommand {
	public static commandName = 'deploy'

	public static title = 'Sincronizar dados'
	public static description =
		'Sincroniza os dados da aplicação como as roles, permissões e templates de email.'

	public static settings = {
		loadApp: true,
		stayAlive: false,
	}

	public async run() {
		try {
			await this.kernel.exec('migration:run', [])
			await this.kernel.exec('create:permissions', ['--no-confirmation'])
			await this.kernel.exec('create:roles', ['--no-confirmation'])
			await this.kernel.exec('create:templatenotifications', ['--no-confirmation'])
			await Database.manager.closeAll()
		} catch (err) {
			this.logger.error(err)
		}
	}
}
