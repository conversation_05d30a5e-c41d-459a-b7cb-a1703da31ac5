<!DOCTYPE html>
<html>
<head>
  <title>Documentação Features</title>
  <style>
    body {
      margin: 0 auto;
      color: #c9d1d9;
      background-color: #0d1117;
      width: 50%;
      align-items: center;
      text-align: center;
      font-family: "Segoe UI";
      font-size: 20px;
    }
    a {
      text-decoration: none;
      color: #58a6ff;
      background-color: transparent;
    }
    ol,ul {
      text-align: center;
      list-style-position: inside;
    }
    .markdown-body .highlight pre, .markdown-body pre{
      display: flex;
      text-align: left;
      justify-content: center;
      padding: 16px;
      overflow: auto;
      font-size: 85%;
      line-height: 1.45;
      background-color: #161b22;
      border-radius: 6px;
    }
  </style>
</head>
<body>
  <article class="markdown-body">
    {{{ content }}}
  </article>
</body>
</html>
