import BaseSchema from '@ioc:Adonis/Lucid/Schema'

export default class extends BaseSchema {
  protected tableName = 'schedules'

  public async up () {
    this.schema.alterTable(this.tableName, (table) => {
      table.dateTime('follow_up_date', { useTz: true }).after('exam_id')
    })
  }

  public async down () {
    this.schema.alterTable(this.tableName, (table) => {
			table.dropColumn('follow_up_date')
		}
		)
  }
}
