import type { HttpContextContract } from '@ioc:Adonis/Core/HttpContext';
import Database from '@ioc:Adonis/Lucid/Database';
import User from 'App/Models/User';
import ActionLogChanges from 'App/Services/ActionLogChanges';

export default class ListDoctorsController {
	public async index({ response, params, request }: HttpContextContract) {
		const { secure_id } = params;
		const { page = 1, limit = 20, search } = request.only(['page', 'limit', 'search'])

		const doctors = await User.query()
			.whereHas('clinic', builder => {
				builder.where('secure_id', secure_id)
				builder.andWhereIn('type', ['clinic', 'hospital'])
			})
			.andWhere((builder) => {
				if (search) {
					builder.whereILike('email', `%${search}%`)
					builder.orWhereHas('userInfo', builder => {
						builder.whereILike('name', `%${search}%`)
					})
				}
			})
			.preload('userInfo', builder => {
				builder.select('name')
			})
			.join('user_infos', 'user_infos.user_id', 'users.id')
			.orderBy('user_infos.name', 'asc')
			.select('users.*')
			.paginate(page, limit)

		return response.ok(doctors);
	}

	public async store({ response, request, params, auth }: HttpContextContract) {
		const userLogged = auth.user!

		const { secure_id } = params;
		const { doctor_id } = request.only(['doctor_id']);

		await userLogged.load('roles')

		const clinic = await User.query()
			.where('secure_id', secure_id)
			.andWhereIn('type', ['clinic', 'hospital'])
			.preload('doctors', buildDoctor => {
				buildDoctor.select('secure_id')
			})
			.first();

		if (!clinic) {
			return response.notFound({
				type: 'warning',
				message: 'Clínica não encontrada',
			});
		}

		const doctor = await User.query()
			.where('secure_id', doctor_id)
			.andWhere('type', 'doctor')
			.first();

		if (!doctor) {
			return response.notFound({
				type: 'warning',
				message: 'Médico não encontrado',
			});
		}

		const existingRelation = await clinic
			.related('doctors')
			.query()
			.where('doctor_id', doctor.id)
			.first();

		if (existingRelation) {
			return response.badRequest({
				type: 'warning',
				message: 'Médico já associado à clínica',
			});
		}

		await Database.transaction(async trx => {
			await ActionLogChanges.saveLogUserChanges({
				userChange: clinic,
				userChangedData: {
					doctorsSecureIds: [...clinic.doctors.map(doctorOld => doctorOld.secureId), doctor.secureId]
				},
				userLogged,
				trx
			})

			await clinic.useTransaction(trx).related('doctors').attach([doctor.id]);
		})

		return response.ok({
			type: 'success',
			message: 'Médico adicionado com sucesso!',
		});
	}

	public async destroy({ response, params, auth }: HttpContextContract) {
		const userLogged = auth.user!
		const { secure_id, id } = params;

		await userLogged.load('roles')

		const clinic = await User.query()
			.where('secure_id', secure_id)
			.andWhereIn('type', ['clinic', 'hospital'])
			.preload('doctors', builderDoctor => {
				builderDoctor.select('secure_id')
			})
			.first();

		if (!clinic) {
			return response.notFound({
				type: 'warning',
				message: 'Clínica não encontrada',
			});
		}

		const doctor = await User.query()
			.where('secure_id', id)
			.andWhere('type', 'doctor')
			.first();

		if (!doctor) {
			return response.notFound({
				type: 'warning',
				message: 'Médico não encontrado',
			});
		}

		const existingRelation = await clinic
			.related('doctors')
			.query()
			.where('doctor_id', doctor.id)
			.first();

		if (!existingRelation) {
			return response.badRequest({
				type: 'warning',
				message: 'Médico não associado à clínica',
			});
		}

		await Database.transaction(async trx => {
			await ActionLogChanges.saveLogUserChanges({
				userChange: clinic,
				userChangedData: {
					doctorsSecureIds: [...clinic.doctors.filter(doctorFilter => doctorFilter.secureId !== doctor.secureId).map(doctorOld => doctorOld.secureId)]
				},
				userLogged,
				trx
			})

			await clinic.related('doctors').detach([doctor.id]);
		})

		return response.ok({
			type: 'success',
			message: 'Médico removido com sucesso da clínica!',
		});
	}
}
