import { marked } from 'marked';
import fs from 'fs';

export default class MarkedownsController {
	public async index({ view }) {
		const md = './docs/index.md'
		const mdContent = fs.readFileSync(md, 'utf8');
		const htmlContent = marked(mdContent);
		return view.render('markedown', { content: htmlContent });
	}

	public async onesignalPush({ view }) {
		const md = './docs/oneSignalNotification.md'
		const mdContent = fs.readFileSync(md, 'utf8');
		const htmlContent = marked(mdContent);
		return view.render('markedown', { content: htmlContent });
	}

	public async onesignalEmail({ view }) {
		const md = './docs/oneSignalEmail.md'
		const mdContent = fs.readFileSync(md, 'utf8');
		const htmlContent = marked(mdContent);
		return view.render('markedown', { content: htmlContent });
	}

	public async zenviaSms({ view }) {
		const md = './docs/zenviaSms.md'
		const mdContent = fs.readFileSync(md, 'utf8');
		const htmlContent = marked(mdContent);
		return view.render('markedown', { content: htmlContent });
	}
}
