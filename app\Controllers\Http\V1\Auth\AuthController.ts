import Hash from '@ioc:Adonis/Core/Hash'
import type { HttpContextContract } from '@ioc:Adonis/Core/HttpContext'
import User from 'App/Models/User'

export default class AuthController {
	public async store({ request, response, auth, logger }: HttpContextContract) {
		try {
			const { email, password } = request.all()

			const user = await User.query()
				.preload('roles')
				.preload('userInfo')
				.where('email', email.trim())
				.first()

			if (!user || !(await Hash.verify(user.password, password))) {
				return response.unauthorized({
					error: 'Invalid credentials',
					message: 'E-mail ou senha inválidos!',
				})
			}

			if (
				user.roles.length < 1
				// || (user.roles.some((role) => role.name === 'PARTNER') && !user.roles.some((role) => role.name === 'ADMIN') && user.type !== 'doctor')
			) {
				return response.forbidden({
					error: 'Invalid credentials',
					message: 'Você não possui autorização para acessar a plataforma!',
				})
			}

			const token = await auth.login(user, {
				expiresIn: '9999 hours',
			})

			return response.ok({
				user: {
					secureId: user.secureId,
					email: user.email,
					name: user.userInfo.name,
					legalDocumentNumber: user.userInfo.legalDocumentNumber
						? `${user.userInfo.legalDocumentNumber.slice(0, 3)}.***.***-**`
						: undefined,
					isFirstAccess: !!user.isFirstAccess
				},
				roles: user.roles ? user.roles.map((role) => role.name) : [],
				token,
				type: user.type,
			})
		} catch (error) {
			logger.error(error)
			return response.internalServerError({
				type: 'error',
				message: 'Ocorreu um problema ao fazer login.',
			})
		}
	}

	public async destroy({ response, auth }: HttpContextContract) {
		await auth.logout()
		return response.ok({
			type: 'success',
			message: 'Deslogado com sucesso!',
		})
	}
}
