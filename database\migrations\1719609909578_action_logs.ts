import BaseSchema from '@ioc:Adonis/Lucid/Schema'

export default class extends BaseSchema {
	protected tableName = 'action_logs'

	public async up() {
		this.schema.alterTable(this.tableName, (table) => {
			table.enum('type', ['user', 'specialty', 'exam', 'appointment', 'schedule', 'group']).alter()
		})
	}

	public async down() {
		this.schema.alterTable(this.tableName, (table) => {
			table.enum('type', ['user', 'specialty', 'exam', 'appointment', 'schedule']).alter()
		})
	}
}
