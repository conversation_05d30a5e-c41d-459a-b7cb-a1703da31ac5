import Route from '@ioc:Adonis/Core/Route'
import { isRoles } from 'App/Contants/Roles'

Route.group(() => {
  Route.resource('patients-partner', 'V1/Partner/Patient/MainController').middleware({
    index: ['auth', `${isRoles(['PARTNER'])}`],
    store: ['auth', `${isRoles(['PARTNER'])}`],
    update: ['auth', `${isRoles(['PARTNER'])}`],
    destroy: ['auth', `${isRoles(['PARTNER'])}`],
  })

  Route.resource('import-patients-partner', 'V1/Partner/Patient/ImportController').middleware({
    store: ['auth', `${isRoles(['PARTNER'])}`],
  })
})
