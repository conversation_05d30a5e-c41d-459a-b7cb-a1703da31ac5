import Route from '@ioc:Adonis/Core/Route'
import { allRoles } from 'App/Contants/Roles'

Route.group(() => {
	Route.get('dependents', 'V1/Patient/Dependent/MainController.index').middleware([
		'auth',
		`${allRoles()}`,
	])
	Route.post('dependents', 'V1/Patient/Dependent/MainController.store').middleware([
		'auth',
		`${allRoles()}`,
	])
	Route.get('dependents/:id', 'V1/Patient/Dependent/MainController.show').middleware([
		'auth',
		`${allRoles()}`,
	])
	Route.put('dependents/:id', 'V1/Patient/Dependent/MainController.update').middleware([
		'auth',
		`${allRoles()}`,
	])
	Route.delete('dependents/:id', 'V1/Patient/Dependent/MainController.destroy').middleware([
		'auth',
		`${allRoles()}`,
	])

	// Route.resource('dependents', 'V1/Patient/Dependent/MainController').middleware({
	// 	index: ['auth', `${allRoles()}`],
	// 	store: ['auth', `${allRoles()}`],
	// 	update: ['auth', `${allRoles()}`],
	// 	destroy: ['auth', `${allRoles()}`],
	// })
})
