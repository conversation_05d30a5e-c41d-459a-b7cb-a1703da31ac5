import Encryption from '@ioc:Adonis/Core/Encryption'
import Event from '@ioc:Adonis/Core/Event'
import type { HttpContextContract } from '@ioc:Adonis/Core/HttpContext'
import Database from '@ioc:Adonis/Lucid/Database'
import User from 'App/Models/User'
import Users<PERSON>ey from 'App/Models/UsersKey'
import ForgotPasswordValidator from 'App/Validators/Auth/ForgotPassword'
import ResetPasswordValidator from 'App/Validators/Auth/ResetPassword'
import { addHours, differenceInMinutes, format, parseISO } from 'date-fns'
import { DateTime } from 'luxon'

const crypto = require('crypto')

export default class ForgotPasswordController {
	public async store({ request, response, view }: HttpContextContract) {
		const { email, redirectUrl } = await request.validate(ForgotPasswordValidator)

		const tokenNotEncrypt = crypto.randomBytes(20).toString('hex')
		const token = Encryption.encrypt(tokenNotEncrypt)
		const expiresTokenDate = DateTime.fromJSDate(
			parseISO(format(addHours(new Date(), 3), 'yyyy-MM-dd HH:mm:ss'))
		)

		const user = await User.query().where('email', email).firstOrFail()

		await UsersKey.create({
			token: tokenNotEncrypt,
			userId: user.id,
			expiresTokenDate,
		})

		const content = await view.render(`emails/forgot_password`, {
			title: 'Recuperação de Senha | helloMed',
			email,
			payload: {
				link: redirectUrl,
				token,
			}
		})

		Event.emit('new:oneSignalEmail', {
			subject: 'Recuperação de Senha | helloMed',
			emails: [email],
			content: content,
		})

		return response.ok({
			type: 'success',
			message: 'Verifique o email para continuar a recuperação da senha',
		})
	}

	public async update({ request, response, view }: HttpContextContract) {
		const { password, token } = await request.validate(ResetPasswordValidator)
		const tokenDecrypt: string = Encryption.decrypt(token)!

		const userKey = await UsersKey.query().where('token', tokenDecrypt).firstOrFail()
		const expiresTokenDate = Number(userKey.expiresTokenDate)

		const diference = differenceInMinutes(new Date(), expiresTokenDate)
		if (diference >= 0) {
			return response.badRequest({
				type: 'warning',
				message: 'Token expirado!',
			})
		}

		let userData
		await Database.transaction(async (trx) => {
			const user = await User.query().where('id', userKey.userId).preload('userInfo').firstOrFail()
			userData = user
			user.merge({
				password,
			})
			user.useTransaction(trx)
			await user.save()

			await UsersKey.query().where('user_id', user.id).useTransaction(trx).delete()
		})

		const content = await view.render(`emails/reset_password`, {
			title: 'Sua senha foi alterada',
			payload: {
				name: userData.userInfo.name.split(' ')[0],
			}
		})

		Event.emit('new:oneSignalEmail', {
			subject: 'Sua senha foi alterada',
			emails: [userData.email],
			content,
		})

		return response.ok({
			type: 'success',
			message: 'Senha alterada com sucesso!',
		})
	}
}
