import type { HttpContextContract } from '@ioc:Adonis/Core/HttpContext'
import { schema } from '@ioc:Adonis/Core/Validator'

import Group from 'App/Models/Group'

export default class ListGroupsController {
	public async index({ response, request }: HttpContextContract) {
		const schemaValidator = schema.create({
			search: schema.string.optional(),
		})

		const {
			search,
		} = await request.validate({
			schema: schemaValidator
		})

		const groups = await Group.query()
			.select('id', 'secure_id', 'name')
			.andWhere(builder => {
				if (search) {
					builder.whereILike('name', `%${search}%`)
				}
			})

		return response.ok(groups)
	}
}
