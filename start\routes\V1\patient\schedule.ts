import Route from '@ioc:Adonis/Core/Route'
import { allRoles } from 'App/Contants/Roles'

Route.group(() => {
	Route.resource('schedule', 'V1/Patient/Schedule/MainController').middleware({
		index: ['auth', `${allRoles()}`],
		store: ['auth', `${allRoles()}`],
		show: ['auth', `${allRoles()}`],
	})
	Route.put('schedule-canceled/:id', 'V1/Patient/Schedule/MainController.destroy').middleware([
		'auth',
		`${allRoles()}`,
	])
})
