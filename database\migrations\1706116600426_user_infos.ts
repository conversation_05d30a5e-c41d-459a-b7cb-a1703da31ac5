import BaseSchema from '@ioc:Adonis/Lucid/Schema'

export default class extends BaseSchema {
  protected tableName = 'user_infos'

  public async up() {
    this.schema.alterTable(this.tableName, (table) => {
      table.enum('type_document', ['cpf', 'cnpj']).after('legal_document_number')
    })
  }

  public async down() {
    this.schema.alterTable(this.tableName, (table) => {
      table.dropColumn('type_document')
    })
  }
}
