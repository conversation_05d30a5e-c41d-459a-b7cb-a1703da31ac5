import { DateTime } from 'luxon'
import {
	BaseModel,
	BelongsTo,
	HasMany,
	ManyToMany,
	beforeCreate,
	belongsTo,
	column,
	hasMany,
	manyToMany,
} from '@ioc:Adonis/Lucid/Orm'

import { v4 as uuid } from 'uuid'
import Upload from './Upload'
import User from './User'
import ActionLog from './ActionLog'

export default class Specialty extends BaseModel {
	@column({ isPrimary: true, serializeAs: null })
	public id: number

	@column()
	public secureId: string

	@column({ serializeAs: null })
	public thumbId: number

	@column()
	public name: string

	@column()
	public tags: string

	@column()
	public labelAdvice: string

	@column({ serialize: (value) => Boolean(value) })
	public active: boolean

	@belongsTo(() => Upload, {
		foreignKey: 'thumbId',
		localKey: 'id',
	})
	public thumb: BelongsTo<typeof Upload>

	@hasMany(() => ActionLog, {
		foreignKey: 'chargedId',
		localKey: 'id',
		onQuery: query => query.where('type', 'specialty')
	})
	public logs: HasMany<typeof ActionLog>

	@manyToMany(() => User, {
		pivotTable: 'user_specialties',
	})
	public users: ManyToMany<typeof User>

	@column.dateTime({
		autoCreate: true,
		serialize: (value: DateTime) => {
			if (value) return value.toFormat('yyyy-MM-dd HH:mm:ss').replace(/\s+/g, 'T')
			return
		},
	})
	public createdAt: DateTime

	@column.dateTime({
		autoCreate: true,
		autoUpdate: true,
		serialize: (value: DateTime) => {
			if (value) return value.toFormat('yyyy-MM-dd HH:mm:ss').replace(/\s+/g, 'T')
			return
		},
	})
	public updatedAt: DateTime

	@beforeCreate()
	public static async createUUID(model: Specialty) {
		model.secureId = uuid()
	}
}
