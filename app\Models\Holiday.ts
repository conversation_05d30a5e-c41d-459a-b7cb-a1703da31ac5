import { DateTime } from "luxon";
import { v4 as uuid } from 'uuid';
import { BaseModel, beforeCreate, column } from "@ioc:Adonis/Lucid/Orm";

export default class Holiday extends BaseModel {
	@column({ isPrimary: true, serializeAs: null })
	public id: number

	@column({ serializeAs: 'secureId' })
	public secure_id: string

	@column()
	public date: Date

	@column()
	public description: string

	@column.dateTime({
		autoCreate: true,
		serialize: (value: DateTime) => {
			if (value) return value.toFormat('yyyy-MM-dd HH:mm:ss').replace(/\s+/g, 'T')
			return
		},
	})
	public createdAt: DateTime

	@column.dateTime({
		autoCreate: true,
		autoUpdate: true,
		serialize: (value: DateTime) => {
			if (value) return value.toFormat('yyyy-MM-dd HH:mm:ss').replace(/\s+/g, 'T')
			return
		},
	})
	public updatedAt: DateTime

	@beforeCreate()
	public static async createUUID(model: Holiday) {
		if (!model.secure_id) {
			model.secure_id = uuid()
		}
	}
}
