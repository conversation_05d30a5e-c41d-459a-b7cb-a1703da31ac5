import { SwaggerConfig } from '@ioc:Adonis/Addons/Swagger'

export default {
	uiEnabled: true, //disable or enable swaggerUi route
	uiUrl: 'docs', // url path to swaggerUI
	specEnabled: true, //disable or enable swagger.json route
	specUrl: '/swagger.json',

	middleware: [], // middlewares array, for protect your swagger docs and spec endpoints

	options: {
		definition: {
			openapi: '3.0.0',
			info: {
				title: 'API HelloMed - Documentação de Rotas',
				version: '1.0.0',
				description: 'Este guia fornece uma visão detalhada das rotas e pontos de extremidade da API HelloMed, projetada para facilitar sua jornada de exploração e compreensão de como se engajar com nossos serviços de saúde. Ele serve como um mapa para navegar na complexidade da nossa aplicação HelloMed, permitindo que você interaja de maneira eficaz e eficiente com nossos recursos de saúde.\n\n ## Outra Documentações \n\n[Features](/v1/admin/docs/index)'
			}
		},

		apis: [
			'app/**/*.ts',
			'docs/swagger/*.yml',
			'start/routes/index.ts'
		],
		basePath: '/'
	},
	mode: process.env.NODE_ENV === 'production' ? 'PRODUCTION' : 'RUNTIME',
  specFilePath: 'docs/swagger.json'
} as SwaggerConfig
