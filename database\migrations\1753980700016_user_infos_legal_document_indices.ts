import BaseSchema from '@ioc:Adonis/Lucid/Schema'

export default class extends BaseSchema {
  protected tableName = 'user_infos'

  public async up() {
    this.schema.alterTable(this.tableName, (table) => {
      table.index(['legal_document_number'], 'user_infos_legal_document_number_idx')
    })
  }

  public async down() {
    this.schema.alterTable(this.tableName, (table) => {
      table.dropIndex(['legal_document_number'], 'user_infos_legal_document_number_idx')
    })
  }
}
