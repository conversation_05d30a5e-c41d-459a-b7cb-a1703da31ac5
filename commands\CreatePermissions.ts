import { BaseCommand, flags } from '@adonisjs/core/build/standalone'
import Database from '@ioc:Adonis/Lucid/Database'
import { PERMISSION } from 'App/Contants/Permissions'
import { SYSTEM_PERMISSIONS } from 'App/Utils/Permissions'

export default class CreatePermissions extends BaseCommand {
	public static commandName = 'create:permissions'

	public static title = 'Criar permissions'
	public static description =
		'Cria as permissions ainda não criadas e apaga as que foram removidas do controle de rules'

	@flags.boolean()
	public confirmation: boolean = true

	public static settings = {
		loadApp: true,
		stayAlive: false,
	}

	public async run() {
		const { default: Permission } = await import('App/Models/Permission')

		try {
			if (this.confirmation) {
				this.ui
					.sticker()
					.add(this.colors.bold(this.colors.cyan(CreatePermissions.title)))
					.add('')
					.add(this.colors.gray(CreatePermissions.description))
					.render()

				const confirm = await this.prompt.confirm('Deseja continuar?')

				if (!confirm) return
			}

			await Permission.updateOrCreateMany('slug', Object.values(PERMISSION))

			await Permission.query()
				.whereNotIn('slug', [...SYSTEM_PERMISSIONS])
				.delete()

			await Database.manager.closeAll()
			this.logger.log(
				`${this.colors.green('Permissions atualizadas com sucesso!')} ${this.ui.icons.tick}`
			)
		} catch (error) {
			this.logger.logError(error)
			this.logger.log(
				`${this.colors.red('Ocorreu um erro ao atualizar permissions')} ${this.ui.icons.cross}`
			)
		}
	}
}
