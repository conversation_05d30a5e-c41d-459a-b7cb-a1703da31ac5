import type { HttpContextContract } from '@ioc:Adonis/Core/HttpContext'
import Database from '@ioc:Adonis/Lucid/Database'

import Notification from 'App/Models/Notification'

import { StoreValidator } from 'App/Validators/Admin/Notification'
import User from 'App/Models/User'

export default class NotificationController {
	public async store({ request, response }: HttpContextContract) {
		const data = await request.validate(StoreValidator)

		const patients = await User.query()
			.select('id', 'parent_id', 'secure_id')
			.preload('userInfo', (builder) => {
				builder.select('name')
			})
			.whereHas('roles', (builder) => {
				builder.whereIn('slug', ['patient'])
			})

		await Database.transaction(async (trx) => {
			for await (const patient of patients) {
				const newNotification = new Notification()
				newNotification.merge({
					...data,
					status: 'not_read',
					userId: patient.id,
				})
				newNotification.useTransaction(trx)
				await newNotification.save()
			}
		})

		return response.ok({
			type: 'success',
			message: 'Notificação enviada!',
		})
	}
}
