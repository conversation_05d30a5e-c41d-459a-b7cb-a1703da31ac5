import BaseSchema from '@ioc:Adonis/Lucid/Schema'

export default class extends BaseSchema {
	protected tableName = 'groups'

	public async up() {
		this.schema.createTable(this.tableName, (table) => {
			table.increments('id')
			table.string('secure_id').notNullable()
			table.string('name')


			/**
			 * Uses timestamptz for PostgreSQL and DATETIME2 for MSSQL
			 */
			table.timestamp('created_at')
			table.timestamp('updated_at')
		})
	}

	public async down() {
		this.schema.dropTable(this.tableName)
	}
}
