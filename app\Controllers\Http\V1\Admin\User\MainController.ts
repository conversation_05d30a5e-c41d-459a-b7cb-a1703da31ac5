import type { HttpContextContract } from '@ioc:Adonis/Core/HttpContext'
import { schema } from '@ioc:Adonis/Core/Validator'
import Database from '@ioc:Adonis/Lucid/Database'

import Role from 'App/Models/Role'
import User from 'App/Models/User'
import Upload from 'App/Models/Upload'
import UserInfo from 'App/Models/UserInfo'

import ActionLogChanges from 'App/Services/ActionLogChanges'

import { StoreValidator, UpdateValidator } from 'App/Validators/Admin/User'

export default class UserController {
	public async index({ response, request, auth }: HttpContextContract) {
		const schemaValidator = schema.create({
			page: schema.number.optional(),
			limit: schema.number.optional(),
			search: schema.string.optional(),
			allowUserLogged: schema.boolean.optional(),
		})

		const {
			page = 1,
			limit = 1,
			search,
			allowUserLogged = false
		} = await request.validate({
			schema: schemaValidator
		})

		const userLogged = auth.user!
		const users = await User.query()
			.select('users.id', 'users.secure_id', 'users.email')
			.preload('userInfo')
			.whereHas('roles', (builder) => {
				builder.whereIn('slug', ['admin'])
			})
			.andWhere((builder) => {
				if (search) {
					builder.whereILike('users.email', `%${search}%`)
						.orWhere((builder) => {
							builder.whereHas('userInfo', (builder) => {
								builder.whereILike('name', `%${search}%`)
							})
						})
				}
			})
			.andWhere(builder => {
				if (!allowUserLogged) builder.whereNot('users.id', userLogged.id)
			})
			.andWhereNot('users.deleted', true)
			.join('user_infos', 'user_infos.user_id', 'users.id')
			.orderBy('user_infos.name', 'asc')
			.paginate(page, limit)

		return response.ok(users)
	}

	public async store({ request, response }: HttpContextContract) {
		const { avatarSecureId, userExists, email, password, ...dataInfo } = await request.validate(
			StoreValidator
		)

		const user = await User.query().where('email', email).preload('roles').first()

		if (userExists && user && user.roles.find((role) => role.name === 'ADMIN')) {
			return response.badRequest({
				type: 'warning',
				message: 'Esse usuário já foi cadastrado como administrador!',
			})
		}

		await Database.transaction(async (trx) => {
			if (userExists && user) {
				const role = await Role.query().where('name', 'ADMIN').firstOrFail()

				if (!user.roles.find((role) => role.name === 'ADMIN')) {
					await user.useTransaction(trx).related('roles').attach([role.id])
				}
			} else {
				const newUser = new User()

				if (avatarSecureId) {
					const avatar = await Upload.query().where('secure_id', avatarSecureId).firstOrFail()

					newUser.merge({
						avatarId: avatar.id,
					})
				}

				newUser.merge({
					email,
					password,
					type: 'admin',
				})
				newUser.useTransaction(trx)
				await newUser.save()

				const newUserInfo = new UserInfo()
				newUserInfo.merge({
					userId: newUser.id,
					...dataInfo,
				})
				newUserInfo.useTransaction(trx)
				await newUserInfo.save()

				const rolesSearch = await Role.query().where('name', 'ADMIN')
				await newUser
					.useTransaction(trx)
					.related('roles')
					.sync(rolesSearch.map((role) => role.id))
			}
		})

		return response.ok({
			type: 'success',
			message: 'Usuário criado com sucesso!',
		})
	}

	public async show({ response, params }: HttpContextContract) {
		const user = await User.query()
			.select('id', 'avatar_id', 'secure_id', 'email')
			.where('secure_id', params.id)
			.andWhereHas('roles', (builder) => {
				builder.whereIn('slug', ['admin'])
			})
			.andWhereNot('deleted', true)
			.preload('permissions')
			.preload('userInfo')
			.preload('avatar', (builderAvatar) => {
				builderAvatar.select('id', 'secure_id', 'url', 'name')
			})
			.firstOrFail()

		return response.ok(user)
	}

	public async update({ response, request, params, auth }: HttpContextContract) {
		const { avatarSecureId, email, password, ...dataInfo } = await request.validate(UpdateValidator)

		const userLogged = auth.user!

		const user = await User.query()
			.where('secure_id', params.id)
			.andWhereHas('roles', (builder) => {
				builder.whereIn('slug', ['admin'])
			})
			.preload('userInfo')
			.firstOrFail()

		await Database.transaction(async (trx) => {
			await ActionLogChanges.saveLogUserChanges({
				userChange: user,
				userChangedData: { avatarSecureId, email, ...dataInfo },
				userLogged,
				trx
			})

			if (avatarSecureId) {
				const avatar = await Upload.query().where('secure_id', avatarSecureId).firstOrFail()

				user.merge({
					avatarId: avatar.id,
				})
			}

			user.merge({ email, password })
			user.useTransaction(trx)
			await user.save()

			const userInfo = user.userInfo
			userInfo.merge({ ...dataInfo })
			await userInfo.save()
		})

		return response.ok({
			type: 'success',
			message: 'Usuário atualizado com sucesso!',
		})
	}

	public async destroy({ response, params }: HttpContextContract) {
		const user = await User.query()
			.where('secure_id', params.id)
			.andWhereHas('roles', (builder) => {
				builder.whereIn('slug', ['admin'])
			})
			.firstOrFail()

		user.merge({ deleted: true })
		await user.save()

		return response.ok({
			type: 'success',
			message: 'Usuário removido com sucesso!',
		})
	}
}
