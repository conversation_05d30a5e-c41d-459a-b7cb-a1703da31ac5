import BaseSchema from '@ioc:Adonis/Lucid/Schema'

export default class extends BaseSchema {
  protected tableName = 'import_jobs'

  public async up() {
    this.schema.alterTable(this.tableName, (table) => {
      table.boolean('is_read').defaultTo(false).after('errors_report')
    })
  }

  public async down() {
    this.schema.alterTable(this.tableName, (table) => {
      table.dropColumn('is_read')
    })
  }
}
