import Route from '@ioc:Adonis/Core/Route'

Route.group(() => {
	require('./user')
	require('./partner')
	require('./accredited')
	require('./patient')
	require('./dependent')
	require('./specialty')
	require('./exams')
	require('./permission')
	require('./list')
	require('./notification')
	require('./schedule')
	require('./appointment')
	require('./zenvia')
	require('./onesignal')
	require('./docs')
	require('./templatenotifications')
	require('./actionLog')
	require('./group')
	require('./dashboard')
	require('./observation')
	require('./holiday')
	require('./reports')
	require('./importJobs')
}).prefix('admin')
