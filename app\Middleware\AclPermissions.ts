import { HttpContextContract } from '@ioc:Adonis/Core/HttpContext'
import { SystemPermissions } from 'App/Contants/Permissions'
import { intersection } from 'lodash'

export default class AclPermissions {
	public async handle(
		{ auth: { user }, response }: HttpContextContract,
		next: () => Promise<void>,
		middlewareParams: SystemPermissions
	) {
		if (!user)
			return response.unauthorized({
				type: 'error',
				message: 'Acesso não autorizado!',
			})
		await user.load('roles')

		const userPermissions = await user.related('permissions').query()

		if (!user.roles.find((role) => role.name === 'MASTER')) {
			const userPermissionsSlugs = userPermissions.map(({ slug }) => slug)

			if (intersection(userPermissionsSlugs, middlewareParams).length < 1) {
				return response.forbidden({
					type: 'error',
					message: 'Você não possui permissão para realizar essa ação.',
				})
			}
		}

		await next()
	}
}
