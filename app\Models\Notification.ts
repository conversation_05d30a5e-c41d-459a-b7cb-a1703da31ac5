import { DateTime } from 'luxon'
import { BaseModel, column, beforeCreate, hasOne, HasOne } from '@ioc:Adonis/Lucid/Orm'

import { v4 as uuid } from 'uuid'
import User from './User'

export default class Notification extends BaseModel {
	@column({ isPrimary: true, serializeAs: null })
	public id: number

	@column()
	public secureId: string

	@column({ serializeAs: null })
	public userId: number

	@column()
	public title: string

	@column()
	public text: string

	@column()
	public status: 'read' | 'not_read'

	@hasOne(() => User, {
		foreignKey: 'id',
		localKey: 'userId',
	})
	public user: HasOne<typeof User>

	@column.dateTime({
		autoCreate: true,
		serialize: (value: DateTime) => {
			if (value) return value.toFormat('yyyy-MM-dd HH:mm:ss').replace(/\s+/g, 'T')
			return
		},
	})
	public readAt: DateTime

	@column.dateTime({
		autoCreate: true,
		serialize: (value: DateTime) => {
			if (value) return value.toFormat('yyyy-MM-dd HH:mm:ss').replace(/\s+/g, 'T')
			return
		},
	})
	public createdAt: DateTime

	@column.dateTime({
		autoCreate: true,
		autoUpdate: true,
		serialize: (value: DateTime) => {
			if (value) return value.toFormat('yyyy-MM-dd HH:mm:ss').replace(/\s+/g, 'T')
			return
		},
	})
	public updatedAt: DateTime

	@beforeCreate()
	public static async createUUID(model: Notification) {
		model.secureId = uuid()
	}
}
