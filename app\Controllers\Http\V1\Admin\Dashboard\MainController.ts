import type { HttpContextContract } from '@ioc:Adonis/Core/HttpContext'
import { schema, rules } from '@ioc:Adonis/Core/Validator'
import { DateTime } from 'luxon'

import Schedule from 'App/Models/Schedule'

import { endOfDay, startOfDay } from 'date-fns'

export default class DashboardController {
	public async index({ request, response }: HttpContextContract) {
		const schemaValidator = schema.create({
			page: schema.number.optional(),
			limit: schema.number.optional(),
			startDate: schema.date.optional(),
			endDate: schema.date.optional(),
			specialty: schema.string.optional([rules.trim(), rules.exists({ table: 'specialties', column: 'secure_id' })]),
			exam: schema.string.optional([rules.trim(), rules.exists({ table: 'exams', column: 'secure_id' })]),
			partner: schema.string.optional([rules.trim(), rules.exists({ table: 'users', column: 'secure_id' })]),

			followUpDateStart: schema.date.optional(),
			followUpDateEnd: schema.date.optional(),

			inCredentialedSchedulesPage: schema.number.optional(),
			inCredentialedSchedulesLimit: schema.number.optional(),
			inCredentialedSchedulesName: schema.string.optional()
		})

		const {
			page = 1,
			limit = 15,
			startDate,
			endDate,
			specialty,
			exam,
			partner,

			followUpDateStart,
			followUpDateEnd,

			inCredentialedSchedulesPage = 1,
			inCredentialedSchedulesLimit = 15,
			inCredentialedSchedulesName,

		} = await request.validate({
			schema: schemaValidator
		})

		const startOfDayDate = startDate ? DateTime.fromJSDate(startOfDay(startDate.toJSDate())) : undefined
		const endOfDayDate = endDate ? DateTime.fromJSDate(endOfDay(endDate.toJSDate())) : undefined

		const followUpStartOfDayDate = followUpDateStart ? DateTime.fromJSDate(startOfDay(followUpDateStart.toJSDate())) : undefined
		const followUpEndOfDayDate = followUpDateEnd ? DateTime.fromJSDate(endOfDay(followUpDateEnd.toJSDate())) : undefined

		const appointments = await Schedule.query()
			.select('id', 'user_id', 'patient_id', 'specialty_id', 'exam_id', 'secure_id', 'status', 'created_at')
			.where('status', 'waiting_backoffice')
			.andWhere(builder => {
				if (startOfDayDate && endOfDayDate) {
					builder.whereBetween('created_at', [startOfDayDate.toSQL()!, endOfDayDate.toSQL()!])
				} else if (startOfDayDate) {
					builder.where('created_at', '>=', startOfDayDate.toSQL()!)
				} else if (endOfDayDate) {
					builder.where('created_at', '<=', endOfDayDate.toSQL()!)
				}

				if (followUpStartOfDayDate && followUpEndOfDayDate) {
					builder.whereBetween('follow_up_date', [followUpStartOfDayDate.toSQL()!, followUpEndOfDayDate.toSQL()!])
				} else if (followUpStartOfDayDate) {
					builder.where('follow_up_date', '>=', followUpStartOfDayDate.toSQL()!)
				} else if (followUpEndOfDayDate) {
					builder.where('follow_up_date', '<=', followUpEndOfDayDate.toSQL()!)
				}
			})
			.andWhere(builder => {
				if (specialty) {
					builder.whereHas('specialty', builderSpecialty => {
						builderSpecialty.where('secure_id', specialty)
					})
				}
			})
			.andWhere(builder => {
				if (exam) {
					builder.whereHas('exam', builderExam => {
						builderExam.where('secure_id', exam)
					})
				}
			})
			.andWhere(builder => {
				if (partner) {
					builder.whereHas('patient', builderPatient => {
						builderPatient.whereHas('partners', builderPartners => {
							builderPartners.where('secure_id', partner)
						})
					})
				}
			})
			.preload('patient', builderPatient => {
				builderPatient.select('id', 'secure_id', 'email')
				builderPatient.preload('userInfo', builderUserInfo => {
					builderUserInfo.select('id', 'user_id', 'name')
				})
				builderPatient.preload('partners', builderPartners => {
					builderPartners.select('id', 'secure_id', 'email')
					builderPartners.preload('userInfo', builderUserInfo => {
						builderUserInfo.select('id', 'user_id', 'name')
					})
				})
			})
			.paginate(page, limit)

		const inCredentialedSchedules = await Schedule.query()
			.where('status', 'in_accreditation')
			.preload('specialty', (builder) => {
				builder.select('id', 'name')
			})
			.preload('exam', (builder) => {
				builder.select('id', 'name')
			})
			.preload('patient', (builder) => {
				builder.select('id')
				builder.preload('userInfo', (builderUserInfo) => {
					builderUserInfo.select('name')
				})
			})
			.andWhere(builder => {
				if (inCredentialedSchedulesName) {
					builder.whereHas('patient', builderPatient => {
						builderPatient.whereHas('userInfo', builderUserInfo => {
							builderUserInfo.whereILike('name', `%${inCredentialedSchedulesName}%`)
						})
					})
				}
			})

			.andWhere(builder => {
				if (specialty) {
					builder.whereHas('specialty', builderSpecialty => {
						builderSpecialty.where('secure_id', specialty)
					})
				}
			})
			.andWhere(builder => {
				if (exam) {
					builder.whereHas('exam', builderExam => {
						builderExam.where('secure_id', exam)
					})
				}
			})
			.andWhere(builder => {
				if (partner) {
					builder.whereHas('patient', builderPatient => {
						builderPatient.whereHas('partners', builderPartners => {
							builderPartners.where('secure_id', partner)
						})
					})
				}
			})
			.andWhere(builder => {
				if (startOfDayDate && endOfDayDate) {
					builder.whereBetween('created_at', [startOfDayDate.toSQL()!, endOfDayDate.toSQL()!])
				} else if (startOfDayDate) {
					builder.where('created_at', '>=', startOfDayDate.toSQL()!)
				} else if (endOfDayDate) {
					builder.where('created_at', '<=', endOfDayDate.toSQL()!)
				}

				if (followUpStartOfDayDate && followUpEndOfDayDate) {
					builder.whereBetween('follow_up_date', [followUpStartOfDayDate.toSQL()!, followUpEndOfDayDate.toSQL()!])
				} else if (followUpStartOfDayDate) {
					builder.where('follow_up_date', '>=', followUpStartOfDayDate.toSQL()!)
				} else if (followUpEndOfDayDate) {
					builder.where('follow_up_date', '<=', followUpEndOfDayDate.toSQL()!)
				}
			})
			.paginate(
				inCredentialedSchedulesPage,
				inCredentialedSchedulesLimit
			)

		return response.ok({
			appointments,
			inCredentialedSchedules
		})
	}
}
