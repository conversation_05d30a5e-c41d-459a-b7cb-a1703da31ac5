import BaseSchema from '@ioc:Adonis/Lucid/Schema'

export default class extends BaseSchema {
  protected tableName = 'user_infos'

  public async up () {
    this.schema.alterTable(this.tableName, (table) => {
      table.string('one_signal_key').after('type_of_care')
    })
  }

  public async down () {
    this.schema.alterTable(this.tableName, (table) => {
      table.dropColumn('one_signal_key')
    })
  }
}
