import type { HttpContextContract } from '@ioc:Adonis/Core/HttpContext'

import Specialty from 'App/Models/Specialty'

export default class SpecialtyController {
	public async index({ response, request }: HttpContextContract) {
		const { page = 1, limit = 15, search } = request.only(['page', 'limit', 'search'])

		const specialties = await Specialty.query()
			.select('id', 'secure_id', 'thumb_id', 'name', 'label_advice', 'active')
			.where((builder) => {
				if (search) {
					// builder.whereRaw('MATCH (name,tags) AGAINST (LOWER(?) IN BOOLEAN MODE)', [`${search}`])
					builder.whereRaw(`LOWER(name) like LOWER('%${search}%')`)
					builder.orWhereRaw(`LOWER(tags) like LOWER('%${search}%')`)
				}
			})
			.preload('thumb', (builder) => {
				builder.select('id', 'url')
			})
			.orderBy('name', 'asc')
			.paginate(page, limit)

		return response.ok(specialties)
	}
}
