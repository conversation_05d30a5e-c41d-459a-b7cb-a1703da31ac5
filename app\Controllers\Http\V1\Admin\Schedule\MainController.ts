import type { HttpContextContract } from '@ioc:Adonis/Core/HttpContext'
import Database from '@ioc:Adonis/Lucid/Database'

import User from 'App/Models/User'
import Exam from 'App/Models/Exam'
import Schedule from 'App/Models/Schedule'
import Specialty from 'App/Models/Specialty'
import ScheduleDatesRequest from 'App/Models/ScheduleDatesRequest'

import ActionLogChanges from 'App/Services/ActionLogChanges'
import NotificationServices from 'App/Services/NotificationServices'

import { StoreValidator, UpdateValidator } from 'App/Validators/Admin/Schedule'
import Appointment from 'App/Models/Appointment'
import Observations from 'App/Models/Observations'
import { addDays, addMonths, getDate, isWeekend, lastDayOfMonth } from 'date-fns'
import Holiday from 'App/Models/Holiday'
import moment from 'moment'
import { DateTime } from 'luxon'

export default class ScheduleController {
	public async index({ request, response }: HttpContextContract) {
		const { page = 1, limit = 5, currentStatus, status, search, field = 'created_at', direction = 'desc', } = request.only(['page', 'search', 'limit', 'currentStatus', 'status', 'field', 'direction'])

		const schedule = await Schedule.query()
			.where((builder) => {
				if (status) {
					builder.whereIn('status', status)
				}
			})

			.andWhere((builder) => {
				if (currentStatus) {
					builder.whereIn('current_status', currentStatus)
				}
			})

			.andWhereNot('status', 'approved')
			.whereHas('patient', (builderPatient) => {
				builderPatient.whereHas('userInfo', (builderUserInfo) => {
					if (search) {
						builderUserInfo.whereRaw(`LOWER(name) like LOWER('%${search}%')`)

						const normalizedSearch = search.replace(/\D/g, '');

						if (normalizedSearch !== '') {
							builderUserInfo.orWhereRaw(`
								REPLACE(REPLACE(legal_document_number, '.', ''), '-', '') LIKE '%${normalizedSearch}%'
								`)
						}
					}
				})
			})
			// .orWhereHas('user', (builderUser) => {
			// 	builderUser.whereHas('partners', (builderPartner) => {
			// 		if (search) {
			// 			builderPartner.whereRaw(`LOWER(name) like LOWER('%${search}%')`)
			// 		}
			// 	})
			// })
			.preload('specialty', (builder) => {
				builder.select('id', 'name')
			})
			.preload('exam', (builder) => {
				builder.select('id', 'name')
			})
			.preload('scheduleDatesRequests', (builder) => {
				builder.select(
					'id',
					'partner_id',
					'secure_id',
					'date',
					'partner_type',
					'date_type',
					'value',
					'status',
					'query_value',
					'payment_methods',
					'query_value_subsidy',
					'query_value_patient'
				)
				builder.preload('partner', (builderPartner) => {
					builderPartner.select('id')
					builderPartner.preload('userInfo', (builderUserInfo) => {
						builderUserInfo.select('name')
					})
				})
			})
			.preload('patient', (builder) => {
				builder.select('id')
				builder.preload('userInfo', (builderUserInfo) => {
					builderUserInfo.select('name')
				})
			})
			.preload('observations', (builder) => {
				builder.select('observation', 'secure_id').where('is_active', true)
			})
			.orderBy(field, direction)
			.paginate(page, limit)

		return response.ok(schedule)
	}

	public async store({ request, response, auth }: HttpContextContract) {
		const {
			patientSecureId,
			accreditedSecureId,
			specialtyOrExamSecureId,
			typeConsult,
			dates,
			local,
			status,
			attendantType,
			queryValueSubsidy,
			queryValuePatient,
			observation
		} = await request.validate(StoreValidator)
		const userLogged = auth.user!

		const schedule = new Schedule()

		// let followUpDate

		// if (status === "in_accreditation") {
		// 	followUpDate = await this.generateFollowUpDate(undefined, true)
		// } else {
		// 	followUpDate = await this.generateFollowUpDate(undefined, false)
		// }

		// const formattedFollowUpDate = DateTime.fromJSDate(followUpDate, { zone: 'America/Sao_Paulo' }).setLocale('pt-BR')

		const followUpDate = await this.generateFollowUpDate()

		followUpDate.setHours(followUpDate.getHours() - 3) // Subtrai 3 horas

		const formattedFollowUpDate = DateTime.fromJSDate(new Date(followUpDate))
			.toUTC()
			.toFormat('yyyy-MM-dd HH:mm:ss')

		await Database.transaction(async (trx) => {
			const patient = await User.query().where('secure_id', patientSecureId).firstOrFail()

			schedule.merge({
				...local,
				status,
				typeConsult,
				patientId: patient.id,
				userId: userLogged.id,
				// @ts-ignore
				follow_up_date: formattedFollowUpDate,
			})

			schedule.useTransaction(trx)
			const savedSchedule = await schedule.save()
			const scheduleId = savedSchedule.$attributes.id;

			if (observation) {
				const newObservation = new Observations()

				newObservation.merge({
					observation: observation,
					schedule_id: scheduleId
				})

				newObservation.useTransaction(trx)
				await newObservation.save()
			}


			if (typeConsult !== 'exam' && specialtyOrExamSecureId) {
				const specialty = await Specialty.query()
					.where('secure_id', specialtyOrExamSecureId)
					.firstOrFail()

				schedule.merge({
					specialtyId: specialty.id,
				})

				schedule.useTransaction(trx)
				await schedule.save()
			} else {
				const exam = await Exam.query().where('secure_id', specialtyOrExamSecureId).firstOrFail()

				schedule.merge({
					examId: exam.id,
				})

				schedule.useTransaction(trx)
				await schedule.save()
			}

			let accreditedId: number | null = null
			let accreditedQueryValue: number | undefined

			if (accreditedSecureId && attendantType !== 'helloMed') {
				const accredited = await User.query()
					.where('secure_id', accreditedSecureId)
					.preload('userInfo')
					.firstOrFail()

				accreditedId = accredited.id
				accreditedQueryValue = accredited.userInfo.queryValue
			}

			for await (const date of dates) {
				const scheduleDate = new ScheduleDatesRequest()

				scheduleDate.merge({
					partnerId: accreditedId,
					scheduleId: schedule.id,
					dateType: date.type,
					partnerType: attendantType,
					type: 'patient',
					status: 'to_check',
					date: date.date,
					value: date.value,
					queryValue: accreditedQueryValue!,
					queryValueSubsidy: queryValueSubsidy,
					queryValuePatient: queryValuePatient,
				})

				scheduleDate.useTransaction(trx)
				await scheduleDate.save()
			}

			await ActionLogChanges.saveLogScheduleCreate({
				schedule,
				userLogged,
				trx
			})
		})

		return response.ok({
			type: 'success',
			message: 'Solicitação criada com sucesso!',
		})
	}

	public async show({ response, params }: HttpContextContract) {
		const schedule = await Schedule.query()
			.where('secure_id', params.id)
			.preload('specialty', (builder) => {
				builder.select('id', 'name')
			})
			.preload('exam', (builder) => {
				builder.select('id', 'name')
			})
			.preload('scheduleDatesRequests', (builder) => {
				builder.select(
					'id',
					'partner_id',
					'secure_id',
					'date',
					'partner_type',
					'date_type',
					'type',
					'value',
					'status',
					'query_value',
					'payment_methods',
					'query_value_subsidy',
					'query_value_patient'
				)
				builder.preload('partner', (builderPartner) => {
					builderPartner.select('id')
					builderPartner.preload('userInfo', (builderUserInfo) => {
						builderUserInfo.select('name', 'ddd_phone', 'phone', 'ddd_cell', 'cell', 'status')
					})
				})
			})
			.preload('patient', (builder) => {
				builder.select('id', 'parent_id')
				builder.preload('userInfo', (builderUserInfo) => {
					builderUserInfo.select('name', 'ddd_phone', 'phone', 'ddd_cell', 'cell')
				})
				builder.preload('parent', (builder) => {
					builder.preload('userInfo', (builderUserInfo) => {
						builderUserInfo.select('name', 'ddd_phone', 'phone', 'ddd_cell', 'cell')
					})
				})
			})
			.preload('observations', (builder) => {
				builder.select('observation', 'secure_id').where('is_active', true)
			})
			.preload('uploads', builder => {
				builder.select('url')
			})
			.firstOrFail()
		return response.ok(schedule)
	}

	public async update({ request, response, params, auth }: HttpContextContract) {
		const userLogged = auth.user!

		const {
			scheduleDateRequestSecureId,
			status,
		} = await request.validate(UpdateValidator)

		const { currentStatus } = request.only(['currentStatus'])


		await Database.transaction(async trx => {
			const schedule = await Schedule.query()
				.where('secure_id', params.id)
				.preload('scheduleDatesRequests')
				.firstOrFail()

			const solicitationDate = schedule.createdAt.toJSDate()
			let followUpDate

			if (status === "in_accreditation") {
				followUpDate = await this.generateFollowUpDate(solicitationDate, true)
			} else {
				followUpDate = await this.generateFollowUpDate(solicitationDate, false)
			}

			const formattedFollowUpDate = DateTime.fromJSDate(followUpDate, { zone: 'America/Sao_Paulo' }).setLocale('pt-BR')

			if (scheduleDateRequestSecureId && status === "approved") {
				const scheduleDate = await ScheduleDatesRequest.query()
					.where('secureId', scheduleDateRequestSecureId)
					.preload('partner')
					.preload('schedule', (builder) => {
						builder.select('id', 'user_id', 'patient_id', 'specialty_id', 'exam_id')
					})
					.firstOrFail()

				const appointment = new Appointment()
				const data: Appointment = {
					userId: scheduleDate.schedule.userId,
					patientId: scheduleDate.schedule.patientId,
					partnerId: scheduleDate.partnerId!,
					specialtyId: scheduleDate.schedule.specialtyId,
					examId: scheduleDate.schedule.examId,
					scheduleId: scheduleDate.scheduleId,
					//@ts-expect-error O partnerType nunca será helloMed
					partnerType: scheduleDate.partnerType,
					date: scheduleDate.date,
					status: 'approved',
					currentStatus: currentStatus
				}
				appointment.merge(data)

				appointment.useTransaction(trx)
				await appointment.save()
			}

			await ActionLogChanges.saveLogScheduleChanges({
				scheduleChange: schedule,
				scheduleChangedData: {
					status,
					currentStatus
				},
				userLogged,
				trx
			})

			schedule.merge({ status, currentStatus, follow_up_date: formattedFollowUpDate })
			schedule.useTransaction(trx)
			await schedule.save()
		})

		const infosNotification = await Database.query()
			.select(
				'schedules.id',
				'schedules.secure_id as scheduleSecureId',
				'schedules.type_consult as type',
				'schedules.status',
				'schedules.neighborhood',
				'schedules.city',
				'schedules.state',
				'schedules.date_canceled as dateCanceled',
				'schedules.type_canceled',
				'schedules.motive_canceled',
				'schedules.patient_id as patientId',
				'users.email as patientEmail',
				'responsable.email as responsableEmail',
				'user_infos.name as patientName',
				'user_infos.ddd_cell as patientDDDCell',
				'responsable_infos.ddd_cell as responsableDDDCell',
				'user_infos.cell as patientCell',
				'responsable_infos.cell as responsableCell',
				'user_infos.one_signal_key as patientOneSignalKey',
				'responsable_infos.one_signal_key as responsableOneSignalKey',
				'schedule_dates_requests.date as realizationDate',
				'partner_infos.id as idPartner',
				'partner_infos.name as namePartner',
				'partners.email as emailPartner',
				'specialties.name as partnerSpecialty',
				'exams.name as examName',
				'exams.tags as examTags',
			)
			.from('schedules')
			.where('schedules.secure_id', params.id)
			.leftJoin('users', 'users.id', 'schedules.patient_id')
			.leftJoin('user_infos', 'user_infos.user_id', 'users.id')
			.leftJoin('schedule_dates_requests', 'schedule_dates_requests.schedule_id', 'schedules.id')
			.leftJoin('users as partners', 'partners.id', 'schedule_dates_requests.partner_id')
			.leftJoin('user_infos as partner_infos', 'partner_infos.user_id', 'partners.id')
			.leftJoin('users as responsable', 'responsable.id', 'users.parent_id')
			.leftJoin('user_infos as responsable_infos', 'responsable_infos.user_id', 'responsable.id')
			.leftJoin('user_specialties', 'user_specialties.user_id', 'partners.id')
			.leftJoin('specialties', 'specialties.id', 'user_specialties.specialty_id')
			.leftJoin('exams', 'exams.id', 'schedules.exam_id')
			.firstOrFail()

		const scheduleData = {
			patient: {
				id: infosNotification.patientId,
				name: infosNotification.patientName,
				email: infosNotification.responsableEmail ?? infosNotification.patientEmail,
				dddCell: infosNotification.responsableDDDCell ?? infosNotification.patientDDDCell,
				cell: infosNotification.responsableCell ?? infosNotification.patientCell,
				oneSignalKey: infosNotification.responsableOneSignalKey ?? infosNotification.patientOneSignalKey,
			},
			laboratory: {
				name: infosNotification.namePartner,
				exam: infosNotification.examName,
				preparation: infosNotification.partnerSpecialty,
				email: infosNotification.emailPartner
			},
			doctor: {
				id: infosNotification.idPartner,
				name: infosNotification.namePartner,
				specialty: infosNotification.partnerSpecialty,
				email: infosNotification.emailPartner,
			},
			schedule: {
				typeAppointment: infosNotification.type,
				date: infosNotification.realizationDate,
				realizationDate: infosNotification.realizationDate,
				cancelDate: infosNotification.dateCanceled,
			},
			place: {
				state: infosNotification.state,
				neighborhood: infosNotification.neighborhood,
				city: infosNotification.city,
			}
		}

		if (infosNotification.type == 'exam') {
			await NotificationServices.sendNotifications(scheduleData, 'examOptions')
		} else {
			await NotificationServices.sendNotifications(scheduleData, 'consultOptions')
		}

		return response.ok({
			type: 'success',
			message: 'Solicitação de agendamento atualizada com sucesso!',
		})
	}

	public async cancel({ params }: HttpContextContract) {
		await Schedule.query().where('secure_id', params.id).firstOrFail()
	}

	public async createDates({ request, response }: HttpContextContract) {
		const { scheduleSecureId, partnerSecureId, paymentMethods, queryValue, queryValueSubsidy, queryValuePatient, date } = request.all()

		const schedule = await Schedule.query().where('secure_id', scheduleSecureId).firstOrFail()
		const partner = await User.query().where('secure_id', partnerSecureId).firstOrFail()

		await ScheduleDatesRequest.create({
			scheduleId: schedule.id,
			date,
			dateType: 'hour',
			partnerId: partner.id,
			//@ts-expect-error nunca vai ser helloMed
			partnerType: partner.type,
			type: 'backoffice',
			status: 'available',
			paymentMethods,
			queryValue,
			queryValueSubsidy,
			queryValuePatient
		})

		return response.ok({
			type: 'success',
			message: 'Horários atualizados com sucesso!',
		})
	}

	public async updateDates({ request, response }: HttpContextContract) {
		const { scheduleSelectedSecureId, status } = request.all()

		await ScheduleDatesRequest.query().whereIn('secure_id', scheduleSelectedSecureId).update({
			status: status,
		})

		return response.ok({
			type: 'success',
			message: 'Horários atualizados com sucesso!',
		})
	}

	public async showData({ response, params }: HttpContextContract) {
		const partner = await User.query()
			.where('secure_id', params.id)
			.select('id')
			.preload('userInfo', (builder) => {
				builder.select('paymentMethods', 'queryValue', 'queryValueSubsidy', 'queryValuePatient')
			})
			.firstOrFail()

		response.ok(partner)
	}

	public async updateDataForDates({ request, response, params }: HttpContextContract) {
		const { paymentMethods, queryValue, queryValueSubsidy, queryValuePatient } = request.all()

		const queryValueAtt = queryValue * 100
		const queryValueSubsidyAtt = queryValueSubsidy * 100
		const queryValuePatientAtt = queryValuePatient * 100

		await ScheduleDatesRequest.query()
			.where('secure_id', params.id)
			.update({
				paymentMethods,
				queryValue: queryValueAtt,
				queryValueSubsidy: queryValueSubsidyAtt,
				queryValuePatient: queryValuePatientAtt,
			})

		response.ok({
			type: 'success',
			message: 'Dados do horário atualizado com sucesso'
		})
	}

	private async generateFollowUpDate(storedDate?: Date, isInAccreditation?: boolean) {
		// new Date(ano, mês, dia, hora, minuto, segundo, milissegundo);
		// const today = new Date(2025, 3, 15, 20, 33, 33, 0);
		let today

		if (storedDate) {
			today = storedDate
		} else {
			today = new Date();
		}

		let lastMonth = lastDayOfMonth(today);
		const todayDay = getDate(today);

		if (todayDay >= 27) {
			lastMonth = addMonths(lastMonth, 1);
		}

		const holidays = await Holiday.query()
			.whereBetween('date', [today, lastMonth])

		const goalDate = this.incrementDays(today, holidays, isInAccreditation);


		return goalDate;
	}

	private incrementDays(parsedDate: Date, holidays: any, isInAccreditation?: boolean) {
		// const threeDaysLater = addDays(parsedDate, 3)

		// let goalDate = threeDaysLater

		let goalDate = parsedDate

		let isDateWeekend = isWeekend(goalDate)

		let isDateHoliday = holidays.find((holiday: any) => {
			const formattedReceivedDate = moment(goalDate).format("YYYY-MM-DD")
			const formattedHoliday = moment(holiday.date).format("YYYY-MM-DD")
			return formattedReceivedDate === formattedHoliday
		})

		let businessDay = 0

		if (isInAccreditation) {
			while (businessDay !== 3) {
				goalDate = addDays(goalDate, 1)
				isDateWeekend = isWeekend(goalDate)
				isDateHoliday = holidays.find((holiday: any) => moment(goalDate).format("YYYY-MM-DD") === moment(holiday.date).format("YYYY-MM-DD"))
				if (isDateWeekend === false && isDateHoliday === undefined) {
					businessDay += 1
				}
			}
		} else {
			while (businessDay !== 2) {
				goalDate = addDays(goalDate, 1)
				isDateWeekend = isWeekend(goalDate)
				isDateHoliday = holidays.find((holiday: any) => moment(goalDate).format("YYYY-MM-DD") === moment(holiday.date).format("YYYY-MM-DD"))
				if (isDateWeekend === false && isDateHoliday === undefined) {
					businessDay += 1
				}
			}
		}

		// while(isDateWeekend || isDateHoliday) {
		// 	goalDate = addDays(goalDate, 1)
		// 	isDateWeekend = isWeekend(goalDate)
		// 	isDateHoliday = holidays.find((holiday: any) => moment(goalDate).format("YYYY-MM-DD") === moment(holiday.date).format("YYYY-MM-DD"))
		// }

		return goalDate;
	}
}
