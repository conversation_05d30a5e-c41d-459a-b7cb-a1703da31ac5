import type { HttpContextContract } from '@ioc:Adonis/Core/HttpContext'
import { schema } from '@ioc:Adonis/Core/Validator'
import User from 'App/Models/User'

export default class ListPatientsController {
	public async index({ request, response }: HttpContextContract) {
		const schemaValidator = schema.create({
			search: schema.string.optional(),
		})

		const { search } = await request.validate({
			schema: schemaValidator,
		})

		const patients = await User.query()
			.select('id', 'parent_id', 'secure_id')
			.where((builder) => {
				if (search) {
					builder.whereILike('email', `%${search}%`)
					builder.orWhereHas('userInfo', (builderUserInfo) => {
						builderUserInfo.whereILike('name', `%${search}%`)
					})
				}
			})
			.preload('userInfo', (builder) => {
				builder.select('name')
			})
			.whereHas('roles', (builder) => {
				builder.whereIn('slug', ['patient'])
			})
			.andWhereNull('parent_id')

		return response.ok(patients)
	}

	public async listByCPF({ request, response }: HttpContextContract) {
		const cpf = await request.param('cpf')
		const typeOfPatient = await request.param('patient')

		const rawPatients = await User.query()
			.select('id', 'parent_id', 'secure_id', 'email')
			.where((builder) => {
				if (cpf) {
					builder.whereHas('userInfo', (builderUserInfo) => {
						builderUserInfo.where('legal_document_number', 'like', `${cpf}%`)
					})
				}
			})
			.if(typeOfPatient === 'dependent', (whereBuilder) => {
				whereBuilder.preload('dependents', (dependentBuild) => {
					dependentBuild.select('id', 'secure_id').whereNotNull('parent_id')
					dependentBuild.preload('userInfo', (nestedDependentBuilder) => {
						nestedDependentBuilder.select('name')
					})
				})
			})
			.preload('userInfo', (builder) => {
				builder.select(
					'name',
					'legal_document_number',
					'zip_code',
					'street',
					'number',
					'complement',
					'neighborhood',
					'city',
					'state'
				)
			})
			.preload('partners', (builder) => {
				builder.select('id')
					.preload('userInfo', (builder) => {
						builder.select('name')
					})
			})
			.whereHas('roles', (builder) => {
				builder.whereIn('slug', ['patient'])
			})
			.andWhereNull('parent_id')

		const formattedResponse = rawPatients.map((patient) => {
			const data = patient.toJSON()

			const partner = data?.partners?.[0];
			const rawDependents = data?.dependents;

			const patientData = {
				secureId: data.secure_id,

				name: data.userInfo.name,
				email: data.email,
				legalDocument: data.userInfo.legal_document_number,
				partnerName: partner?.userInfo?.name ? partner?.userInfo?.name : undefined,
			}

			const addressData = {
				zipCode: data?.userInfo?.zip_code ? data?.userInfo?.zip_code : undefined,
				street: data.userInfo.street ? data.userInfo.street : undefined,
				streetNumber: data.userInfo.number ? data.userInfo.number : undefined,
				complement: data.userInfo.complement ? data.userInfo.complement : undefined,
				neighborhood: data.userInfo.neighborhood ? data.userInfo.neighborhood : undefined,
				city: data.userInfo.city ? data.userInfo.city : undefined,
				state: data.userInfo.state ? data.userInfo.state : undefined,
			}

			const dependentsData = rawDependents ?
				rawDependents.map((dependent) => {
					return {
						secureId: dependent.secure_id,
						name: dependent.userInfo.name,
					}
				})
				: undefined

			return {
				patientData,
				addressData,
				dependentsData
			}
		})

		return response.ok(formattedResponse)
		// return response.ok({})
	}
}
