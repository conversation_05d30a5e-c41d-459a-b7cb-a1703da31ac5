import Route from '@ioc:Adonis/Core/Route'
import { isPermissions } from 'App/Contants/Permissions'
import { isRoles } from 'App/Contants/Roles'

Route.group(() => {

	Route.post('/onesignal-push', 'V1/Admin/OneSignal/MainController.notificationPush').middleware([
		'auth',`${isRoles(['MASTER','ADMIN'])}`, isPermissions(['partners_create']),
	])

	Route.post('/onesignal-email', 'V1/Admin/OneSignal/MainController.notificationEmail').middleware([
		'auth',`${isRoles(['MASTER','ADMIN'])}`, isPermissions(['partners_create']),
	])
	
})
