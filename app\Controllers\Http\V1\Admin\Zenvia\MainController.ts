import type { HttpContextContract } from '@ioc:Adonis/Core/HttpContext'
import Event from '@ioc:Adonis/Core/Event'

export default class MainController {

public async store({ request, response }: HttpContextContract) {
	const date = request.only(['content','phone'])

	if(!date) {
		return response.badRequest({
			type: 'warning',
			message: 'Error no envio'
		})
	}

	try {
		Event.emit('new:zenviaSms', {
			content: date.content,
			phone: date.phone
		})
	} catch(error) {
		response.badRequest({
			type: 'error',
			message: 'Error'
		})
	}

	response.ok({
		type: 'success',
		message: 'Sms enviado com sucesso'
	})
}
}

