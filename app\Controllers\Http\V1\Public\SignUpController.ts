import type { HttpContextContract } from '@ioc:Adonis/Core/HttpContext'
import Database from '@ioc:Adonis/Lucid/Database'
import Role from 'App/Models/Role'
import User from 'App/Models/User'
import UserInfo from 'App/Models/UserInfo'

import { StoreValidator } from 'App/Validators/Public/SignUp'

export default class SignUpController {
	public async store({ request, response }: HttpContextContract) {
		const { email, password, ...dataInfos } = await request.validate(StoreValidator)

		await Database.transaction(async (trx) => {
			const newUser = new User()
			newUser.merge({
				email,
				password,
			})
			newUser.useTransaction(trx)
			await newUser.save()

			const userInfo = new UserInfo()
			userInfo.merge({
				userId: newUser.id,
				...dataInfos,
			})
			userInfo.useTransaction(trx)
			await userInfo.save()

			const rolesSearch = await Role.query().where('name', 'PATIENT')
			await newUser
				.useTransaction(trx)
				.related('roles')
				.sync(rolesSearch.map((role) => role.id))
		})

		return response.ok({
			type: 'success',
			message: 'Usuário criado com sucesso!',
		})
	}
}
