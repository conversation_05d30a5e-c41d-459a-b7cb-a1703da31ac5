import BaseSchema from '@ioc:Adonis/Lucid/Schema'

export default class extends BaseSchema {
	protected tableName = 'users'

	public async up() {
		this.schema.alterTable(this.tableName, (table) => {
			table.boolean('is_first_access').defaultTo(false).after('show_accredited_in_app')
		})
	}

	public async down() {
		this.schema.alterTable(this.tableName, (table) => {
			table.dropColumn('is_first_access')
		})
	}
}
