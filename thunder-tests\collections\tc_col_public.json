{"_id": "9b339f86-f4b9-4ee2-a15a-6879f2842ff7", "colName": "Public", "created": "2023-05-08T21:39:14.108Z", "sortNum": 105000, "folders": [], "requests": [{"_id": "b68a4d7e-9c05-429a-9133-1b83f3a5cfd1", "colId": "9b339f86-f4b9-4ee2-a15a-6879f2842ff7", "containerId": "", "name": "SignUp", "url": "{{url}}/v1/public/sign-up", "method": "POST", "sortNum": 10000, "created": "2023-04-12T16:53:13.105Z", "modified": "2023-06-20T18:47:24.281Z", "headers": [], "params": [], "body": {"type": "formencoded", "raw": "", "form": [{"name": "email", "value": "<EMAIL>"}, {"name": "name", "value": "<PERSON>"}, {"name": "password", "value": "3z2io23m"}, {"name": "ddd_cell", "value": "14"}, {"name": "cell", "value": "998358805"}]}, "auth": {"type": "bearer", "bearer": "{{token}}"}, "tests": [], "docs": "# Cria usuário do tipo paciente\n\nPara criaçao devem ser enviados os seguintes campos:\nAtributo | Tipo | Descrição\n:------ | :------: | :------\n```email``` <font color='#dd1e2e'>required</font>  | ```string``` | Email do usuário\n```name``` <font color='#dd1e2e'>required</font>  | ```string``` | Nome do usu<PERSON>rio\n```password``` <font color='#dd1e2e'>required</font>| ```string``` | Senha do usuário\n```ddd_cell``` <font color='#dd1e2e'>required</font>| ```number``` | DDD do Celular do usuário\n```cell``` <font color='#dd1e2e'>required</font>| ```number``` | Celular do usuário"}, {"_id": "e460686d-72b0-4fac-bc3f-ffc938974cf5", "colId": "9b339f86-f4b9-4ee2-a15a-6879f2842ff7", "containerId": "", "name": "Attendent", "url": "{{url}}/v1/public/attendants?typeConsult=in_person", "method": "GET", "sortNum": 20000, "created": "2023-06-28T21:31:34.575Z", "modified": "2023-06-28T21:33:17.598Z", "headers": [], "params": [{"name": "typeConsult", "value": "in_person", "isPath": false}], "tests": []}]}