import type { HttpContextContract } from '@ioc:Adonis/Core/HttpContext'
import { schema } from '@ioc:Adonis/Core/Validator'

import User from 'App/Models/User'
import Exam from 'App/Models/Exam'
import Specialty from 'App/Models/Specialty'
import ActionLog from 'App/Models/ActionLog'
import Appointment from 'App/Models/Appointment'
import Schedule from 'App/Models/Schedule'
import Group from 'App/Models/Group'

export default class ActionLogController {
	public async index({ response, request }: HttpContextContract) {
		const schemaValidator = schema.create({
			changedSecureId: schema.string(),
			type: schema.enum(['user', 'specialty', 'exam', 'appointment', 'schedule', 'group'] as const),
			page: schema.number.optional(),
			limit: schema.number.optional()
		})

		const {
			changedSecureId,
			type,
			page = 1,
			limit = 15
		} = await request.validate({
			schema: schemaValidator
		})

		const checkChange = async () => {
			switch (type) {
				case 'exam':
					const exam = await Exam.query()
						.where('secure_id', changedSecureId)
						.first()

					return exam?.id

				case 'specialty':
					const specialty = await Specialty.query()
						.where('secure_id', changedSecureId)
						.first()

					return specialty?.id

				case 'appointment':
					const appointment = await Appointment.query()
						.where('secure_id', changedSecureId)
						.first()

					return appointment?.id

				case 'schedule':
					const schedule = await Schedule.query()
						.where('secure_id', changedSecureId)
						.first()

					return schedule?.id

				case 'group':
					const group = await Group.query()
						.where('secure_id', changedSecureId)
						.first()

					return group?.id

				default:
					const user = await User.query()
						.where('secure_id', changedSecureId)
						.first()

					return user?.id
			}
		}

		const chargedId = await checkChange()

		if (chargedId) {
			const logs = await ActionLog.query()
				.select('id', 'user_id', 'charged_id', 'type', 'date', 'charged_data')
				.where('charged_id', chargedId)
				.andWhere('type', type)
				.preload('user', builderUser => {
					builderUser.select('id', 'secure_id')
					builderUser.preload('userInfo', builderUserInfo => {
						builderUserInfo.select('id', 'name')
					})
				})
				.orderBy('created_at', 'desc')
				.paginate(page, limit)

			return response.ok(logs)
		}

		return response.ok([])
	}
}
