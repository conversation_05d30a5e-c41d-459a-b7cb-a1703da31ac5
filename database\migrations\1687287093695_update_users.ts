import BaseSchema from '@ioc:Adonis/Lucid/Schema'

export default class extends BaseSchema {
	protected tableName = 'users'

	public async up() {
		this.schema.alterTable(this.tableName, (table) => {
			table
				.integer('avatar_id')
				.unsigned()
				.references('id')
				.inTable('uploads')
				.onUpdate('CASCADE')
				.onDelete('RESTRICT')
				.after('parent_id')
		})
	}

	public async down() {
		this.schema.alterTable(this.tableName, (table) => {
			table.dropForeign('avatar_id')
			table.dropColumn('avatar_id')
		})
	}
}
