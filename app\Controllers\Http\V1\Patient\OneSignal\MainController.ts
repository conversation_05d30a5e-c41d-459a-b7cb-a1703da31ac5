import type { HttpContextContract } from '@ioc:Adonis/Core/HttpContext'

export default class MainController {
	public async update({ auth, request, response, logger }: HttpContextContract) {
    const { oneSignalKey } = request.only(['oneSignalKey'])
    const userLogged = auth.user!
    await userLogged.load('userInfo')

    try {
      userLogged.userInfo.merge({
        oneSignalKey: oneSignalKey
      })

      await userLogged.userInfo.save()
      return response.ok({
        message: 'OneSignalKey atualizado com sucesso!'
      })
    } catch (error) {
      logger.error(error)
      return response.badRequest({
        message: 'Erro ao atualizar o onesignalkey'
      })
    }
  }
}
