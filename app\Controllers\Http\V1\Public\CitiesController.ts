import type { HttpContextContract } from '@ioc:Adonis/Core/HttpContext'
import City from 'App/Models/City'
import State from 'App/Models/State'

export default class CitiesController {
	public async indexCities({ request, response }: HttpContextContract) {
		const { search, uf } = request.only(['search', 'uf'])

		const cities = await City.query()
			.where((builder) => {
				if (search) {
					builder.whereILike('name', `%${search}%`)
				}
			})
			.andWhere(builder => {
				if (uf) {
					builder.whereHas('state', builder => {
						builder.where('uf', uf)
					})
				}
			})
			.preload('state')
		return response.ok(cities)
	}

	public async indexStates({ request, response }: HttpContextContract) {
		const { search } = request.only(['search'])

		const states = await State.query().where((builder) => {
			if (search) {
				builder.whereILike('name', `%${search}%`)
			}
		})
		return response.ok(states)
	}
}
