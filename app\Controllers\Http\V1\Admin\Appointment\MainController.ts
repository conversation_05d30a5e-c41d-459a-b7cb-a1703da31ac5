import type { HttpContextContract } from '@ioc:Adonis/Core/HttpContext'
import Database from '@ioc:Adonis/Lucid/Database'

import Appointment from 'App/Models/Appointment'
import City from 'App/Models/City'
import Exam from 'App/Models/Exam'
import Specialty from 'App/Models/Specialty'
import State from 'App/Models/State'
import ActionLogChanges from 'App/Services/ActionLogChanges'

export default class AppointmentController {
	public async index({ request, response, auth }: HttpContextContract) {
		const {
			page = 1,
			limit = 5,
			status,
			currentStatus,
			field = 'created_at',
			direction = 'desc',
			stateUF,
			cityName,
			examOrSpecialtySecureId,
			search,
		} = request.only([
			'page',
			'limit',
			'status',
			'currentStatus',
			'field',
			'direction',
			'stateUF',
			'cityName',
			'examOrSpecialtySecureId',
			'search'
		])
		const userLogged = auth.user!

		await userLogged.load('roles')

		let hasExam: Exam | null;
		let hasSpecialty: Specialty | null;


		if (examOrSpecialtySecureId) {
			hasExam = await Exam.query()
				.where('secure_id', examOrSpecialtySecureId)
				.first();

			hasSpecialty = await Specialty.query()
				.where('secure_id', examOrSpecialtySecureId)
				.first();
		}

		const appointments = await Appointment.query()
			.where((builder) => {
				if (status) {
					builder.andWhereIn('status', status)
				}

				if (currentStatus) {
					builder.andWhereIn('current_status', currentStatus)
				}

				if (stateUF) {
					builder.andWhereHas('schedule', (builderAndWhere) => {
						builderAndWhere.where('state', stateUF)
					})
				}

				if (examOrSpecialtySecureId) {
					if (hasExam) {
						builder.andWhereHas('exam', (examBuilder) => {
							examBuilder.where('id', hasExam?.$attributes.id)
						})
					}

					if (hasSpecialty) {
						builder.andWhereHas('specialty', (specialtyBuilder) => {
							specialtyBuilder.where('id', hasSpecialty?.$attributes.id)
						})
					}
				}

				// if (specialtySecureId) {
				// 	builder.andWhereHas('specialty', (specialtyBuilder) => {
				// 		specialtyBuilder.where('secure_id', specialtySecureId)
				// 	})
				// }

				// if (examSecureId) {
				// 	builder.andWhereHas('exam', (examBuilder) => {
				// 		examBuilder.where('secure_id', examSecureId)
				// 	})
				// }

				if (cityName) {
					builder.andWhereHas('schedule', (scheduleBuilder) => {
						scheduleBuilder.where('city', cityName)
					})
				}

				if (search) {
					builder.andWhereHas('patient', (patientBuilder) => {
						patientBuilder.whereHas('userInfo', (userInfoBuilder) => {
							userInfoBuilder.whereILike('name', `%${search}%`)

							const normalizedSearch = search.replace(/\D/g, '');

							if (normalizedSearch !== '') {
								userInfoBuilder.orWhereRaw(`
									REPLACE(REPLACE(legal_document_number, '.', ''), '-', '') LIKE '%${normalizedSearch}%'
									`)
							}
						})
					})
				}
			})
			// .andWhere((builder) => {
			// 	builder.whereHas('specialty', (builder) => {
			// 		if (specialtySecureId) {
			// 			builder.where('secure_id', specialtySecureId)
			// 		}
			// 	})
			// })
			// .andWhere((builder) => {
			// 	builder.whereHas('exam', (builder) => {
			// 		if (examSecureId) {
			// 			builder.where('secure_id', examSecureId)
			// 		}
			// 	})
			// })
			.preload('specialty', (builder) => {
				builder.select('id', 'name')
			})
			.preload('exam', (builder) => {
				builder.select('id', 'name')
			})
			.preload('patient', (builder) => {
				builder.select('id')
				builder.preload('userInfo', (builderUserInfo) => {
					builderUserInfo.select('name')
				})
			})
			.preload('partner', (builder) => {
				builder.select('id')
				builder.preload('userInfo', (builderUserInfo) => {
					builderUserInfo.select('name')
				})
			})
			.preload('schedule', builder => {
				builder.preload('uploads', (builder) => {
					builder.select('url')
				})
			})
			.orderBy(field, direction)
			.paginate(page, limit)

		return response.ok(appointments)
	}

	public async show({ response, params }: HttpContextContract) {
		const schedule = await Appointment.query()
			.where('secure_id', params.id)
			.preload('specialty', (builder) => {
				builder.select('id', 'name')
			})
			.preload('exam', (builder) => {
				builder.select('id', 'name')
			})
			.preload('patient', (builder) => {
				builder.select('id')
				builder.preload('userInfo', (builderUserInfo) => {
					builderUserInfo.select('name')
				})
			})
			.firstOrFail()
		return response.ok(schedule)
	}

	public async update({ request, response, params, auth }: HttpContextContract) {
		const userLogged = auth.user!
		const { status, currentStatus } = request.only(['status', 'currentStatus'])

		await userLogged.load('roles')

		await Database.transaction(async (trx) => {
			const appointment = await Appointment.query()
				.where('secure_id', params.id)
				.firstOrFail()

			await ActionLogChanges.saveLogAppointmentChanges({
				appointmentChange: appointment,
				appointmentChangedData: {
					status,
					currentStatus
				},
				userLogged,
				trx
			})

			appointment.merge({
				status,
				currentStatus
			})
			appointment.useTransaction(trx)
			await appointment.save()
		})

		return response.ok({
			type: 'success',
			message: 'Agendamento atualizado com sucesso!',
		})
	}

	public async cancel({ params }: HttpContextContract) {
		await Appointment.query().where('secure_id', params.id).firstOrFail()
	}

	public async dependencies({ response }: HttpContextContract) {
		const specialties = await Specialty.query()
			.select('secure_id', 'name')
			.orderBy('name')

		const exams = await Exam.query()
			.select('secure_id', 'name')
			.orderBy('name')

		const states = await State.query()
			.select('name', 'uf')

		return response.ok({
			exams,
			specialties,
			states
		})
	}

	public async getDependencyCityByStateUF({ response, params }: HttpContextContract) {
		const stateUF = params.stateUF

		const rawState = await State.query()
			.select('id')
			.where('uf', stateUF)
			.first()

		const stateID = rawState?.$attributes.id

		const cities = await City.query()
			.select('name')
			.where('state_id', stateID)

		return response.ok({
			cities: cities
		})
	}

	public async getAppointmentObservationsByAppointmentSecureID({ response, params }: HttpContextContract) {
		const appointmentSecureId = await params.appointmentSecureId;

		const rawAppointmentObservations = await Appointment.query()
			.select('id', 'schedule_id', 'secure_id')
			.where('secure_id', appointmentSecureId)
			.preload('schedule', (builder) => {
				builder.select('id', 'secure_id')
					.preload('observations', (observationBuilder) => {
						observationBuilder.select('secure_id', 'observation', 'created_at')
							.where('is_active', true)
					})
			}).first()

		const appointmentObservations = rawAppointmentObservations?.toJSON();

		return response.ok({
			appointmentObservations: appointmentObservations
		})
	}
}
