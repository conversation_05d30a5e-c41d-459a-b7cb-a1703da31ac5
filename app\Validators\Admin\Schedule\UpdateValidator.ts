import { schema, rules, CustomMessages } from '@ioc:Adonis/Core/Validator'
import { HttpContextContract } from '@ioc:Adonis/Core/HttpContext'

export default class StoreValidator {
	constructor(protected ctx: HttpContextContract) { }

	public schema = schema.create({
		status: schema.enum.optional
			([
				'in_schedule',
				'waiting_backoffice',
				'waiting_backoffice_network',
				'budget',
				'waiting_patient',
				'approved',
				'canceled',
				'canceled_by_patient',
				'canceled_at_patient_request',
				'canceled_by_backoffice',
				"in_accreditation",
				"no_contact",
				"no_interest",
				"lack_request",
				"info_divergence",
				"financial_condition",
				"no_interest_accreditation"
			] as const),
		scheduleDateRequestSecureId: schema.string.optional([
			rules.exists({
				table: 'schedule_dates_requests',
				column: 'secure_id',
			}),
			rules.requiredWhen('status', '=', 'approved')
		]),
	})

	private getFields = (field) => {
		const fieldI18N = this.ctx.i18n.formatMessage(`fields.${field}`)

		const isI18N = !fieldI18N.match(/translation/)

		if (isI18N && fieldI18N) {
			return fieldI18N
		} else {
			return field
		}
	}

	private getValidator = (rule, field) => {
		const fieldI18N = this.ctx.i18n.formatMessage(`validator.shared.${field}.${rule}`)
		const isI18N = fieldI18N.match(/translation/)

		if (isI18N === null && fieldI18N) {
			return `validator.shared.${field}.${rule}`
		} else {
			return `validator.shared.${rule}`
		}
	}

	public messages: CustomMessages = {
		'*': (field, rule, arrayExpressionPointer, options) => {
			try {
				return this.ctx.i18n.formatMessage(this.getValidator(rule, field), {
					field: this.getFields(field),
				})
			} catch (_) {
				return this.ctx.i18n.validatorMessages()['*'](field, rule, arrayExpressionPointer, options)
			}
		},
	}
}
