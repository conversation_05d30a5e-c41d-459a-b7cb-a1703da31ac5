import type { HttpContextContract } from '@ioc:Adonis/Core/HttpContext'
import Database from '@ioc:Adonis/Lucid/Database'
import Role from 'App/Models/Role'
import Upload from 'App/Models/Upload'
import User from 'App/Models/User'
import UserInfo from 'App/Models/UserInfo'

import { StoreValidator, UpdateValidator } from 'App/Validators/Admin/Dependent'

export default class DependentController {
	public async index({ response, request, auth }: HttpContextContract) {
		const {
			page = 1,
			limit = 15,
			parent,
		} = request.only(['page', 'limit', 'parent'])
		const userLogged = auth.user!

		if (!parent) return response.ok([])

		const parentData = await User.query()
			.where('secure_id', parent)
			.andWhere('type', 'patient')
			.andWhereNot('deleted', true)
			.andWhereNot('id', userLogged.id)
			.firstOrFail()

		const dependents = await User.query()
			.select('users.id', 'users.parent_id', 'users.secure_id')
			.where('parent_id', parentData.id)
			.where('type', 'dependent')
			.whereNot('deleted', true)
			.where('is_active', true)
			.preload('userInfo')
			.join('user_infos', 'user_infos.user_id', 'users.id')
			.orderBy('user_infos.name', 'asc')
			.paginate(page, limit)

		return response.ok(dependents)
	}

	public async store({ request, response }: HttpContextContract) {
		const { parentSecureId, avatarSecureId, userExists, email, password, ...dataUserInfo } =
			await request.validate(StoreValidator)

		const user = await User.query().where('email', email).preload('roles').first()

		if (userExists && user && user.roles.find((role) => role.name === 'PATIENT')) {
			return response.badRequest({
				type: 'warning',
				message: 'Esse usuário já foi cadastrado como paciente!',
			})
		}

		const parent = await User.query()
			.where('secure_id', parentSecureId)
			.andWhereNull('parent_id')
			.firstOrFail()

		await Database.transaction(async (trx) => {
			if (userExists && user) {
				const role = await Role.query().where('name', 'PATIENT').firstOrFail()

				if (!user.roles.find((role) => role.name === 'PATIENT')) {
					await user.useTransaction(trx).related('roles').attach([role.id])
				}

				user.merge({
					parentId: parent.id,
					type: 'dependent',
				})
				user.useTransaction(trx)
				await user.save()
			} else {
				const newUser = new User()

				if (avatarSecureId) {
					const avatar = await Upload.query().where('secure_id', avatarSecureId).firstOrFail()

					newUser.merge({
						avatarId: avatar.id,
					})
				}

				newUser.merge({
					// email: `${email}#${String(Math.floor(Date.now() / 1000))}`,
					email,
					password,
					parentId: parent.id,
					type: 'dependent',
				})
				newUser.useTransaction(trx)
				await newUser.save()

				const newUserInfo = new UserInfo()
				newUserInfo.merge({
					...dataUserInfo,
					userId: newUser.id,
				})
				newUserInfo.useTransaction(trx)
				await newUserInfo.save()

				const rolesSearch = await Role.query().where('name', 'PATIENT')
				await newUser
					.useTransaction(trx)
					.related('roles')
					.sync(rolesSearch.map((role) => role.id))
			}
		})

		return response.ok({
			type: 'success',
			message: 'Dependente criado com sucesso!',
		})
	}

	public async show({ response, params }: HttpContextContract) {
		const patient = await User.query()
			.select('id', 'parent_id', 'avatar_id', 'secure_id', 'email')
			.where('secure_id', params.id)
			.andWhereHas('roles', (builder) => {
				builder.whereIn('slug', ['patient'])
			})
			.andWhereNotNull('parent_id')
			.preload('parent', (builderParent) => {
				builderParent.select('id', 'secure_id')
				builderParent.preload('userInfo')
			})
			.preload('avatar', (builderAvatar) => {
				builderAvatar.select('id', 'secure_id', 'url', 'name')
			})
			.andWhere('type', 'dependent')
			.andWhereNot('deleted', true)
			.preload('userInfo')
			.firstOrFail()

		return response.ok(patient)
	}

	public async update({ response, request, params }: HttpContextContract) {
		const { avatarSecureId, email, ...dataUser } = await request.validate(UpdateValidator)

		const user = await User.query()
			.where('secure_id', params.id)
			.andWhereHas('roles', (builder) => {
				builder.whereIn('slug', ['patient'])
			})
			.andWhere('type', 'dependent')
			.andWhereNot('deleted', true)
			.preload('userInfo')
			.firstOrFail()

		await Database.transaction(async (trx) => {
			if (avatarSecureId) {
				const avatar = await Upload.query().where('secure_id', avatarSecureId).firstOrFail()

				user.merge({
					avatarId: avatar.id,
				})
			}

			user.merge({
				email,
			})
			user.useTransaction(trx)
			await user.save()

			const userInfo = user.userInfo
			userInfo.merge({ ...dataUser })
			userInfo.useTransaction(trx)
			await userInfo.save()
		})

		return response.ok({
			type: 'success',
			message: 'Dependente atualizado com sucesso!',
		})
	}

	public async destroy({ response, params }: HttpContextContract) {
		const patient = await User.query()
			.where('secure_id', params.id)
			.andWhereHas('roles', (builder) => {
				builder.whereIn('slug', ['patient'])
			})
			.andWhereNotNull('parent_id')
			.firstOrFail()

		patient.merge({ deleted: true })
		await patient.save()

		return response.ok({
			type: 'success',
			message: 'Dependente removido com sucesso!',
		})
	}
}
