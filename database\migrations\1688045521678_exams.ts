import BaseSchema from '@ioc:Adonis/Lucid/Schema'

export default class extends BaseSchema {
	protected tableName = 'exams'

	public async up() {
		this.schema.createTable(this.tableName, (table) => {
			table.increments('id')
			table.string('secure_id')
			table
				.integer('thumb_id')
				.unsigned()
				.references('id')
				.inTable('uploads')
				.onUpdate('CASCADE')
				.onDelete('RESTRICT')
			table.string('name')
			table.text('tags')
			table.boolean('active').defaultTo(true)

			table.dateTime('created_at', { useTz: true })
			table.dateTime('updated_at', { useTz: true })
		})
	}

	public async down() {
		this.schema.dropTable(this.tableName)
	}
}
