# Hello-Med Gestão API

### Holiday Commander
Existe um comando para popular os dados da tabela **holidays**.
<PERSON><PERSON> de rodar o comando, certifique-se de que o arquivo: __raw-holiday.json__ existe na raiz do projeto. O arquivo pode ser baixado no [Git](https://gist.github.com/sistematico/0d795e73e133632204593f1d1db4a618).
O arquivo também deve ter o seguinte formato:
```json
{
  "2001-01-01": "Confraternização Universal",
  "2001-02-27": "Carnaval",
  "2001-04-13": "Paixão de Cristo",
  "2001-04-21": "Tiradentes",
  "2001-05-01": "Dia do Trabalho",
  "2001-06-14": "Corpus Christi",
  "2001-09-07": "Independência do Brasil",
  "2001-10-12": "Nossa Sr.a Aparecida - Padroeira do Brasil",
  "2001-11-02": "Finados",
  "2001-11-15": "Proclamação da República",
  "2001-12-25": "Natal"
}
```

Para executar o comando digite:
```bash
node ace create:holidays
```

Workflow:
* Pega o arquivo __raw-holiday.json__ e converte para uma estrutura de dados no formato:
```typescript
type HolidayJsonData = {
	date: Date;
	description: string;
}[]
```
* Após realizar a conversão ele roda um loop no array e popula a tabela.

Caso necessário gerar um arquivo com os dados convertidos, existe um método, privado, dentro do commander chamado de: __writeJsonToFile__ que irá gerar um arquivo json e irá salvar ele na raiz do projeto. O arquivo se chama: __holidays.json__. Caso necessário alterar o nome do arquivo isso pode ser feito alterando a linha:
```typescript
// Caso necessário converter para um arquivo json usar a linha de baixo
// this.writeJsonToFile('holidays.json', holidaysFormatted);
```

**Importante!**: Lembre-se de conferir se o arquivo existe na raiz do projeto! Caso não exista faça o [download](https://gist.github.com/sistematico/0d795e73e133632204593f1d1db4a618) dele.
