import type { ApplicationContract } from '@ioc:Adonis/Core/Application'
import Env from '@ioc:Adonis/Core/Env'
import amqplib from 'amqplib'
import ProcessPatientImportResponse from 'App/Services/RabbitQueue/ProcessPatientImportResponse'

export default class RabbitProvider { // TODO: Consertar o B.O aqui
	constructor(protected app: ApplicationContract) { }

	public register() {
		this.app.container.singleton('Rabbit', async () => {
			const conn = await amqplib.connect(Env.get('CLOUD_AMQP_URL'))
			return conn
		})
	}

	public async boot() {
		this.app.container.singleton('RabbitChannel', async () => {
			const conn = await this.app.container.resolveBinding('Rabbit')
			const channel = await conn.createChannel()
			return channel
		})
	}

	public async ready() {
		if (this.app.environment !== 'web') {
			return
		}
		const channel = await this.app.container.resolveBinding('RabbitChannel')

		const queuePatientImport = 'patient_import_queue'

		// Import patients
		await channel.assertQueue(queuePatientImport, {
			durable: true
		})
		channel.prefetch(1)

		await channel.consume(queuePatientImport, async (msg) => {
			const jobId = JSON.parse(msg.content.toString())
			console.log("jobId DENTRO DO CONSUME:", jobId)

			await ProcessPatientImportResponse(jobId)

			channel.ack(msg)
		})
	}

	public async shutdown() {
		const conn = await this.app.container.resolveBinding('Rabbit')
		const channel = await this.app.container.resolveBinding('RabbitChannel')
		channel.close()
		conn.close()
	}
}
