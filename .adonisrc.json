{"typescript": true, "commands": ["./commands", "@adonisjs/core/build/commands/index.js", "@adonisjs/repl/build/commands", "@adonisjs/lucid/build/commands", "@adonisjs/mail/build/commands", "adonis5-swagger/build/commands"], "exceptionHandlerNamespace": "App/Exceptions/Handler", "aliases": {"App": "app", "Config": "config", "Database": "database", "Contracts": "contracts"}, "preloads": ["./start/routes", "./start/kernel", {"file": "./start/validator", "environment": ["web"]}, "./start/events"], "providers": ["./providers/AppProvider", "@adonisjs/core", "@adonisjs/lucid", "@adonisjs/auth", "@adonisjs/i18n", "@adonisjs/view", "@adonisjs/drive-s3", "@adonisjs/mail", "adonis5-swagger", "./providers/RabbitProvider"], "aceProviders": ["@adonisjs/repl"], "tests": {"suites": [{"name": "functional", "files": ["tests/functional/**/*.spec(.ts|.js)"], "timeout": 60000}]}, "testProviders": ["@japa/preset-adonis/TestsProvider"], "metaFiles": ["resources/lang/**/*.(json|yaml)", {"pattern": "resources/views/**/*.edge", "reloadServer": false}]}