import BaseSchema from '@ioc:Adonis/Lucid/Schema'

export default class extends BaseSchema {
	protected tableName = 'schedules'

	public async up() {
		this.schema.createTable(this.tableName, (table) => {
			table.increments('id')
			table.string('secure_id')
			table
				.integer('user_id')
				.unsigned()
				.references('id')
				.inTable('users')
				.onUpdate('CASCADE')
				.onDelete('RESTRICT')
			table
				.integer('patient_id')
				.unsigned()
				.references('id')
				.inTable('users')
				.onUpdate('CASCADE')
				.onDelete('RESTRICT')
			table
				.integer('specialty_id')
				.unsigned()
				.references('id')
				.inTable('specialties')
				.onUpdate('CASCADE')
				.onDelete('RESTRICT')
			table
				.integer('exam_id')
				.unsigned()
				.references('id')
				.inTable('exams')
				.onUpdate('CASCADE')
				.onDelete('RESTRICT')
			table
				.integer('user_by_canceled_id')
				.unsigned()
				.references('id')
				.inTable('users')
				.onUpdate('CASCADE')
				.onDelete('RESTRICT')
			table.enu('type_consult', ['in_person', 'video_call', 'exam'])
			table.enu('status', ['waiting_backoffice', 'waiting_patient', 'approved', 'canceled'])
			table.string('neighborhood')
			table.string('city')
			table.string('state')
			table.dateTime('date_canceled', { useTz: true })
			table.enu('type_canceled', ['patient', 'backoffice'])
			table.text('motive_canceled')

			table.dateTime('created_at', { useTz: true })
			table.dateTime('updated_at', { useTz: true })
		})
	}

	public async down() {
		this.schema.dropTable(this.tableName)
	}
}
