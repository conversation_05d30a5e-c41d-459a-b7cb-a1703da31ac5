import type { HttpContextContract } from '@ioc:Adonis/Core/HttpContext'
import User from 'App/Models/User'
import Database from '@ioc:Adonis/Lucid/Database'
import Upload from 'App/Models/Upload'
import ActionLogChanges from 'App/Services/ActionLogChanges'
import { UpdateValidator } from 'App/Validators/Partner/Patient'

// import { StoreValidator, UpdateValidator } from 'App/Validators/Admin/Patient'

type PatientDatabaseDTO = {
  secure_id: string;
  email: string;
  userInfo: {
    name: string;
    ddd_cell: string;
    cell: string;
    legal_document_number: string;
    birth_date: string;
  }

  isActive: boolean;

  partners: PartnerDatabaseDTO[];
}

type PartnerDatabaseDTO = {
  secure_id: string;
  userInfo: {
    name: string
  }
}

export default class PartnerPatientsController {
  public async index({ response, request, auth }: HttpContextContract) {
    const { page = 1, limit = 15, search, status } = request.only(['page', 'limit', 'search', 'status'])

    const userLogged = auth.user!

    const rawPatients = await User.query()
      .whereHas('partners', (partnerBuilder) => {
        partnerBuilder.where('partner_id', userLogged.id)
      })
      // .andWhere('is_active', true)
      .select('users.id', 'users.secure_id', 'users.email', 'users.is_active')
      .whereHas('roles', (builder) => {
        builder.whereIn('slug', ['patient'])
      })
      .andWhere((builder) => {
        if (status === 'active') {
          builder.andWhere('users.is_active', true)
        } else if (status === 'inactive') {
          builder.andWhere('users.is_active', false)
        }

        if (search) {
          builder.whereRaw(`LOWER(users.email) like LOWER('%${search}%')`)
          builder.orWhereHas('userInfo', builder => {
            builder.whereRaw(`LOWER(name) like LOWER('%${search}%')`)
            builder.orWhereRaw(`LOWER(legal_document_number) like LOWER('%${search}%')`)
            builder.orWhereRaw(`LOWER(zip_code) like LOWER('%${search}%')`)
            builder.orWhere(builder => {
              builder.whereRaw(`LOWER(CONCAT(LOWER(ddd_cell), '', LOWER(cell))) LIKE LOWER(?)`, [`%${search}%`]);
              builder.orWhereRaw(`LOWER(CONCAT(LOWER(ddd_cell), ' ', LOWER(cell))) LIKE LOWER(?)`, [`%${search}%`]);
            })
          })

          if (status === 'active') {
            builder.andWhere('users.is_active', true)
          } else if (status === 'inactive') {
            builder.andWhere('users.is_active', false)
          }
        }
      })
      .preload('partners', (partnerBuilder) => {
        partnerBuilder
          .select('secure_id', 'id')
          .preload('userInfo', (nestedBuilder) => {
            nestedBuilder.select('name')
          }
          )
      })
      .andWhereNot('users.id', userLogged.id)
      .andWhere('users.type', 'patient')
      .andWhereNot('users.deleted', true)
      .preload('userInfo', builder => {
        builder.select('id', 'name', 'ddd_cell', 'cell', 'legal_document_number', 'birth_date')
      })
      .join('user_infos', 'user_infos.user_id', 'users.id')
      .orderBy('user_infos.name', 'asc')
      .paginate(page, limit)

    const serializedPatients = rawPatients.serialize();

    const meta = serializedPatients.meta;
    const patients = serializedPatients.data.map((patient: PatientDatabaseDTO) => {
      const firstPartner = patient.partners[0];

      const partner = firstPartner && firstPartner?.secure_id ? {
        secureId: firstPartner?.secure_id,
        name: firstPartner?.userInfo?.name,
      } : undefined;

      return {
        secure_id: patient.secure_id,
        email: patient?.email,
        userInfo: {
          name: patient?.userInfo?.name,
          ddd_cell: patient?.userInfo?.ddd_cell,
          cell: patient?.userInfo?.cell,
          legal_document_number: patient?.userInfo?.legal_document_number,
          birthDate: patient?.userInfo?.birth_date,
        },
        isActive: patient.isActive,
        partner: partner,
      }
    })

    return response.ok({ meta: meta, data: patients })
  }

  public async show({ response, params }: HttpContextContract) {
    const patient = await User.query()
      .select('id', 'avatar_id', 'secure_id', 'email', 'is_active')
      .where('secure_id', params.id)
      .andWhereHas('roles', (builder) => {
        builder.whereIn('slug', ['patient'])
      })
      .andWhere('type', 'patient')
      .andWhereNot('deleted', true)
      .preload('userInfo')
      .preload('avatar', (builderAvatar) => {
        builderAvatar.select('id', 'secure_id', 'url', 'name')
      })
      .preload('dependents', dependentBuilder => {
        dependentBuilder.select('id', 'secure_id', 'email')
        dependentBuilder.preload('userInfo', (nestedBuilder) => {
          nestedBuilder.select('name', 'legal_document_number', 'birth_date')
        })
      })
      .preload('partners', builderPartner => {
        builderPartner.select('id', 'avatar_id', 'secure_id', 'email')
        builderPartner.preload('userInfo')
      })
      .firstOrFail()

    return response.ok(patient)
  }

  public async update({ response, request, params, auth }: HttpContextContract) {
    const userLogged = auth.user!

    const dataRequest = await request.validate(UpdateValidator)

    const {
      avatarSecureId,
      email,
      password,
      isActive,
      ...dataUser
    } = dataRequest

    const user = await User.query()
      .where('secure_id', params.id)
      .andWhereHas('partners', (partnerBuilder) => {
        partnerBuilder.where('partner_id', userLogged.id)
      })
      .andWhereHas('roles', (builder) => {
        builder.whereIn('slug', ['patient'])
      })
      .andWhereNull('parent_id')
      .andWhereNot('deleted', true)
      .preload('userInfo')
      .preload('partners')
      .firstOrFail()

    await Database.transaction(async (trx) => {
      await ActionLogChanges.saveLogUserChanges({
        userChange: user,
        userChangedData: {
          ...dataRequest
        },
        userLogged,
        trx
      })

      if (avatarSecureId) {
        const avatar = await Upload.query().where('secure_id', avatarSecureId).firstOrFail()

        user.merge({
          avatarId: avatar.id,
        })
      }

      user.merge({ email, password, is_active: isActive })
      user.useTransaction(trx)
      await user.save()

      const userInfo = user.userInfo
      userInfo.merge({ ...dataUser })
      userInfo.useTransaction(trx)
      await userInfo.save()

      // if (partnerSecureId) {
      //   const partner = await User.query()
      //     .where('secure_id', partnerSecureId)
      //     .firstOrFail()

      //   if (user.partners[0] && user.partners[0].id !== partner.id) {
      //     await user.useTransaction(trx).related('partners').detach([user.partners[0].id])
      //   }

      //   await partner.useTransaction(trx).related('patients').attach([user.id])
      // }
    })

    return response.ok({
      type: 'success',
      message: 'Paciente atualizado com sucesso!',
    })
  }

  public async destroy({ response, params, auth }: HttpContextContract) {
    const userLogged = auth.user!

    const partner = await User.query()
      .where('secure_id', userLogged.secureId)
      .andWhereHas('roles', (builder) => {
        builder.whereIn('slug', ['partner'])
      })
      .preload('patients')
      .first()

    if (!partner) {
      return response.notFound({
        type: 'warning',
        message: 'Parceiro não encontrado',
      })
    }

    const patient = await User.query()
      .whereHas('partners', (partnerBuilder) => {
        partnerBuilder.where('partner_id', userLogged.id)
      })
      .where('secure_id', params.id)
      .andWhereHas('roles', (builder) => {
        builder.whereIn('slug', ['patient'])
      })
      .andWhere('type', 'patient')
      .firstOrFail()

    await partner.related('patients').detach([patient.id])
    // patient.merge({ deleted: true })
    // await patient.save()

    return response.ok({
      type: 'success',
      message: 'Paciente desvinculado com sucesso!',
    })
  }
}
