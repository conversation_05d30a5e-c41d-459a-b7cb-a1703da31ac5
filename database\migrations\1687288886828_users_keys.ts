import BaseSchema from '@ioc:Adonis/Lucid/Schema'

export default class UsersKeys extends BaseSchema {
	protected tableName = 'users_keys'

	public async up() {
		this.schema.createTable(this.tableName, (table) => {
			table.increments('id')
			table
				.integer('user_id')
				.unsigned()
				.references('id')
				.inTable('users')
				.onUpdate('CASCADE')
				.onDelete('CASCADE')
			table.string('token').notNullable()
			table.dateTime('expires_token_date').notNullable()

			table.dateTime('created_at', { useTz: true })
			table.dateTime('updated_at', { useTz: true })
		})
	}

	public async down() {
		this.schema.dropTable(this.tableName)
	}
}
