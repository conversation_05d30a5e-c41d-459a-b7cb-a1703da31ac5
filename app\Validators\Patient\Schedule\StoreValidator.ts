import { schema, rules, CustomMessages } from '@ioc:Adonis/Core/Validator'
import { HttpContextContract } from '@ioc:Adonis/Core/HttpContext'

export default class StoreValidator {
	constructor(protected ctx: HttpContextContract) { }

	public schema = schema.create({
		typeConsult: schema.enum(['in_person', 'video_call', 'exam'] as const),
		specialtyOrExamSecureId: schema.string([
			rules.exists({
				table: this.ctx.request.body().typeConsult === 'exam' ? 'exams' : 'specialties',
				column: 'secure_id',
			}),
		]),
		patientSecureId: schema.string([
			rules.exists({
				table: 'users',
				column: 'secure_id',
			}),
		]),
		attendantType: schema.enum(['helloMed', 'doctor', 'clinic', 'lab'] as const),
		partnerSecureId: schema.string.optional([
			rules.requiredWhen('attendantType', '!=', 'helloMed'),
		]),
		local: schema.object().members({
			neighborhood: schema.string(),
			city: schema.string(),
			state: schema.string(),
		}),
		dates: schema.array().members(
			schema.object().members({
				date: schema.date({ format: 'yyyy-MM-dd HH:mm:ss' }),
				type: schema.enum(['hour', 'period'] as const),
				value: schema.enum.optional(['morning', 'afternoon', 'night'] as const, [
					rules.requiredWhen('type', '=', 'period'),
				]),
			})
		),
		imagesLabs: schema.array().members(schema.string())
	})

	private getFields = (field) => {
		const fieldI18N = this.ctx.i18n.formatMessage(`fields.${field}`)

		const isI18N = !fieldI18N.match(/translation/)

		if (isI18N && fieldI18N) {
			return fieldI18N
		} else {
			return field
		}
	}

	private getValidator = (rule, field) => {
		const fieldI18N = this.ctx.i18n.formatMessage(`validator.shared.${field}.${rule}`)
		const isI18N = fieldI18N.match(/translation/)

		if (isI18N === null && fieldI18N) {
			return `validator.shared.${field}.${rule}`
		} else {
			return `validator.shared.${rule}`
		}
	}

	public messages: CustomMessages = {
		'*': (field, rule, arrayExpressionPointer, options) => {
			try {
				return this.ctx.i18n.formatMessage(this.getValidator(rule, field), {
					field: this.getFields(field),
				})
			} catch (_) {
				return this.ctx.i18n.validatorMessages()['*'](field, rule, arrayExpressionPointer, options)
			}
		},
	}
}
