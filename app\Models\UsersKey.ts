import { DateTime } from 'luxon'
import { BaseModel, BelongsTo, belongsTo, column } from '@ioc:Adonis/Lucid/Orm'
import User from './User'

export default class Users<PERSON>ey extends BaseModel {
	@column({ isPrimary: true, serializeAs: null })
	public id: number

	@column({ serializeAs: null })
	public userId: number

	@column()
	public token: string

	@column.dateTime({
		serialize: (value: DateTime) => {
			if (value) return value.toFormat('yyyy-MM-dd HH:mm:ss').replace(/\s+/g, 'T')
			return
		},
	})
	public expiresTokenDate: DateTime

	@belongsTo(() => User)
	public user: BelongsTo<typeof User>

	@column.dateTime({
		autoCreate: true,
		serialize: (value: DateTime) => {
			return value.toFormat('yyyy-MM-dd HH:mm:ss').replace(/\s+/g, 'T')
		},
	})
	public createdAt: DateTime

	@column.dateTime({
		autoCreate: true,
		autoUpdate: true,
		serialize: (value: DateTime) => {
			if (value) return value.toFormat('yyyy-MM-dd HH:mm:ss').replace(/\s+/g, 'T')
			return
		},
	})
	public updatedAt: DateTime
}
