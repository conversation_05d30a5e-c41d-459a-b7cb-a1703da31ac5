import type { HttpContextContract } from '@ioc:Adonis/Core/HttpContext'
import { schema, rules } from '@ioc:Adonis/Core/Validator'
import Upload from 'App/Models/Upload'
import Env from '@ioc:Adonis/Core/Env'
import { especialCharMask } from 'App/Utils/especialCharMask'

export default class UploadController {
	public async index({ request, response, auth }: HttpContextContract) {
		const validate = schema.create({
			page: schema.number.optional([rules.trim()]),
			limit: schema.number.optional([rules.trim()]),
			type: schema.enum.optional(['image', 'pdf'] as const),
			search: schema.string.optional([rules.trim()]),
		})
		const {
			page = 1,
			limit = 15,
			type,
			search,
		} = await request.validate({
			schema: validate,
		})

		const userLogged = auth.user!
		await userLogged.load('roles')
		const userIsNotAdmin = !userLogged.roles.find((role) => role.name === 'ADMIN')

		const uploads = await Upload.query()
			.select('secure_id', 'url', 'name')
			.whereHas('user', (builderUser) => {
				if (userIsNotAdmin) builderUser.where('secure_id', userLogged.secureId)
			})
			.andWhere((builder) => {
				if (type) {
					builder.where('type', type)
				}
			})
			.andWhere((builder) => {
				if (search) {
					builder.whereILike('name', `%${search}%`)
				}
			})
			.orderBy('created_at', 'desc')
			.paginate(page, limit)

		return response.ok(uploads)
	}

	public async store({ response, request, auth }: HttpContextContract) {
		const userLogged = auth.user!
		const UploadFileValidator = schema.create({
			file: schema.file({
				size: '100mb',
				extnames: ['jpg', 'png', 'jpeg', 'pdf', 'mp3', 'wma', 'ogg', 'aac', 'wav', 'alac', 'flac'],
			}),
			name: schema.string([rules.trim()]),
		})
		const { file, name } = await request.validate({
			schema: UploadFileValidator,
		})

		const fileName = `${Date.now()}-${especialCharMask(name)}.${file.subtype}`
		const type = file.type === 'application' ? 'pdf' : file.type === 'image' ? 'image' : 'image'

		await file.moveToDisk(
			`files/${userLogged.secureId}/${type}`,
			{
				name: fileName,
			},
			's3'
		)

		const upload = await Upload.create({
			name,
			fileName,
			type,
			url: `${Env.get('S3_CDN')}/${file.fileName}`,
			fileType: file.subtype,
			bucket: Env.get('S3_BUCKET'),
			userId: userLogged.id,
		})

		return response.ok({
			uploadSecureId: upload.secureId,
		})
	}

	public async show({ response, params, auth }: HttpContextContract) {
		const userLogged = auth.user!
		await userLogged.load('roles')
		const userIsNotAdmin = !userLogged.roles.find((role) => role.name === 'ADMIN')

		const file = await Upload.query()
			.whereHas('user', (builderUser) => {
				if (userIsNotAdmin) builderUser.where('secure_id', userLogged.secureId)
			})
			.andWhere('secure_id', params.id)
			.firstOrFail()

		return response.ok(file)
	}
}
