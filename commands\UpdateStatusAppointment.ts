import { BaseCommand, flags } from '@adonisjs/core/build/standalone'

export default class UpdateStatusAppointment extends BaseCommand {
	public static commandName = 'update:status_appointment'

	public static title = 'Atualiza os status de agendamentos'
	public static description = 'Atualiza os status de agendamentos e solicitação de agendamentos de aguardando para aprovado e cancelados para cancelados pelo paciente.'

	@flags.boolean()
	public confirmation: boolean = true

	public static settings = {
		loadApp: true,
		stayAlive: false,
	}

	public async run() {
		const { default: Appointment } = await import('App/Models/Appointment')

		try {
			if (this.confirmation) {
				this.ui
					.sticker()
					.add(this.colors.bold(this.colors.cyan(UpdateStatusAppointment.title)))
					.add('')
					.add(this.colors.gray(UpdateStatusAppointment.description))
					.render()

				const confirm = await this.prompt.confirm('Deseja continuar?')

				if (!confirm) return
			}

			await Appointment.query()
				.where('status', 'waiting')
				.update({
					status: 'approved'
				})

			await Appointment.query()
				.where('status', 'canceled')
				.update({
					status: 'canceled_by_patient'
				})

			this.logger.log(
				`${this.colors.green('Agendamentos atualizados com sucesso!')} ${this.ui.icons.tick}`
			)
		} catch (error) {
			this.logger.logError(error)
			this.logger.log(
				`${this.colors.red('Ocorreu um erro ao atualizar agendamentos')} ${this.ui.icons.cross}`
			)
		}
	}
}
