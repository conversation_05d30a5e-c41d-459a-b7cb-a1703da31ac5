import Route from '@ioc:Adonis/Core/Route'
import { isPermissions } from 'App/Contants/Permissions'
import { isRoles } from 'App/Contants/Roles'

Route.group(() => {
	Route.resource('patients', 'V1/Admin/Patient/MainController').middleware({
		index: ['auth', `${isRoles(['MASTER', 'ADMIN'])}`, isPermissions(['patients_view'])],
		store: ['auth', `${isRoles(['MASTER', 'ADMIN'])}`, isPermissions(['patients_create'])],
		update: ['auth', `${isRoles(['MASTER', 'ADMIN'])}`, isPermissions(['patients_edit'])],
		destroy: ['auth', `${isRoles(['MASTER', 'ADMIN'])}`, isPermissions(['patients_delete'])],
	})

	Route.delete('patients-inactivate/:patientSecureId', 'V1/Admin/Patient/MainController.inactivate').middleware([
		'auth',
		`${isRoles(['MASTER', 'ADMIN'])}`,
		isPermissions(['patients_delete']),
	])

	Route.post('patients-activate/:patientSecureId', 'V1/Admin/Patient/MainController.activate').middleware([
		'auth',
		`${isRoles(['MASTER', 'ADMIN'])}`,
		isPermissions(['patients_create']),
	])

	Route.get('patients-history/:patientSecureId', 'V1/Admin/Patient/MainController.history').middleware([
		'auth',
		`${isRoles(['MASTER', 'ADMIN'])}`,
		isPermissions(['patients_view', 'appointment_view', 'schedule_view']),
	])
})
