import BaseSchema from '@ioc:Adonis/Lucid/Schema'

export default class extends BaseSchema {
  protected tableName = 'schedule_dates_requests'

  public async up () {
    this.schema.alterTable(this.tableName, (table) => {
      table.text('payment_methods')
      table.integer('query_value')
    })
  }

  public async down () {
    this.schema.alterTable(this.tableName, (table) => {
      table.dropColumn('payment_methods')
      table.dropColumn('query_value')
    })
  }
}
