import type { HttpContextContract } from '@ioc:Adonis/Core/HttpContext'
import { schema } from '@ioc:Adonis/Core/Validator'

import User from 'App/Models/User'

export default class ListPartnersController {
	public async index({ response, request }: HttpContextContract) {
		const schemaValidator = schema.create({
			search: schema.string.optional(),
		})

		const {
			search,
		} = await request.validate({
			schema: schemaValidator
		})

		const partners = await User.query()
			.select('id', 'parent_id', 'secure_id')
			.where(builder => {
				if (search) {
					builder.whereILike('email', `%${search}%`)
					builder.orWhereHas('userInfo', builderUserInfo => {
						builderUserInfo.whereILike('name', `%${search}%`)
					})
				}
			})
			.preload('userInfo', (builder) => {
				builder.select('name')
			})
			.whereHas('roles', (builder) => {
				builder.whereIn('slug', ['partner'])
			})

		return response.ok(partners)
	}
}
