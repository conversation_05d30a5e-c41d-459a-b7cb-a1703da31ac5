import type { HttpContextContract } from '@ioc:Adonis/Core/HttpContext';
import Holiday from 'App/Models/Holiday';
import { addMonths, getDate, lastDayOfMonth } from 'date-fns';

export default class HolidayController {
  public async index({ response }: HttpContextContract) {
    const rawHolidays = await Holiday.query()
    const holidays = rawHolidays.map((holiday) => {
      return {
        secureId: holiday.$attributes.secure_id,
        date: holiday.$attributes.date,
      }
    })

    response.ok({
      holidays: holidays
    })
  }
  public async show() {

  }
  public async store() {

  }
  public async update() {

  }
  public async destroy() {

  }

  public async searchByDate({ response, params}: HttpContextContract) {
    const currentDate = await params.currentDate

    const currentDateInDateFormat = new Date(currentDate)
    let lastDayMonth = lastDayOfMonth(currentDateInDateFormat)

    const dayCurrentDate = getDate(currentDateInDateFormat)

    if (dayCurrentDate >= 27) {
      lastDayMonth = addMonths(lastDayMonth, 1)
    }

    const rawHolidays = await Holiday.query()
      .whereBetween("date", [currentDate, lastDayMonth])

    response.ok({dates: rawHolidays})
  }
}
