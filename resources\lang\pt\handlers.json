{"global": {"ER_NO_DEFAULT_FOR_FIELD": {"code": "400", "type": "error", "message": "Existem campos obrigatórios que não estão sendo enviado, verifique as informações"}, "ER_DUP_ENTRY": {"code": "400", "type": "error", "message": "Já existem um cadastro com estas informações!"}, "ER_SIGNAL_EXCEPTION": {"code": "400", "type": "error", "message": "Já existem um cadastro com estas informações!"}, "E_UNAUTHORIZED_ACCESS": {"code": "401", "type": "error", "message": "Acesso não autorizado!"}}, "v1": {"admin": {"users": {"E_ROW_NOT_FOUND": {"code": "400", "type": "error", "message": "O usuário não foi encontrado!"}}, "patients": {"DUPLICATE_EMAIL_NOT_ALLOWED": {"code": "400", "type": "error", "message": "Já existe um paciente com este email!"}}}}}