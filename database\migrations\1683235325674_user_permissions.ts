import BaseSchema from '@ioc:Adonis/Lucid/Schema'

export default class extends BaseSchema {
	protected tableName = 'user_permissions'

	public async up() {
		this.schema.createTable(this.tableName, (table) => {
			table.increments('id').primary().primary()
			table.string('secure_id')
			table
				.integer('user_id')
				.unsigned()
				.references('id')
				.inTable('users')
				.onUpdate('CASCADE')
				.onDelete('CASCADE')
			table
				.integer('permission_id')
				.unsigned()
				.references('id')
				.inTable('permissions')
				.onUpdate('CASCADE')
				.onDelete('CASCADE')

			table.dateTime('created_at')
			table.dateTime('updated_at')
		})
	}

	public async down() {
		this.schema.dropTable(this.tableName)
	}
}
