import { DateTime } from 'luxon'
import { BaseModel, HasOne, column, hasOne } from '@ioc:Adonis/Lucid/Orm'
import User from './User'

export default class ActionLog extends BaseModel {
	@column({ isPrimary: true, serializeAs: null })
	public id: number

	@column({ serializeAs: null })
	public userId: number

	@column({ serializeAs: null })
	public chargedId: number

	@column()
	public type: 'user' | 'specialty' | 'exam' | 'appointment' | 'schedule' | 'group' | 'observation'

	@column()
	public chargedData: string

	@hasOne(() => User, {
		localKey: 'userId',
		foreignKey: 'id',
	})
	public user: HasOne<typeof User>

	@column.dateTime({
		serialize: (value: DateTime) => {
			if (value) return value.toFormat('yyyy-MM-dd HH:mm:ss').replace(/\s+/g, 'T')
			return
		},
	})
	public date: DateTime

	@column.dateTime({
		autoCreate: true,
		serialize: (value: DateTime) => {
			if (value) return value.toFormat('yyyy-MM-dd HH:mm:ss').replace(/\s+/g, 'T')
			return
		},
	})
	public createdAt: DateTime

	@column.dateTime({
		autoCreate: true,
		autoUpdate: true,
		serialize: (value: DateTime) => {
			if (value) return value.toFormat('yyyy-MM-dd HH:mm:ss').replace(/\s+/g, 'T')
			return
		},
	})
	public updatedAt: DateTime
}
