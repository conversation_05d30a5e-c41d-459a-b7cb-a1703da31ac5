import type { HttpContextContract } from '@ioc:Adonis/Core/HttpContext'
import Database from '@ioc:Adonis/Lucid/Database'

import User from 'App/Models/User'

import { UpdateValidator } from 'App/Validators/Admin/Accredited/ChangeVisibility'

export default class ChangeVisibilityController {
	public async update({ response, request }: HttpContextContract) {
		const {
			partners,
			showAccreditedInApp
		} = await request.validate(UpdateValidator)

		await Database.transaction(async trx => {
			await User.query()
				.whereIn('secure_id', partners)
				.useTransaction(trx)
				.update({
					showAccreditedInApp
				})
		})

		return response.ok({
			type: 'success',
			message: 'Visibilidade dos parceiros selecionados atualizada.',
		})
	}
}
