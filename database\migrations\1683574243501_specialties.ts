import BaseSchema from '@ioc:Adonis/Lucid/Schema'

export default class extends BaseSchema {
	protected tableName = 'specialties'

	public async up() {
		this.schema.createTable(this.tableName, (table) => {
			table.increments('id')
			table.string('secure_id')
			table
				.integer('thumb_id')
				.unsigned()
				.references('id')
				.inTable('uploads')
				.onUpdate('CASCADE')
				.onDelete('RESTRICT')
			table.string('name')
			table.string('label_advice')
			table.boolean('active').defaultTo(true)

			table.dateTime('created_at')
			table.dateTime('updated_at')
		})
	}

	public async down() {
		this.schema.dropTable(this.tableName)
	}
}
