import { DateTime } from 'luxon'
import { BaseModel, column } from '@ioc:Adonis/Lucid/Orm'
import { SystemRoles } from 'App/Contants/Roles'

export default class Role extends BaseModel {
	@column({ isPrimary: true, serializeAs: null })
	public id: number

	@column()
	public name: SystemRoles

	@column()
	public slug: string

	@column()
	public description: string

	@column.dateTime({
		autoCreate: true,
		serialize: (value: DateTime) => {
			if (value) return value.toFormat('yyyy-MM-dd HH:mm:ss').replace(/\s+/g, 'T')
			return
		},
	})
	public createdAt: DateTime

	@column.dateTime({
		autoCreate: true,
		autoUpdate: true,
		serialize: (value: DateTime) => {
			if (value) return value.toFormat('yyyy-MM-dd HH:mm:ss').replace(/\s+/g, 'T')
			return
		},
	})
	public updatedAt: DateTime
}
