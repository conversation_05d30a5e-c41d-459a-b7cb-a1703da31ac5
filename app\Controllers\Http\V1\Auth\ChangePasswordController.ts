import type { HttpContextContract } from '@ioc:Adonis/Core/HttpContext'

import Database from '@ioc:Adonis/Lucid/Database'

import Hash from '@ioc:Adonis/Core/Hash'

import { UpdateValidator } from 'App/Validators/Auth/ChangePassword'

export default class ProfileController {

	public async update({ request, response, auth }: HttpContextContract) {
		const loggedUser = auth.user!
		const { password } = await request.validate(UpdateValidator)

		if (await Hash.verify(loggedUser.password, password)) {
			return response.badRequest({
				error: 'Invalid credentials',
				message: 'Sua nova senha dever ser diferente da senha anterior'
			})
		}

		await Database.transaction(async (trx) => {
			loggedUser.merge({
				password,
				isFirstAccess: false
			})
			loggedUser.useTransaction(trx)
			await loggedUser.save()
		})

		return response.ok({
			type: 'success',
			message: 'Senha alterada com sucesso!'
		})
	}
}
