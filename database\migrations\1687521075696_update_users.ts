import BaseSchema from '@ioc:Adonis/Lucid/Schema'

export default class extends BaseSchema {
	protected tableName = 'users'

	public async up() {
		this.schema.alterTable(this.tableName, (table) => {
			table.dropUnique(['email', 'parent_id'])
		})
		this.raw(
			`
			CREATE TRIGGER enforce_unique_email_except_dependent
			BEFORE INSERT ON users
			FOR EACH ROW
			BEGIN
				IF NEW.type <> 'dependent'THEN
					IF EXISTS (SELECT 1 FROM users WHERE email = NEW.email AND deleted = 0 AND type <> 'dependent') THEN
						SIGNAL SQLSTATE '45000' SET MESSAGE_TEXT = 'Duplicate email';
					END IF;
				END IF;
			END;
			`
		)
	}

	public async down() {
		this.raw('DROP TRIGGER IF EXISTS enforce_unique_email_except_dependent;')
		this.schema.alterTable(this.tableName, (table) => {
			table.unique(['email', 'parent_id'])
		})
	}
}
