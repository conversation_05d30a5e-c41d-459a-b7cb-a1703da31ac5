{"_id": "78492f2c-8c7b-40a9-a329-204fe6fef852", "colName": "Upload", "created": "2023-05-08T22:16:16.606Z", "sortNum": 135000, "folders": [{"_id": "23680139-650b-48db-b50a-4ae23f80b051", "name": "Upload", "containerId": "", "created": "2023-05-08T22:19:04.980Z", "sortNum": 10000}], "requests": [{"_id": "1b33b7d5-55c6-4d3f-a490-dc605de05f99", "colId": "78492f2c-8c7b-40a9-a329-204fe6fef852", "containerId": "23680139-650b-48db-b50a-4ae23f80b051", "name": "List", "url": "{{url}}/v1/uploads", "method": "GET", "sortNum": 10000, "created": "2023-05-08T22:19:04.980Z", "modified": "2023-05-08T22:20:40.997Z", "headers": [], "params": [], "auth": {"type": "bearer", "bearer": "{{token}}"}, "tests": [], "docs": "# Lista de uploads\n- <PERSON><PERSON> uplodas, se usu<PERSON>rio não for admin só irão aparecer os uploads do usuário logado\n\n## Query Parameters aceitos\n\n- **page**: Página. O padrão é 1\n- **limit**: Limite de dados por página. Padrão é 15\n- **search**: pesquisa por nome, email ou nome fantasia\n- **type**: image ou pdf\n\n\n"}, {"_id": "917a2178-4391-4b63-9a6c-7ea04474f54b", "colId": "78492f2c-8c7b-40a9-a329-204fe6fef852", "containerId": "23680139-650b-48db-b50a-4ae23f80b051", "name": "Create", "url": "{{url}}/v1/uploads", "method": "POST", "sortNum": 20000, "created": "2023-05-08T22:19:04.981Z", "modified": "2023-05-08T22:21:37.716Z", "headers": [], "params": [], "auth": {"type": "bearer", "bearer": "{{token}}"}, "tests": [], "docs": "# Salva upload\n\nPara criação podem ser enviados os seguintes campos:\nAtributo | Tipo | Descrição\n:------ | :------: | :------\n```file``` <font color='#dd1e2e'>required</font>  | ```File``` | Arquivo\n```name``` <font color='#dd1e2e'>required</font>  | ```string``` | Nome do upload"}, {"_id": "9f92bd9e-4ff7-4321-93b8-b4372ff9b4c9", "colId": "78492f2c-8c7b-40a9-a329-204fe6fef852", "containerId": "23680139-650b-48db-b50a-4ae23f80b051", "name": "Show", "url": "{{url}}/v1/uploads/123", "method": "GET", "sortNum": 15000, "created": "2023-05-08T22:26:23.389Z", "modified": "2023-05-08T22:26:51.038Z", "headers": [], "params": [], "auth": {"type": "bearer", "bearer": "{{token}}"}, "tests": [], "docs": "# Visualizar upload\n- Envie o secureId como parâmetro\n"}]}