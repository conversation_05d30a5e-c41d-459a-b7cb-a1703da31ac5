import BaseSchema from '@ioc:Adonis/Lucid/Schema'

export default class extends BaseSchema {
  protected tableName = 'user_infos'

  public async up() {
    this.schema.alterTable(this.tableName, (table) => {
      table.string('advice_register').after('state')
      table.text('payment_methods').after('state')
      table.enu('type_of_care', ['in_person', 'video_call', 'both']).after('state')
    })
  }

  public async down() {
    this.schema.alterTable(this.tableName, (table) => {
      table.dropColumn('advice_register')
      table.dropColumn('payment_methods')
      table.dropColumn('type_of_care')
    })
  }
}
