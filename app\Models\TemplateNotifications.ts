import { DateTime } from 'luxon'
import { BaseModel, beforeCreate, column } from '@ioc:Adonis/Lucid/Orm'

import { v4 as uuid } from 'uuid'

export default class TemplateNotifications extends BaseModel {
	@column({ isPrimary: true })
	public id: number

	@column()
	public secureId: string

	@column()
	public name: string

	@column()
	public type: 'push' | 'email' | 'sms'

	@column()
	public subject: string

	@column()
	public content: string

	@column()
	public email: string

	@column.dateTime({
		autoCreate: true,
		serialize: (value: DateTime) => {
			if (value) return value.toFormat('yyyy-MM-dd HH:mm:ss').replace(/\s+/g, 'T')
			return
		},
	})
	public createdAt: DateTime

	@column.dateTime({
		autoCreate: true,
		autoUpdate: true,
		serialize: (value: DateTime) => {
			if (value) return value.toFormat('yyyy-MM-dd HH:mm:ss').replace(/\s+/g, 'T')
			return
		},
	})
	public updatedAt: DateTime

	@beforeCreate()
	public static async createUUID(model: TemplateNotifications) {
		model.secureId = uuid()
	}
}
