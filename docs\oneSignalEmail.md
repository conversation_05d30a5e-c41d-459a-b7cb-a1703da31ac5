# Documentação do Evento OneSignal Email (AdonisJs)
1. [Introdução](#introdução) 📖
2. [Emitindo o Evento](#emitindo-o-evento) 🚀
3. [Envio de Emails para Múltiplos Destinatários com Corpos Diferentes](#envio-de-emails-para-múltiplos-destinatários-com-corpos-diferentes) 📧
4. [Notificação para Vários Usuários](#notificação-para-vários-usuários) 🔔

[Voltar](index)

## Introdução

Este documento descreve o evento "new:oneSignalEmail" e os parâmetros necessários para emitir esse evento usando o AdonisJs. O evento "new:oneSignalEmail" é utilizado para enviar notificações por email usando o serviço OneSignal. Este evento permite que você envie emails com diferentes títulos, corpos e destinatários.

## Emitindo o Evento

Para emitir o evento "new:oneSignalEmail", você deve utilizar o seguinte código:

```javascript
Event.emit('new:oneSignalEmail', {
    subject: data.subject,
    content: data.content,
    emails: data.emails,
    date?: data.date
})
```
Onde:

- subject (string): Recebe o título do email.
- content (string): Recebe o corpo da email.
- emails (array): Recebe um ou muitos endereços de email como destinatários.
- date (DateTime): Recebe uma data para agendar o envio (campo opcional)

## Envio de Emails para Múltiplos Destinatários com Corpos Diferentes

Se você deseja enviar emails para vários destinatários com corpos de email diferentes, é necessário usar um loop e emitir o evento dentro desse loop. Cada iteração do loop deve incluir um objeto com os parâmetros apropriados (título, corpo e destinatário) e emitir o evento "new:oneSignalEmail" separadamente para cada destinatário.

Aqui está um exemplo de como fazer isso em um loop:

```javascript
for (const recipient of recipients) {
    Event.emit('new:oneSignalEmail', {
        subject: recipient.subject,
        content: recipient.content,
        emails: [recipient.email]
    })
}
```

Onde recipients é um array que contém objetos com os campos subject, content, e email para cada destinatário.

## Notificação para Vários Usuários

Se você deseja enviar a mesma notificação para vários usuários, basta passar todos os endereços de email no array emails. O evento "new:oneSignalEmail" cuidará de enviar a mesma notificação para todos os destinatários listados no array.

```javascript
Event.emit('new:oneSignalEmail', {
    subject: data.subject,
    content: data.content,
    emails: ['<EMAIL>', '<EMAIL>', '<EMAIL>']
})
```

Isso enviará a mesma notificação para os três endereços de email listados no array.

Certifique-se de que os parâmetros estejam configurados corretamente de acordo com suas necessidades para garantir o envio correto das notificações por email usando o OneSignal.
