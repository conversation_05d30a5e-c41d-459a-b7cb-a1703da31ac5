import { BaseCommand, flags } from '@adonisjs/core/build/standalone'
import Schedule from 'App/Models/Schedule'
import User from 'App/Models/User'
import UserInfo from 'App/Models/UserInfo'

export default class UpdateSchedulesCityState extends BaseCommand {
  public static commandName = 'update:schedules_city_state'
  public static description = 'Atualiza os campos city e state dos schedules a partir dos dados dos pacientes'

  @flags.string({ description: 'Cidade padrão para usar quando o paciente não tiver cidade definida' })
  public defaultCity: string

  @flags.string({ description: 'Estado padrão para usar quando o paciente não tiver estado definido' })
  public defaultState: string

  public static settings = {
    loadApp: true,
    stayAlive: false,
  }

  private getProgressBar(currentPercentage: number) {
    const completed = Math.ceil(currentPercentage / 3)
    const incomplete = Math.ceil((100 - currentPercentage) / 3)
    return `[${new Array(completed).join('=')}${new Array(incomplete).join(' ')}]`
  }

  public async run() {
    this.logger.info('Iniciando atualização de city e state nos schedules...')

    try {
      // Busca todos os schedules que não têm city e state preenchidos
      this.logger.info('Buscando schedules sem city ou state...')

      const schedules = await Schedule.query()
        .where(query => {
          query.whereNull('city')
            .orWhereNull('state')
            .orWhere('city', '')
            .orWhere('state', '')
        })

      this.logger.info(`Query executada com sucesso`)

      const totalSchedules = schedules.length
      this.logger.info(`Encontrados ${totalSchedules} schedules para atualizar`)

      if (totalSchedules === 0) {
        this.logger.info('Não há schedules para atualizar')
        return
      }

      for (const [index, schedule] of schedules.entries()) {
        const progressPercentage = Math.round(((index + 1) / totalSchedules) * 100)
        const progressBar = this.getProgressBar(progressPercentage)

        this.logger.info(`Atualizando ${index + 1} de ${totalSchedules}: ${progressBar} ${progressPercentage}%`)

        // Busca o paciente associado ao schedule
        const patient = await User.findBy('id', schedule.patientId)

        if (!patient) {
          this.logger.info(`Paciente não encontrado para o schedule ID ${schedule.id}`)
          continue
        }

        // Busca as informações do paciente
        const userInfo = await UserInfo.findBy('userId', patient.id)

        if (!userInfo) {
          this.logger.info(`Informações do paciente não encontradas para o usuário ID ${patient.id}`)
          continue
        }

        // Determina os valores de city e state a serem usados
        let cityToUse = userInfo.city
        let stateToUse = userInfo.state

        // Se o paciente não tiver city ou state, usa os valores padrão se fornecidos
        if (!cityToUse && this.defaultCity) {
          cityToUse = this.defaultCity
          this.logger.info(`Usando cidade padrão "${this.defaultCity}" para o paciente ID ${patient.id}`)
        }

        if (!stateToUse && this.defaultState) {
          stateToUse = this.defaultState
          this.logger.info(`Usando estado padrão "${this.defaultState}" para o paciente ID ${patient.id}`)
        }

        // Verifica se ainda faltam valores
        if (!cityToUse || !stateToUse) {
          this.logger.info(`Paciente ID ${patient.id} não possui city ou state definidos e não foram fornecidos valores padrão`)
          continue
        }

        // Atualiza o schedule com os dados
        schedule.city = cityToUse
        schedule.state = stateToUse
        await schedule.save()

        this.logger.info(`Schedule ID ${schedule.id} atualizado com city=${cityToUse} e state=${stateToUse}`)
      }

      this.logger.info(`Atualização concluída. ${totalSchedules} schedules foram processados.`)
    } catch (error) {
      this.logger.error(`Erro ao atualizar schedules: ${error.message}`)
      console.error(error)
    }
  }
}
