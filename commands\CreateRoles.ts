import { BaseCommand, flags } from '@adonisjs/core/build/standalone'
import Database from '@ioc:Adonis/Lucid/Database'

import { ROLES, SYSTEM_ROLES } from 'App/Contants/Roles'

export default class CreateRoles extends BaseCommand {
	public static commandName = 'create:roles'

	public static title = 'Criar roles'
	public static description =
		'<PERSON><PERSON> as roles ainda não criadas e apaga as que foram removidas do controle de roles'

	@flags.boolean()
	public confirmation: boolean = true

	public static settings = {
		loadApp: true,
		stayAlive: false,
	}

	public async run() {
		const { default: Role } = await import('App/Models/Role')

		try {
			if (this.confirmation) {
				this.ui
					.sticker()
					.add(this.colors.bold(this.colors.cyan(CreateRoles.title)))
					.add('')
					.add(this.colors.gray(CreateRoles.description))
					.render()

				const confirm = await this.prompt.confirm('Deseja continuar?')

				if (!confirm) return
			}

			await Role.fetchOrCreateMany('name', Object.values(ROLES))

			await Role.query()
				.whereNotIn('name', [...SYSTEM_ROLES])
				.delete()

			await Database.manager.closeAll()
			this.logger.log(
				`${this.colors.green('Roles atualizadas com sucesso!')} ${this.ui.icons.tick}`
			)
		} catch (error) {
			this.logger.logError(error)
			this.logger.log(
				`${this.colors.red('Ocorreu um erro ao atualizar roles')} ${this.ui.icons.cross}`
			)
		}
	}
}
