import BaseSchema from '@ioc:Adonis/Lucid/Schema'

export default class extends BaseSchema {
	protected tableName = 'schedule_dates_requests'

	public async up() {
		this.schema.createTable(this.tableName, (table) => {
			table.increments('id')
			table.string('secure_id')
			table
				.integer('schedule_id')
				.unsigned()
				.references('id')
				.inTable('schedules')
				.onUpdate('CASCADE')
				.onDelete('RESTRICT')
			table
				.integer('partner_id')
				.unsigned()
				.references('id')
				.inTable('users')
				.onUpdate('CASCADE')
				.onDelete('RESTRICT')
			table.enu('partner_type', ['helloMed', 'doctor', 'clinic', 'lab'])
			table.dateTime('date', { useTz: true })
			table.enu('date_type', ['hour', 'period'])
			table.enu('value', ['morning', 'afternoon', 'night'])
			table.enu('type', ['patient', 'backoffice'])
			table.enu('status', ['to_check', 'available', 'unavailable'])

			table.dateTime('created_at', { useTz: true })
			table.dateTime('updated_at', { useTz: true })
		})
	}

	public async down() {
		this.schema.dropTable(this.tableName)
	}
}
