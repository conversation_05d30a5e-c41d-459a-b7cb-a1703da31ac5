import Route from '@ioc:Adonis/Core/Route'
import { isPermissions } from 'App/Contants/Permissions'
import { isRoles } from 'App/Contants/Roles'

Route.group(() => {
	Route.resource('observations', 'V1/Admin/Observation/MainController').middleware({
		index: ['auth', `${isRoles(['MASTER', 'ADMIN'])}`, isPermissions(['schedule_view'])],
		store: ['auth', `${isRoles(['MASTER', 'ADMIN'])}`, isPermissions(['schedule_create'])],
		show: ['auth', `${isRoles(['MASTER', 'ADMIN'])}`, isPermissions(['schedule_view'])],
		update: ['auth', `${isRoles(['MASTER', 'ADMIN'])}`, isPermissions(['schedule_edit'])],
		// destroy: ['auth', `${isRoles(['MASTER', 'ADMIN'])}`, isPermissions(['schedule_edit'])],
	})

	Route.get('observations/by-schedule-secure-id/:scheduleSecureId', 'V1/Admin/Observation/MainController.getByScheduleSecureId').middleware(
		 ['auth', `${isRoles(['MASTER', 'ADMIN'])}`, isPermissions(['schedule_view'])],
	);

	Route.delete('observations/:observationSecureId/:type', 'V1/Admin/Observation/MainController.destroy').middleware(
		['auth', `${isRoles(['MASTER', 'ADMIN'])}`, isPermissions(['schedule_edit'])],
	);

	// Route.delete('observations/schedule/:observationSecureId', 'V1/Admin/Observation/MainController.deleteFromSchedule').middleware(
	// 	['auth', `${isRoles(['MASTER', 'ADMIN'])}`, isPermissions(['schedule_edit'])],
	// );

	// Route.delete('observations/appointment/:observationSecureId', 'V1/Admin/Observation/MainController.deleteFromAppointment').middleware(
	// 	['auth', `${isRoles(['MASTER', 'ADMIN'])}`, isPermissions(['schedule_edit'])],
	// );

	// Route.delete('observations/user/:observationSecureId', 'V1/Admin/Observation/MainController.deleteFromUser').middleware(
	// 	['auth', `${isRoles(['MASTER', 'ADMIN'])}`, isPermissions(['schedule_edit'])],
	// );
})
