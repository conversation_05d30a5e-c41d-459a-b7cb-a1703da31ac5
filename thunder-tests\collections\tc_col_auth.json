{"_id": "90e9227e-7a36-45f1-a8bf-55dc59313a5b", "colName": "<PERSON><PERSON>", "created": "2023-04-04T20:02:47.313Z", "sortNum": 20000, "folders": [], "requests": [{"_id": "fb7c107f-a769-48f5-b7eb-2f79de91e73d", "colId": "90e9227e-7a36-45f1-a8bf-55dc59313a5b", "containerId": "", "name": "<PERSON><PERSON>", "url": "{{url}}/v1/sessions", "method": "POST", "sortNum": 10000, "created": "2023-04-04T20:03:28.376Z", "modified": "2023-06-20T19:03:12.360Z", "headers": [], "params": [], "body": {"type": "formencoded", "raw": "", "form": [{"name": "email", "value": "{{email}}"}, {"name": "password", "value": "{{password}}"}]}, "auth": {"type": "bearer", "bearer": "{{token}}"}, "tests": [{"type": "set-env-var", "custom": "json.token.token", "action": "setto", "value": "{{token,local}}"}], "docs": "# Autenticação na plataforma\n\nPara autenticação devem ser enviados os seguintes campos:\nAtributo | Tipo | Descrição\n:------ | :------: | :------\n```email``` <font color='#dd1e2e'>required</font>  | ```string``` | Emaild do usuário\n```password``` <font color='#dd1e2e'>required</font>| ```string``` | Senha do usuário"}, {"_id": "121921c5-5f19-484c-a1ea-593366581019", "colId": "90e9227e-7a36-45f1-a8bf-55dc59313a5b", "containerId": "", "name": "Logout", "url": "{{url}}/v1/sessions", "method": "DELETE", "sortNum": 20000, "created": "2023-04-04T20:11:22.127Z", "modified": "2023-04-04T20:11:55.371Z", "headers": [], "params": [], "auth": {"type": "bearer", "bearer": "{{token}}"}, "tests": []}, {"_id": "88e05de2-4622-43aa-a8b6-845e4f64cf33", "colId": "90e9227e-7a36-45f1-a8bf-55dc59313a5b", "containerId": "", "name": "Me", "url": "{{url}}/v1/me", "method": "GET", "sortNum": 30000, "created": "2023-04-04T20:12:15.527Z", "modified": "2023-04-04T20:13:30.832Z", "headers": [{"name": "accountSecureId", "value": "{{accountSecureId}}"}], "params": [], "auth": {"type": "bearer", "bearer": "{{token}}"}, "tests": []}, {"_id": "1e946736-d582-4553-a2ef-ee311ab72cb6", "colId": "90e9227e-7a36-45f1-a8bf-55dc59313a5b", "containerId": "", "name": "ForgotPassword", "url": "{{url}}/v1/recovery_password", "method": "POST", "sortNum": 40000, "created": "2023-06-21T21:27:36.591Z", "modified": "2023-06-21T21:37:13.128Z", "headers": [], "params": [], "body": {"type": "formencoded", "raw": "", "form": [{"name": "email", "value": "{{email}}", "isDisabled": true}, {"name": "redirectUrl", "value": "https://hellomed.com.br/forgotpassord"}, {"name": "email", "value": "<EMAIL>"}]}, "auth": {"type": "bearer", "bearer": "{{token}}"}, "tests": [{"type": "set-env-var", "custom": "json.token.token", "action": "setto", "value": "{{token,local}}"}], "docs": "# Autenticação na plataforma\n\nPara autenticação devem ser enviados os seguintes campos:\nAtributo | Tipo | Descrição\n:------ | :------: | :------\n```email``` <font color='#dd1e2e'>required</font>  | ```string``` | Emaild do usuário\n```password``` <font color='#dd1e2e'>required</font>| ```string``` | Senha do usuário"}]}