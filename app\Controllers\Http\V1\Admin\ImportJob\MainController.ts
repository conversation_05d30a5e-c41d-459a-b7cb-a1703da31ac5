import ImportJob from 'App/Models/ImportJob'
import type { HttpContextContract } from '@ioc:Adonis/Core/HttpContext'


export default class ImportsController {
  public async status({ params, response, auth }: HttpContextContract) {
    const { jobId } = params;

    console.log("JOBID DO PROGRESSO:", jobId)

    const job = await ImportJob.findOrFail(jobId);

    // if (job.userId !== auth.user!.id) {
    //   return response.forbidden();
    // }

    return response.ok({
      status: job.status,
      totalRows: job.totalRows,
      processedRows: job.processedRows,
      errorsReport: job.errorsReport,
    });
  }

  public async summary({ auth, response }: HttpContextContract) {
    const userId = auth.user!.id;

    const processingJobs = await ImportJob.query()
      .where({ userId })
      .whereIn('status', ['pending', 'processing']);

    const recentFinishedJobs = await ImportJob.query()
      .where({ userId, isRead: false }) // <<< Só pega as não lidas
      .whereIn('status', ['completed', 'failed', 'failed_with_errors'])
      .orderBy('updatedAt', 'desc')
      .limit(5); // Pega as 5 mais recentes

    return response.ok({
      processingJobs,
      recentFinishedJobs,
    });
  }

  public async markAsRead({ params, response }: HttpContextContract) {
    const job = await ImportJob.findOrFail(params.jobId);
    job.isRead = true;
    await job.save();
    return response.ok(job);
  }
}