import BaseSchema from '@ioc:Adonis/Lucid/Schema'

export default class extends BaseSchema {
  protected tableName = 'schedules'

  public async up () {
    this.schema.alterTable(this.tableName, (table) => {
			table.enu(
        'status', 
        [
          'waiting_backoffice', 
          'budget', 
          'waiting_patient', 
          'approved', 
          'canceled',
          'canceled_by_patient', 
          'canceled_at_patient_request', 
          'canceled_by_backoffice',
          "in_accreditation",
          "no_contact",
          "no_interest",
          "lack_request",
          "info_divergence",
          "financial_condition",
          "no_interest_accreditation",
        ]).alter()
		})
  }

  public async down () {
    this.schema.alterTable(this.tableName, (table) => {
			table.enu('status', ['waiting_backoffice', 'budget', 'waiting_patient', 'approved', 'canceled', 'canceled_by_patient', 'canceled_at_patient_request', 'canceled_by_backoffice']).alter()
		})
  }
}
