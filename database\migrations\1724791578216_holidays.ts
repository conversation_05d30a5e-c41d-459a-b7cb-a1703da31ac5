import BaseSchema from '@ioc:Adonis/Lucid/Schema'

export default class extends BaseSchema {
  protected tableName = 'holidays'

  public async up () {
    this.schema.createTable(this.tableName, (table) => {
      table.increments('id')

			table.string('secure_id')
			table.date('date')
			table.string('description')

      table.timestamp('created_at', { useTz: true })
      table.timestamp('updated_at', { useTz: true })
    })
  }

  public async down () {
    this.schema.dropTable(this.tableName)
  }
}
