{"shared": {"exists": "Selecione um valor valido no campo {field}.", "string": "Digite um valor valido no campo {field}.", "number": "Digite um numero valido no campo {field}.", "range": "O campo {field} deve estar entre {start} e {stop}.", "array": "O campo {field} deve ser um array", "unsigned": "O campo {field} deve ser um número positivo.", "required": "O campo {field} é obrigatório.", "requiredWhen": "O campo {field} é obrigatório.", "unique": "O campo {field} já foi utilizado.", "maxLength": "O campo {field} não pode ser maior que {maxLength} caracteres.", "minLength": "O campo {field} deve ter pelo menos {minLength} caracteres.", "enum": "O campo {field} não tem um valor valido", "date.format": "O formato da data do campo {field} está invalido", "name.required": "O nome é obrigatório", "email.email": "Digite um email valido.", "email.required": "O email é obrigatório!", "email.unique": "Este email já está em uso!", "cpf.required": "O CPF é obrigatório!", "cpf.cpf": "Digite um CPF valido.", "cpf.unique": "Este CPF já está em uso!.", "cnpj.required": "O CNPJ é obrigatório!", "cnpj.cnpj": "Digite um CNPJ valido.", "cnpj.unique": "Este CNPJ já está em uso!", "password.required": "A senha é obrigatória!", "password.minLength": "A senha deve ter pelo menos {minLength} caracteres.", "permissions.required": "Permissão obrigatória!", "permissions.array": "As permissões devem ser enviadas em um array!", "permissions.*.string": "As permissões devem ser enviadas em um array de string!", "permissions.*.exists": "Permissão informada invalida!"}}