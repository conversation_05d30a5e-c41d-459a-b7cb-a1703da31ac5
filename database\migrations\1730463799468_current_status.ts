import BaseSchema from '@ioc:Adonis/Lucid/Schema'

export default class extends BaseSchema {
  protected tableSchedules = 'schedules'
  protected tableAppointments = 'appointments'

  public async up() {
    this.schema.alterTable(this.tableSchedules, (table) => {
      table.enu(
        'current_status',
        [
          'open',
          'closed'
        ]).defaultTo('open').after('type_consult')
    })

    this.schema.alterTable(this.tableAppointments, (table) => {
      table.enu(
        'current_status',
        [
          'open',
          'closed'
        ]).defaultTo('open').after('date')
    })
  }

  public async down() {
    this.schema.alterTable(this.tableSchedules, (table) => {
      table.dropColumn('current_status')
    })

    this.schema.alterTable(this.tableAppointments, (table) => {
      table.dropColumn('current_status')
    })
  }
}
