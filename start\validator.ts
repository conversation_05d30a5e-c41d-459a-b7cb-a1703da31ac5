import { validator } from '@ioc:Adonis/Core/Validator'
import { CNPJValidation, CPFValidation } from 'App/Utils/VerifyCPFCNPJ'

validator.rule('cpf', (value, _, options) => {
	if (typeof value !== 'string') {
		return
	}

	if (!CPFValidation(value)) {
		options.errorReporter.report(
			options.pointer,
			'cpf.cpf',
			'cpf validation failed',
			options.arrayExpressionPointer
		)
	}
})

validator.rule('cnpj', (value, _, options) => {
	if (typeof value !== 'string') {
		return
	}

	if (!CNPJValidation(value)) {
		options.errorReporter.report(
			options.pointer,
			'cnpj.cnpj',
			'cnpj validation failed',
			options.arrayExpressionPointer
		)
	}
})

validator.rule('cpfOrCnpj', (value, _, options) => {
	if (typeof value !== 'string') {
		return
	}

	if (value.length <= 14) {
		const isValid = CPFValidation(value)

		if (isValid) {
			return true
		} else {
			options.errorReporter.report(
				options.pointer,
				'cpf.cpf',
				'cpf validation failed',
				options.arrayExpressionPointer
			)
		}
	}

	if (value.length > 14) {
		const isValid = CNPJValidation(value)

		if (isValid) {
			return true
		} else {
			options.errorReporter.report(
				options.pointer,
				'cnpj.cnpj',
				'cnpj validation failed',
				options.arrayExpressionPointer
			)
		}
	}
})
