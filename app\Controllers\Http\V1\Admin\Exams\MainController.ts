import type { HttpContextContract } from '@ioc:Adonis/Core/HttpContext'
import Database from '@ioc:Adonis/Lucid/Database'
import Exam from 'App/Models/Exam'
import Upload from 'App/Models/Upload'
import ActionLogChanges from 'App/Services/ActionLogChanges'

import { StoreValidator, UpdateValidator } from 'App/Validators/Admin/Exam'

export default class SpecialtyController {
	public async index({ response, request }: HttpContextContract) {
		const { page = 1, limit = 15, search } = request.only(['page', 'limit', 'search'])

		const exams = await Exam.query()
			.select('id', 'secure_id', 'name', 'active')
			.where((builder) => {
				if (search) {
					builder.whereRaw(`LOWER(name) like LOWER('%${search}%')`)
				}
			})
			.paginate(page, limit)

		return response.ok(exams)
	}

	public async store({ request, response }: HttpContextContract) {
		const { thumbSecureId, ...data } = await request.validate(StoreValidator)

		await Database.transaction(async (trx) => {
			const newExam = new Exam()

			const thumb = await Upload.query().where('secure_id', thumbSecureId).firstOrFail()

			newExam.merge({
				...data,
				thumbId: thumb.id,
			})
			newExam.useTransaction(trx)
			await newExam.save()
		})

		return response.ok({
			type: 'success',
			message: 'Exame criado com sucesso!',
		})
	}

	public async show({ response, params }: HttpContextContract) {
		const exam = await Exam.query()
			.select('id', 'thumb_id', 'secure_id', 'name', 'tags')
			.where('secure_id', params.id)
			.preload('thumb', (builderThumb) => {
				builderThumb.select('id', 'secure_id', 'url', 'name')
			})
			.firstOrFail()

		return response.ok(exam)
	}

	public async update({ response, request, params, auth }: HttpContextContract) {
		const userLogged = auth.user!

		const dataRequest = await request.validate(UpdateValidator)

		const { thumbSecureId, ...data } = dataRequest

		const exam = await Exam.query().where('secure_id', params.id).firstOrFail()

		await Database.transaction(async (trx) => {
			await ActionLogChanges.saveLogExamChanges({
				examChange: exam,
				examChangedData: {
					...dataRequest
				},
				userLogged,
				trx
			})

			if (thumbSecureId) {
				const thumb = await Upload.query().where('secure_id', thumbSecureId).firstOrFail()

				exam.merge({
					thumbId: thumb.id,
				})
			}

			exam.merge({
				...data,
			})
			exam.useTransaction(trx)
			await exam.save()
		})

		return response.ok({
			type: 'success',
			message: 'Exame atualizado com sucesso!',
		})
	}

	public async destroy({ response, params }: HttpContextContract) {
		const exam = await Exam.query().where('secure_id', params.id).firstOrFail()

		await exam.delete()

		return response.ok({
			type: 'success',
			message: 'Exame removido com sucesso!',
		})
	}
}
