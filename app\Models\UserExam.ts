import { BaseModel, BelongsTo, belongsTo, column } from '@ioc:Adonis/Lucid/Orm'
import { DateTime } from 'luxon'
import Exam from './Exam'
import User from './User'

export default class UserExam extends BaseModel {
	@column({ isPrimary: true, serializeAs: null })
	public id: number

	@column({ serializeAs: null })
	public examId: number

	@column({ serializeAs: null })
	public userId: number

	@belongsTo(() => Exam)
	public exam: BelongsTo<typeof Exam>

	@belongsTo(() => User)
	public user: BelongsTo<typeof User>

	@column.dateTime({
		autoCreate: true,
		serialize: (value: DateTime) => {
			if (value) return value.toFormat('yyyy-MM-dd HH:mm:ss').replace(/\s+/g, 'T')
			return
		},
	})
	public createdAt: DateTime

	@column.dateTime({
		autoCreate: true,
		autoUpdate: true,
		serialize: (value: DateTime) => {
			if (value) return value.toFormat('yyyy-MM-dd HH:mm:ss').replace(/\s+/g, 'T')
			return
		},
	})
	public updatedAt: DateTime
}
