import BaseSchema from '@ioc:Adonis/Lucid/Schema'

export default class extends BaseSchema {
	protected tableName = 'permissions'

	public async up() {
		this.schema.createTable(this.tableName, (table) => {
			table.increments('id').primary()
			table.string('secure_id')
			table.string('name').notNullable()
			table.string('slug').notNullable()
			table.string('group')
			table.string('description').nullable()

			table.dateTime('created_at')
			table.dateTime('updated_at')
		})
	}

	public async down() {
		this.schema.dropTable(this.tableName)
	}
}
