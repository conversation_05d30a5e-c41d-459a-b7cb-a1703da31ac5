import BaseSchema from '@ioc:Adonis/Lucid/Schema'

export default class extends BaseSchema {
	protected tableName = 'users'

	public async up() {
		this.schema.alterTable(this.tableName, (table) => {
			table.boolean('show_partner_in_app').defaultTo(true).after('type')
		})
	}

	public async down() {
		this.schema.alterTable(this.tableName, (table) => {
			table.dropColumn('show_partner_in_app')
		})
	}
}
