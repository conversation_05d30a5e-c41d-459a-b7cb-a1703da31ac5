import BaseSchema from '@ioc:Adonis/Lucid/Schema'

export default class extends BaseSchema {
	protected tableName = 'users'

	public async up() {
		this.schema.alterTable(this.tableName, (table) => {
			table.renameColumn('show_partner_in_app', 'show_accredited_in_app')
		})
	}

	public async down() {
		this.schema.alterTable(this.tableName, (table) => {
			table.renameColumn('show_accredited_in_app', 'show_partner_in_app')
		})
	}
}
