import BaseSchema from '@ioc:Adonis/Lucid/Schema';

export default class ClinicDoctorPivotTable extends BaseSchema {
  protected tableName = 'clinic_doctor';

  public async up() {
    this.schema.createTable(this.tableName, (table) => {
      table.increments('id');
      table.integer('clinic_id').unsigned().notNullable();
      table.integer('doctor_id').unsigned().notNullable();

      table.timestamps(true, true);
    });
  }

  public async down() {
    this.schema.dropTable(this.tableName);
  }
}
