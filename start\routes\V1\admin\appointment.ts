import Route from '@ioc:Adonis/Core/Route'
import { isPermissions } from 'App/Contants/Permissions'
import { isRoles } from 'App/Contants/Roles'

Route.group(() => {
	Route.get('appointments', 'V1/Admin/Appointment/MainController.index').middleware([
		'auth',
		`${isRoles(['MASTER', 'ADMIN'])}`,
		isPermissions(['appointment_view']),
	])

	Route.get('appointments-doctors', 'V1/Admin/Appointment/DoctorController.index').middleware([
		'auth',
		`${isRoles(['MASTER', 'PARTNER'])}`,
	])

	Route.put('appointments-doctors/:id', 'V1/Admin/Appointment/DoctorController.update').middleware([
		'auth',
		`${isRoles(['MASTER', 'PARTNER'])}`,
	])

	Route.get('appointments/:id', 'V1/Admin/Appointment/MainController.show').middleware([
		'auth',
		`${isRoles(['MASTER', 'ADMIN'])}`,
		isPermissions(['appointment_view']),
	])

	Route.put('appointments/:id', 'V1/Admin/Appointment/MainController.update').middleware([
		'auth',
		`${isRoles(['MASTER', 'ADMIN'])}`,
		isPermissions(['appointment_edit']),
	])

	Route.get(
		'appointments-dependencies',
		'V1/Admin/Appointment/MainController.dependencies'
	).middleware([
		'auth',
		`${isRoles(['MASTER', 'ADMIN'])}`,
	])

	Route.get(
		'appointments-dependencies-get-city/:stateUF',
		'V1/Admin/Appointment/MainController.getDependencyCityByStateUF'
	).middleware([
		'auth',
		`${isRoles(['MASTER', 'ADMIN'])}`,
	])

	Route.get(
		'appointments-observations/:appointmentSecureId',
		'V1/Admin/Appointment/MainController.getAppointmentObservationsByAppointmentSecureID'
	).middleware([
		'auth',
		`${isRoles(['MASTER', 'ADMIN'])}`,
	])
})
