import type { HttpContextContract } from '@ioc:Adonis/Core/HttpContext'
import { rules, schema } from '@ioc:Adonis/Core/Validator'
import Database from '@ioc:Adonis/Lucid/Database'
import Permission from 'App/Models/Permission'
import User from 'App/Models/User'
import ActionLogChanges from 'App/Services/ActionLogChanges'

export default class UpdatePermissionsController {
	public async update({ response, request, params, auth }: HttpContextContract) {
		const userLogged = auth.user!
		const schemaValidator = schema.create({
			permissions: schema.array().members(schema.string([rules.trim()])),
		})
		const { permissions } = await request.validate({
			schema: schemaValidator,
		})

		const permissionsDatabase = await Permission.query().whereIn('slug', permissions)

		const permissionsIds = permissionsDatabase.map(({ id }) => id)

		const user = await User.query()
			.where('secure_id', params.id)
			.andWhereHas('roles', (builder) => {
				builder.whereIn('slug', ['admin'])
			})
			.firstOrFail()

		await Database.transaction(async trx => {
			await ActionLogChanges.saveLogPermissions({
				userLogged,
				userChange: user,
				permissionsChangedData: permissionsDatabase,
				trx
			})

			await user.useTransaction(trx).related('permissions').sync(permissionsIds)
		})

		return response.ok({
			type: 'success',
			message: 'Permissões atualizadas com sucesso!',
		})
	}
}
