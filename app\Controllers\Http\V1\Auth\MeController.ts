import type { HttpContextContract } from '@ioc:Adonis/Core/HttpContext'

export default class MeController {
	public async index({ response, auth }: HttpContextContract) {
		const userLogged = auth.user!

		await userLogged.load('userInfo')
		await userLogged.load('roles')
		await userLogged.load('permissions')

		const userInfo = userLogged.userInfo
		const userCompleted =
			userInfo.name &&
			userLogged.email &&
			userInfo.legalDocumentNumber &&
			userInfo.dddCell &&
			userInfo.cell &&
			userInfo.birthDate &&
			userInfo.zipCode &&
			userInfo.street &&
			userInfo.number &&
			userInfo.neighborhood &&
			userInfo.city &&
			userInfo.state && true;

		return response.ok({
			user: {
				secureId: userLogged.secureId,
				name: userLogged.userInfo.name,
				email: userLogged.email,
				completed: userCompleted,
				isFirstAccess: !!userLogged.isFirstAccess
			},
			roles: userLogged.roles ? userLogged.roles.map((role) => role.name) : [],
			permissions: userLogged.permissions ? userLogged.permissions.map((role) => role.slug) : [],
			type: userLogged.type
		})
	}
}
