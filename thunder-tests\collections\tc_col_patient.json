{"_id": "5ec0110f-0e9f-4b56-a2fa-678f9b12cb83", "colName": "Patient", "created": "2023-05-08T22:14:05.150Z", "sortNum": 125000, "folders": [{"_id": "b9ea7a77-832e-437e-b2ad-b90451ffb5af", "name": "Dependent", "containerId": "", "created": "2023-05-08T22:14:10.083Z", "sortNum": 10000}, {"_id": "40514497-e338-4e5b-9bc4-dccbd1d3b20d", "name": "Notification", "containerId": "", "created": "2023-05-17T20:36:59.091Z", "sortNum": 20000}, {"_id": "a2caf15e-1062-45d6-9e66-f0569b4a72f8", "name": "Schedule", "containerId": "", "created": "2023-06-29T17:15:24.383Z", "sortNum": 30000}], "requests": [{"_id": "c11bc920-f106-4af2-8c0c-90a541274c6f", "colId": "5ec0110f-0e9f-4b56-a2fa-678f9b12cb83", "containerId": "b9ea7a77-832e-437e-b2ad-b90451ffb5af", "name": "List", "url": "{{url}}/v1/patient/dependents", "method": "GET", "sortNum": 10000, "created": "2023-05-08T22:14:10.083Z", "modified": "2023-05-25T18:01:09.426Z", "headers": [], "params": [], "auth": {"type": "bearer", "bearer": "{{token}}"}, "tests": [], "docs": "# Lista de usuários dependents\n- Lista usuários do tipo dependent do usuário logado\n\n## Query Parameters aceitos\n\n- **page**: Página. O padrão é 1\n- **limit**: Limite de dados por página. Padrão é 15\n- **search**: pesquisa por nome, email ou nome fantasia\n\n\n"}, {"_id": "40c916d5-663c-4f2f-81fa-10056cdb9609", "colId": "5ec0110f-0e9f-4b56-a2fa-678f9b12cb83", "containerId": "b9ea7a77-832e-437e-b2ad-b90451ffb5af", "name": "Create", "url": "{{url}}/v1/patient/dependents", "method": "POST", "sortNum": 20000, "created": "2023-05-08T22:14:10.084Z", "modified": "2023-06-29T21:33:29.685Z", "headers": [], "params": [], "body": {"type": "json", "raw": "{\"birthDate\": \"2023-07-04\", \"legal_document_number\": \"29679765806\", \"name\": \"<PERSON><PERSON>e\"}", "form": []}, "auth": {"type": "bearer", "bearer": "{{token}}"}, "tests": [], "docs": "# Cadastro de Usuários Dependents\n- Cria usuário Dependent relacionado ao usuário logado\n\nPara criação podem ser enviados os seguintes campos:\nAtributo | Tipo | Descrição\n:------ | :------: | :------\n```name``` <font color='#dd1e2e'>required</font>  | ```string``` | Nome do usuário\n```email``` <font color='#dd1e2e'>required</font>  | ```string``` | Email do usuário\n```password``` <font color='#dd1e2e'>required</font>  | ```string``` | Senha do usuário\n```phone``` <font color='#dd1e2e'>required</font>  | ```string``` | Telefone do usuário\n```birthDate``` <font color='#dd1e2e'>required</font>  | ```Date``` | Data de nascimento do usuário\n```cpf``` <font color='#dd1e2e'>required</font>  | ```string``` | CPF do usuário\n```avatarSecureId``` | ```string``` | SecureId do avatar do usuário\n```parentSecureId``` <font color='#dd1e2e'>required</font> | ```string``` | SecureId do parente do usuário"}, {"_id": "c4597671-3082-413c-9f65-4095d28694f8", "colId": "5ec0110f-0e9f-4b56-a2fa-678f9b12cb83", "containerId": "b9ea7a77-832e-437e-b2ad-b90451ffb5af", "name": "Update", "url": "{{url}}/v1/patient/dependents/123", "method": "PUT", "sortNum": 30000, "created": "2023-05-08T22:14:10.085Z", "modified": "2023-05-25T18:01:55.361Z", "headers": [], "params": [], "auth": {"type": "bearer", "bearer": "{{token}}"}, "tests": [], "docs": "# Atualização de Usuários Dependent\n- Atualiza usuário\n- Envie o secureId do usuário como parâmetro\n\nPara atualização podem ser enviados os seguintes campos:\nAtributo | Tipo | Descrição\n:------ | :------: | :------\n```name``` <font color='#dd1e2e'>required</font>  | ```string``` | Nome do usuário\n```email``` <font color='#dd1e2e'>required</font>  | ```string``` | Email do usuário\n```password```  | ```string``` | Senha do usuário\n```phone``` <font color='#dd1e2e'>required</font>  | ```string``` | Telefone do usuário\n```birthDate``` <font color='#dd1e2e'>required</font>  | ```Date``` | Data de nascimento do usuário\n```cpf``` <font color='#dd1e2e'>required</font>  | ```string``` | CPF do usuário\n```avatarSecureId``` | ```string``` | SecureId do avatar do usuário"}, {"_id": "787d28de-91df-4041-8e34-6467ed3e96bf", "colId": "5ec0110f-0e9f-4b56-a2fa-678f9b12cb83", "containerId": "b9ea7a77-832e-437e-b2ad-b90451ffb5af", "name": "Delete", "url": "{{url}}/v1/patient/dependents/123", "method": "DELETE", "sortNum": 40000, "created": "2023-05-08T22:14:10.086Z", "modified": "2023-05-25T18:02:11.552Z", "headers": [], "params": [], "auth": {"type": "bearer", "bearer": "{{token}}"}, "tests": [], "docs": "# Deletar Usuário <PERSON>dent\n- Envie o secureId do usuário como parâmetro\n"}, {"_id": "6359a2d2-100a-4a17-a26f-6c65b0e25bda", "colId": "5ec0110f-0e9f-4b56-a2fa-678f9b12cb83", "containerId": "b9ea7a77-832e-437e-b2ad-b90451ffb5af", "name": "Show", "url": "{{url}}/v1/admin/dependents/123", "method": "GET", "sortNum": 15000, "created": "2023-05-08T22:25:53.196Z", "modified": "2023-05-25T18:01:20.599Z", "headers": [], "params": [], "auth": {"type": "bearer", "bearer": "{{token}}"}, "tests": [], "docs": "# Visualizar usuário\n- Envie o secureId como parâmetro\n"}, {"_id": "9aaa4c16-c450-4e01-be17-0b1d18fae288", "colId": "5ec0110f-0e9f-4b56-a2fa-678f9b12cb83", "containerId": "40514497-e338-4e5b-9bc4-dccbd1d3b20d", "name": "List", "url": "{{url}}/v1/patient/notifications", "method": "GET", "sortNum": 10000, "created": "2023-05-17T20:36:59.091Z", "modified": "2023-05-17T20:38:35.848Z", "headers": [], "params": [], "auth": {"type": "bearer", "bearer": "{{token}}"}, "tests": [], "docs": "# Lista notificações do usuário\n- Lista notificações do usuário logado\n\n## Query Parameters aceitos\n\n- **page**: Página. O padrão é 1\n- **limit**: Limite de dados por página. Padrão é 15\n- **search**: pesquisa por nome, email ou nome fantasia\n- **status**: Os status das mensagens podem ser: 'read' ou 'not_read'\n\n\n"}, {"_id": "998a2568-8f3d-4ece-a855-07662d9892fe", "colId": "5ec0110f-0e9f-4b56-a2fa-678f9b12cb83", "containerId": "40514497-e338-4e5b-9bc4-dccbd1d3b20d", "name": "Show", "url": "{{url}}/v1/admin/notifications/123", "method": "GET", "sortNum": 15000, "created": "2023-05-17T20:36:59.092Z", "modified": "2023-05-17T20:39:01.653Z", "headers": [], "params": [], "auth": {"type": "bearer", "bearer": "{{token}}"}, "tests": [], "docs": "# Visualizar notificação\n- Envie o secureId como parâmetro\n"}, {"_id": "15d72f96-b869-4dd5-acb1-d20164ecc81f", "colId": "5ec0110f-0e9f-4b56-a2fa-678f9b12cb83", "containerId": "40514497-e338-4e5b-9bc4-dccbd1d3b20d", "name": "Update", "url": "{{url}}/v1/patient/notifications/123", "method": "PUT", "sortNum": 30000, "created": "2023-05-17T20:36:59.094Z", "modified": "2023-05-17T20:40:28.813Z", "headers": [], "params": [], "auth": {"type": "bearer", "bearer": "{{token}}"}, "tests": [], "docs": "# Marcar notificação como lida\n- Atualiza notificação\n- Envie o secureId como parâmetro"}, {"_id": "84fe0c52-d79b-4156-bcef-bf7c5cb6ccb0", "colId": "5ec0110f-0e9f-4b56-a2fa-678f9b12cb83", "containerId": "a2caf15e-1062-45d6-9e66-f0569b4a72f8", "name": "Create", "url": "{{url}}/v1/patient/schedule", "method": "POST", "sortNum": 50000, "created": "2023-06-29T17:15:30.482Z", "modified": "2023-06-29T18:46:24.378Z", "headers": [], "params": [], "body": {"type": "json", "raw": "{\n\t\t\"patientSecureId\": \"8dcd711b-340e-444d-9966-0bb61e737102\",\n\t\t\"specialtyOrExamSecureId\": \"0cd1d7e5-ea74-4942-bd0e-c3d77a1da9c1\",\n\t\t\"typeConsult\": \"in_person\",\n\t\t\"attendantType\": \"doctor\",\n\t\t\"partnerSecureId\": \"26b7d2b4-6523-4e4f-bddc-482eaa9a085c\",\n\t\t\"local\": {\n\t\t\t\"city\": \"Pederneiras\",\n\t\t\t\"neighborhood\": \"Residencial Planalto Verde II\",\n\t\t\t\"state\": \"SP\"\n\t\t},\n\t\t\"dates\": [\n\t\t\t{\n\t\t\t\t\"date\": \"2023-06-30 09:00:15\",\n\t\t\t\t\"type\": \"hour\"\n\t\t\t},\n\t\t\t\t{\n\t\t\t\t\"date\": \"2023-06-30 12:00:15\",\n\t\t\t\t\"type\": \"hour\"\n\t\t\t}\n\t\t]\n\t}", "form": []}, "auth": {"type": "bearer", "bearer": "{{token}}"}, "tests": []}, {"_id": "f18b5cd3-2a76-4e48-8bef-651c3c8e9e9d", "colId": "5ec0110f-0e9f-4b56-a2fa-678f9b12cb83", "containerId": "a2caf15e-1062-45d6-9e66-f0569b4a72f8", "name": "Index", "url": "{{url}}/v1/patient/schedule?status[]=waiting_backoffice&status[]=waiting_patient", "method": "GET", "sortNum": 60000, "created": "2023-06-29T17:49:52.989Z", "modified": "2023-07-01T14:12:53.400Z", "headers": [], "params": [{"name": "status[]", "value": "waiting_backoffice", "isPath": false}, {"name": "status[]", "value": "waiting_patient", "isPath": false}, {"name": "statusScheduleDates[]", "value": "available", "isDisabled": true, "isPath": false}], "auth": {"type": "bearer", "bearer": "{{token}}"}, "tests": []}, {"_id": "8b67ffdf-54e5-4c32-a6c9-7b285f096f85", "colId": "5ec0110f-0e9f-4b56-a2fa-678f9b12cb83", "containerId": "a2caf15e-1062-45d6-9e66-f0569b4a72f8", "name": "Delete", "url": "{{url}}/v1/patient/schedule-canceled/1ca9ca37-313a-49c5-80c1-43790b42d6a5", "method": "PUT", "sortNum": 55000, "created": "2023-06-29T18:31:29.561Z", "modified": "2023-06-29T18:41:37.429Z", "headers": [], "params": [], "body": {"type": "json", "raw": "{\n\t\t\"patientSecureId\": \"8dcd711b-340e-444d-9966-0bb61e737102\",\n\t\t\"specialtyOrExamSecureId\": \"0cd1d7e5-ea74-4942-bd0e-c3d77a1da9c1\",\n\t\t\"typeConsult\": \"in_person\",\n\t\t\"attendantType\": \"helloMed\",\n\t\t\"local\": {\n\t\t\t\"city\": \"Pederneiras\",\n\t\t\t\"neighborhood\": \"Residencial Planalto Verde II\",\n\t\t\t\"state\": \"SP\"\n\t\t},\n\t\t\"dates\": [\n\t\t\t{\n\t\t\t\t\"date\": \"2023-06-30 09:00:15\",\n\t\t\t\t\"type\": \"hour\"\n\t\t\t},\n\t\t\t\t{\n\t\t\t\t\"date\": \"2023-06-30 12:00:15\",\n\t\t\t\t\"type\": \"hour\"\n\t\t\t}\n\t\t]\n\t}", "form": []}, "auth": {"type": "bearer", "bearer": "{{token}}"}, "tests": []}, {"_id": "4688b3c3-c614-4143-a1ed-1f417faba851", "colId": "5ec0110f-0e9f-4b56-a2fa-678f9b12cb83", "containerId": "a2caf15e-1062-45d6-9e66-f0569b4a72f8", "name": "Show", "url": "{{url}}/v1/patient/schedule/422508d8-83a8-4a93-9eb7-eb6c0568c605", "method": "GET", "sortNum": 70000, "created": "2023-07-01T14:11:31.552Z", "modified": "2023-07-01T14:13:11.022Z", "headers": [], "params": [], "auth": {"type": "bearer", "bearer": "{{token}}"}, "tests": []}]}