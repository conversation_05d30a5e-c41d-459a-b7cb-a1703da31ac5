import Route from '@ioc:Adonis/Core/Route'
import { isPermissions } from 'App/Contants/Permissions'
import { isRoles } from 'App/Contants/Roles'

Route.group(() => {
	Route.resource('partners', 'V1/Admin/Partner/MainController').middleware({
		index: ['auth', `${isRoles(['MASTER', 'ADMIN'])}`, isPermissions(['partners_view'])],
		store: ['auth', `${isRoles(['MASTER', 'ADMIN'])}`, isPermissions(['partners_create'])],
		update: ['auth', `${isRoles(['MASTER', 'ADMIN'])}`, isPermissions(['partners_edit'])],
		destroy: ['auth', `${isRoles(['MASTER', 'ADMIN'])}`, isPermissions(['partners_delete'])],
	})

	Route.resource('partners/:secure_id/patients', 'V1/Admin/Partner/PartnerPatientsController').middleware({
		index: ['auth', `${isRoles(['MASTER', 'ADMIN'])}`, isPermissions(['partners_view'])],
		store: ['auth', `${isRoles(['MASTER', 'ADMIN'])}`, isPermissions(['partners_create'])],
		destroy: ['auth', `${isRoles(['MASTER', 'ADMIN'])}`, isPermissions(['partners_delete'])],
	})

	Route.resource('import-partners', 'V1/Admin/Partner/ImportController').middleware({
		store: ['auth', `${isRoles(['MASTER', 'ADMIN'])}`, isPermissions(['partners_create'])],
	})
})
