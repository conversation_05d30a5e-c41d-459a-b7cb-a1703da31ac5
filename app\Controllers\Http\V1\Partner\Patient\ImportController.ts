import type { HttpContextContract } from '@ioc:Adonis/Core/HttpContext'
import Database from '@ioc:Adonis/Lucid/Database'
import Role from 'App/Models/Role'
import User from 'App/Models/User'
import UserInfo from 'App/Models/UserInfo'
import ActionLogChanges from 'App/Services/ActionLogChanges'
import { StoreValidator } from 'App/Validators/Admin/Partner/ImportPatient'

export default class ImportController {
  public async store({ response, request, auth }: HttpContextContract) {
    const { users } = await request.validate(StoreValidator)
    const holderUsers = users.filter(user => user.holderLegalDocumentNumber === undefined)
    const dependentUsers = users.filter(user => user.holderLegalDocumentNumber !== undefined)

    const userLogged = auth.user!

    const partner = await User.query()
      .where('secure_id', userLogged.secureId)
      .andWhereHas('roles', (builder) => {
        builder.whereIn('slug', ['partner'])
      })
      .preload('patients')
      .first()

    if (!partner) {
      return response.notFound({
        type: 'warning',
        message: 'Parceiro não encontrado',
      })
    }

    const newParterPatients: User[] = []
    const newPartnerPatientDependent: User[] = []

    await Database.transaction(async trx => {
      // Puxa todos os pacientes do parceiro para inativar
      const oldPatientsToInactivate = await User.query()
        .select('id', 'secure_id')
        .whereHas('partners', (hasBuilder) => {
          hasBuilder.where('partner_id', partner.$attributes.id)
        }).useTransaction(trx)

      if (oldPatientsToInactivate.length > 0) {
        for await (const patientToInactivate of oldPatientsToInactivate) {
          const patientDependentsToInactive = await User.query()
            .where('parent_id', patientToInactivate.id)
            .useTransaction(trx)

          if (patientDependentsToInactive.length > 0) {
            for await (const dependentToInactivate of patientDependentsToInactive) {
              await User.query()
                .where('id', dependentToInactivate.id)
                .update({ is_active: false })
                .useTransaction(trx)

              await dependentToInactivate.save()
            }
          }

          await User.query()
            .where('id', patientToInactivate.id)
            .update({ is_active: false })
            .useTransaction(trx)

          await patientToInactivate.save();
        }
      }

      // Faz o cadastro de todos os pacientes titulares
      for await (const currentUser of holderUsers) {
        const { email, password, ...dataUserInfo } = currentUser

        const patient = await User.query({
          client: trx
        })
          .where('email', email)
          .andWhere('type', 'patient')
          .preload('roles')
          .preload('userInfo')
          .preload('partners')
          // .whereHas('partners', (partnerBuilder) => {
          // 	partnerBuilder.where('partner_id', partner.$attributes.id)
          // })
          .useTransaction(trx)
          .first()

        if (patient) {
          patient.merge({
            password: password ? password : dataUserInfo.legalDocumentNumber.replace(/[^0-9]/g, ''),
            is_active: true,
          })
          patient.useTransaction(trx)
          await patient.save()

          const userInfo = patient.userInfo
          userInfo.merge({
            ...dataUserInfo
          })
          userInfo.useTransaction(trx)
          await userInfo.save()

          await patient.useTransaction(trx).related('partners').attach([partner.id])

          newParterPatients.push(patient)
        } else {
          const newPatient = new User()

          newPatient.merge({
            email,
            password: password ? password : dataUserInfo.legalDocumentNumber.replace(/[^0-9]/g, ''),
            is_active: true,
          })
          newPatient.useTransaction(trx)
          await newPatient.save()

          const newPatientInfo = new UserInfo()
          newPatientInfo.merge({
            typeDocument: 'cpf',
            userId: newPatient.id,
            ...dataUserInfo,
          })
          newPatientInfo.useTransaction(trx)
          await newPatientInfo.save()

          // Faz o relacionamento de um cliente novo com o parceiro
          await newPatient.useTransaction(trx).related('partners').attach([partner.id])

          const rolesSearch = await Role.query().where('name', 'PATIENT')
          await newPatient
            .useTransaction(trx)
            .related('roles')
            .sync(rolesSearch.map((role) => role.id))

          newParterPatients.push(newPatient)
        }
      }

      // Faz o cadastro de todos os pacientes dependentes
      for await (const dependent of dependentUsers) {
        const parentDocument = dependent.holderLegalDocumentNumber;
        const { email, password, ...dependentUserData } = dependent;

        const role = await Role.query().where('name', 'PATIENT').firstOrFail()

        delete dependentUserData.holderLegalDocumentNumber;

        const dependentParentData = await User.query({
          client: trx
        })
          .select('id', 'secure_id')
          .where((whereBuilder) => {
            whereBuilder.whereHas('userInfo', (userInfoBuilder) => {
              userInfoBuilder.where('legal_document_number', parentDocument!)
            })
          })
          .useTransaction(trx)
          .first()

        const patientDependentFromDB = await User.query({
          client: trx
        })
          .andWhere('type', 'dependent')
          .andWhere((andWhereBuilder) => {
            andWhereBuilder.where('email', email)
            andWhereBuilder.orWhereHas('userInfo', (userInfoBuilder) => {
              userInfoBuilder.where('legal_document_number', dependentUserData.legalDocumentNumber)
            })
          })
          .preload('roles')
          .preload('userInfo')
          .preload('partners')
          .useTransaction(trx)
          .first()

        if (patientDependentFromDB) {
          if (!patientDependentFromDB.roles.find((role) => role.name === 'PATIENT')) {
            await patientDependentFromDB.useTransaction(trx).related('roles').attach([role.id])
          }

          patientDependentFromDB.merge({
            password: password ? password : dependentUserData.legalDocumentNumber.replace(/[^0-9]/g, ''),
            is_active: true,
            parentId: dependentParentData?.$attributes.id,
            type: 'dependent'
          })
          patientDependentFromDB.useTransaction(trx)
          await patientDependentFromDB.save()

          const userInfo = patientDependentFromDB.userInfo
          userInfo.merge({
            ...dependentUserData
          })
          userInfo.useTransaction(trx)
          await userInfo.save()

          newPartnerPatientDependent.push(patientDependentFromDB)
        } else {
          const newDependent = new User()

          newDependent.merge({
            email,
            password: password ? password : dependentUserData.legalDocumentNumber.replace(/[^0-9]/g, ''),
            is_active: true,
            parentId: dependentParentData?.$attributes.id,
            type: 'dependent'
          });
          newDependent.useTransaction(trx);
          await newDependent.save();

          const newDependentInfo = new UserInfo();
          newDependentInfo.merge({
            typeDocument: 'cpf',
            userId: newDependent.id,
            ...dependentUserData,
          });
          newDependentInfo.useTransaction(trx);
          await newDependentInfo.save();

          const rolesSearch = await Role.query().where('name', 'PATIENT');
          await newDependent
            .useTransaction(trx)
            .related('roles')
            .sync(rolesSearch.map((role) => role.id));
          newPartnerPatientDependent.push(newDependent);
        }
      }

      const uniqueSecureIds = new Set(partner.patients.map(patient => patient.secureId))
      const filteredNewParterPatients = newParterPatients.filter(patient => !uniqueSecureIds.has(patient.secureId))
      const patientsSecureIds = [...partner.patients.map(patient => patient.secureId), ...filteredNewParterPatients.map(patient => patient.secureId)]

      await ActionLogChanges.saveLogUserChanges({
        userChange: partner,
        userChangedData: {
          patientsSecureIds,
        },
        userLogged,
        trx
      })

      for await (const patient of newParterPatients) {
        const existingRelation = await partner
          .related('patients')
          .query()
          .where('user_id', patient.id)
          .useTransaction(trx)
          .first()

        if (!existingRelation) {
          if (patient.partners && patient.partners[0] && patient.partners[0].id !== partner.id) {
            await patient.useTransaction(trx).related('partners').detach([patient.partners[0].id])
          }

          await partner.useTransaction(trx).related('patients').attach([patient.id])
        }
      }
    })

    return response.ok({
      type: 'success',
      message: 'Médico adicionado com sucesso!',
    })
  }
}
