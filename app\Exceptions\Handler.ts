import Logger from '@ioc:Adonis/Core/Logger'
import { HttpContextContract } from '@ioc:Adonis/Core/HttpContext'
import HttpExceptionHandler from '@ioc:Adonis/Core/HttpExceptionHandler'

export default class ExceptionHandler extends HttpExceptionHandler {
	protected statusPages = {
		'404': 'errors/not-found',
		'500..599': 'errors/server-error',
	}

	constructor() {
		super(Logger)
	}

	public async handle(error: any, ctx: HttpContextContract) {
		const route = ctx.route?.pattern.split('/:')[0].replace(/\//g, '.')

		if (error.code === 'E_VALIDATION_FAILURE') {
			const { errors } = error.messages
			return ctx.response.status(422).send({
				type: 'warning',
				message: errors[0].message,
			})
		}

		if (error.code === 'ER_SIGNAL_EXCEPTION') {
			const codeError = error.sqlMessage.toUpperCase().replaceAll(' ', '_')
			const isI18N = ctx.i18n
				.formatMessage(`handlers${route}.${codeError}.message`)
				.match(/translation/)

			const fieldI18N =
				route && !isI18N
					? ctx.i18n.formatMessage(`handlers${route}.${codeError}.message`)
					: ctx.i18n.formatMessage(`handlers.global.${error.code}.message`)

			const type =
				route && !isI18N
					? ctx.i18n.formatMessage(`handlers${route}.${codeError}.type`)
					: ctx.i18n.formatMessage(`handlers.global.${error.code}.type`)

			const code =
				route && !isI18N
					? Number(ctx.i18n.formatMessage(`handlers${route}.${codeError}.code`))
					: Number(ctx.i18n.formatMessage(`handlers.global.${error.code}.code`))

			return ctx.response.status(code).send({
				type: type,
				message: fieldI18N,
			})
		}

		const isI18N = ctx.i18n
			.formatMessage(`handlers${route}.${error.code}.message`)
			.match(/translation/)

		const fieldI18N =
			route && !isI18N
				? ctx.i18n.formatMessage(`handlers${route}.${error.code}.message`)
				: ctx.i18n.formatMessage(`handlers.global.${error.code}.message`)

		const code =
			route && !isI18N
				? Number(ctx.i18n.formatMessage(`handlers${route}.${error.code}.code`))
				: Number(ctx.i18n.formatMessage(`handlers.global.${error.code}.code`))

		const type =
			route && !isI18N
				? ctx.i18n.formatMessage(`handlers${route}.${error.code}.type`)
				: ctx.i18n.formatMessage(`handlers.global.${error.code}.type`)

		if (fieldI18N && !fieldI18N.match(/translation/)) {
			return ctx.response.status(code).send({
				type: type,
				message: fieldI18N,
			})
		}

		this.logger.error(error)
		return super.handle(error, ctx)
	}
}
