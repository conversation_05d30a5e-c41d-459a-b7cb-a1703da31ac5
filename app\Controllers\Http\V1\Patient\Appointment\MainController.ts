import type { HttpContextContract } from '@ioc:Adonis/Core/HttpContext'
import Database from '@ioc:Adonis/Lucid/Database'
import Appointment from 'App/Models/Appointment'
import ScheduleDatesRequest from 'App/Models/ScheduleDatesRequest'
import NotificationServices from 'App/Services/NotificationServices'
import { DateTime } from 'luxon'

export default class AppointmentController {
	public async index({ request, response, auth }: HttpContextContract) {
		const { page = 1, limit = 5, status } = request.only(['page', 'limit', 'status'])
		const userLogged = auth.user!

		const appointment = await Appointment.query()
			.where('userId', userLogged.id)
			.andWhere((builder) => {
				if (status) {
					builder.andWhereIn('status', status)
				}
			})
			.preload('schedule', (builder) => {
				builder.select('type_consult')
				builder.preload('uploads', (builder) => {
					builder.select('url')
				})
					.preload('scheduleDatesRequests', (builder) => {
						builder.select('secure_id', 'payment_methods', 'query_value')
					})
			})
			.preload('specialty', (builder) => {
				builder.select('name')
			})
			.preload('exam', (builder) => {
				builder.select('name')
			})
			.preload('partner', (builder) => {
				builder.preload('userInfo', (builderPartner) => {
					builderPartner.select('id', 'name')
				})
			})
			.paginate(page, limit)

		return response.ok(appointment)
	}

	public async store({ request, response }: HttpContextContract) {
		const { scheduleDatesSecureId } = request.all()

		await Database.transaction(async (trx) => {
			const scheduleDate = await ScheduleDatesRequest.query()
				.where('secureId', scheduleDatesSecureId)
				.preload('partner')
				.preload('schedule', (builder) => {
					builder.select('id', 'user_id', 'patient_id', 'specialty_id', 'exam_id')
				})
				.firstOrFail()

			const appointment = new Appointment()
			const data: Appointment = {
				userId: scheduleDate.schedule.userId,
				patientId: scheduleDate.schedule.patientId,
				partnerId: scheduleDate.partnerId!,
				specialtyId: scheduleDate.schedule.specialtyId,
				examId: scheduleDate.schedule.examId,
				scheduleId: scheduleDate.scheduleId,
				//@ts-expect-error O partnerType nunca será helloMed
				partnerType: scheduleDate.partnerType,
				date: scheduleDate.date,
				status: 'approved',
			}
			appointment.merge(data)

			appointment.useTransaction(trx)
			await appointment.save()

			const schedule = scheduleDate.schedule

			schedule.merge({
				status: 'approved',
			})
			schedule.useTransaction(trx)
			await schedule.save()

			const infosNotification = await Database.query()
				.select(
					'schedules.id',
					'schedules.secure_id as scheduleSecureId',
					'schedules.type_consult as type',
					'schedules.status',
					'schedules.neighborhood',
					'schedules.city',
					'schedules.state',
					'schedules.date_canceled as dateCanceled',
					'schedules.type_canceled',
					'schedules.motive_canceled',
					'schedules.patient_id as patientId',
					'users.email as patientEmail',
					'responsable.email as responsableEmail',
					'user_infos.name as patientName',
					'user_infos.ddd_cell as patientDDDCell',
					'user_infos.cell as patientCell',
					'responsable_infos.ddd_cell as responsableDDDCell',
					'responsable_infos.cell as responsableCell',
					'user_infos.one_signal_key as patientOneSignalKey',
					'responsable_infos.one_signal_key as responsableOneSignalKey',
					'schedule_dates_requests.date as realizationDate',
					'schedule_dates_requests.partner_type as partnerType',
					'schedule_dates_requests.query_value as paymentDate',
					'partner_infos.id as idPartner',
					'partner_infos.name as namePartner',
					'partners.email as emailPartner',
					'specialties.name as partnerSpecialty',
					'exams.name as examName',
					'exams.tags as examTags',
				)
				.from('schedules')
				.where('schedules.id', scheduleDate.schedule.id)
				.leftJoin('users', 'users.id', 'schedules.patient_id')
				.leftJoin('user_infos', 'user_infos.user_id', 'users.id')
				.leftJoin('schedule_dates_requests', 'schedule_dates_requests.schedule_id', 'schedules.id')
				.leftJoin('users as partners', 'schedules.id', 'schedule_dates_requests.schedule_id')
				.leftJoin('user_infos as partner_infos', 'partner_infos.user_id', 'partners.id')
				.leftJoin('users as responsable', 'responsable.id', 'users.parent_id')
				.leftJoin('user_infos as responsable_infos', 'responsable_infos.user_id', 'responsable.id')
				.leftJoin('user_specialties', 'user_specialties.user_id', 'partners.id')
				.leftJoin('specialties', 'specialties.id', 'user_specialties.specialty_id')
				.leftJoin('exams', 'exams.id', 'schedules.exam_id')
				.firstOrFail()

			const scheduleDataNotification = {
				patient: {
					id: infosNotification.patientId,
					name: infosNotification.patientName,
					email: infosNotification.responsableEmail ?? infosNotification.patientEmail,
					dddCell: infosNotification.responsableDDDCell ?? infosNotification.patientDDDCell,
					cell: infosNotification.responsableCell ?? infosNotification.patientCell,
					oneSignalKey: infosNotification.responsableOneSignalKey ?? infosNotification.patientOneSignalKey,
				},
				laboratory: {
					name: infosNotification.namePartner,
					exam: infosNotification.examName,
					preparation: infosNotification.partnerSpecialty,
					email: infosNotification.emailPartner
				},
				exams: appointment.status,
				payment: {
					date: scheduleDate.date.toString(),
					amount: !!scheduleDate.queryValue ? scheduleDate.queryValue.toString() : ''
				},
				doctor: {
					id: infosNotification.idPartner,
					name: infosNotification.namePartner,
					specialty: infosNotification.partnerSpecialty,
					email: infosNotification.emailPartner,
				},
				schedule: {
					typeAppointment: infosNotification.type,
					date: scheduleDate.date.toString(),
					realizationDate: scheduleDate.date.toString(),
					cancelDate: infosNotification.dateCanceled,
				},
				place: {
					state: infosNotification.state,
					neighborhood: infosNotification.neighborhood,
					city: infosNotification.city,
				}
			}

			if (infosNotification.type == 'exam') {
				await NotificationServices.sendNotifications(scheduleDataNotification, 'examConfirmation')

				const exam3h = scheduleDate.date.minus({ hours: 3 })
				await NotificationServices.sendNotifications(scheduleDataNotification, 'exam3h', exam3h)

				const exam24h = scheduleDate.date.minus({ hours: 24 })
				await NotificationServices.sendNotifications(scheduleDataNotification, 'exam24h', exam24h)
			} else {
				await NotificationServices.sendNotifications(scheduleDataNotification, 'consultConfirmation')

				const consult3h = scheduleDate.date.minus({ hours: 3 })
				await NotificationServices.sendNotifications(scheduleDataNotification, 'consult3h', consult3h)

				const consult24h = scheduleDate.date.minus({ hours: 24 });
				await NotificationServices.sendNotifications(scheduleDataNotification, 'consult24h', consult24h)
			}

			return response.ok({
				type: 'success',
				message: 'Agendamento confirmado com sucesso!',
			})
		})
	}

	public async destroy({ response, params }: HttpContextContract) {
		await Database.transaction(async trx => {
			const appointment = await Appointment.query()
				.where('secure_id', params.id)
				.preload('schedule', builder => {
					builder.preload('scheduleDatesRequests')
				})
				.firstOrFail()

			appointment.merge({
				status: 'canceled_by_patient',
				dateCanceled: DateTime.now(),
				typeCanceled: 'patient',
			})
			appointment.useTransaction(trx)
			await appointment.save()

			appointment.schedule.merge({
				status: 'canceled_by_patient',
				dateCanceled: DateTime.now(),
				typeCanceled: 'patient',
			})
			appointment.schedule.useTransaction(trx)
			await appointment.schedule.save()

			const infosNotification = await Database.query()
				.select(
					'schedules.id',
					'schedules.secure_id as scheduleSecureId',
					'schedules.type_consult as type',
					'schedules.status',
					'schedules.neighborhood',
					'schedules.city',
					'schedules.state',
					'schedules.date_canceled as dateCanceled',
					'schedules.type_canceled',
					'schedules.motive_canceled',
					'schedules.patient_id as patientId',
					'users.email as patientEmail',
					'responsable.email as responsableEmail',
					'user_infos.name as patientName',
					'user_infos.ddd_cell as patientDDDCell',
					'user_infos.cell as patientCell',
					'responsable_infos.ddd_cell as responsableDDDCell',
					'responsable_infos.cell as responsableCell',
					'user_infos.one_signal_key as patientOneSignalKey',
					'responsable_infos.one_signal_key as responsableOneSignalKey',
					'schedule_dates_requests.date as realizationDate',
					'schedule_dates_requests.partner_type as partnerType',
					'schedule_dates_requests.query_value as paymentDate',
					'partner_infos.id as idPartner',
					'partner_infos.name as namePartner',
					'partners.email as emailPartner',
					'specialties.name as partnerSpecialty',
					'exams.name as examName',
					'exams.tags as examTags',
				)
				.from('schedules')
				.where('schedules.id', appointment.schedule.id)
				.leftJoin('users', 'users.id', 'schedules.patient_id')
				.leftJoin('user_infos', 'user_infos.user_id', 'users.id')
				.leftJoin('schedule_dates_requests', 'schedule_dates_requests.schedule_id', 'schedules.id')
				.leftJoin('users as partners', 'schedules.id', 'schedule_dates_requests.schedule_id')
				.leftJoin('user_infos as partner_infos', 'partner_infos.user_id', 'partners.id')
				.leftJoin('users as responsable', 'responsable.id', 'users.parent_id')
				.leftJoin('user_infos as responsable_infos', 'responsable_infos.user_id', 'responsable.id')
				.leftJoin('user_specialties', 'user_specialties.user_id', 'partners.id')
				.leftJoin('specialties', 'specialties.id', 'user_specialties.specialty_id')
				.leftJoin('exams', 'exams.id', 'schedules.exam_id')
				.firstOrFail()

			const scheduleDataNotification = {
				patient: {
					id: infosNotification.patientId,
					name: infosNotification.patientName,
					email: infosNotification.responsableEmail ?? infosNotification.patientEmail,
					dddCell: infosNotification.responsableDDDCell ?? infosNotification.patientDDDCell,
					cell: infosNotification.responsableCell ?? infosNotification.patientCell,
					oneSignalKey: infosNotification.responsableOneSignalKey ?? infosNotification.patientOneSignalKey,
				},
				laboratory: {
					name: infosNotification.namePartner,
					exam: infosNotification.examName,
					preparation: infosNotification.partnerSpecialty,
					email: infosNotification.emailPartner
				},
				payment: {
					date: appointment.date.toString(),
					amount: ''
				},
				doctor: {
					id: infosNotification.idPartner,
					name: infosNotification.namePartner,
					specialty: infosNotification.partnerSpecialty,
					email: infosNotification.emailPartner,
				},
				schedule: {
					typeAppointment: infosNotification.type,
					date: appointment.schedule.createdAt.toString(),
					realizationDate: appointment.date.toString(),
					cancelDate: infosNotification.dateCanceled,
				},
				place: {
					state: infosNotification.state,
					neighborhood: infosNotification.neighborhood,
					city: infosNotification.city,
				}
			}

			if (infosNotification.type == 'exam') {
				await NotificationServices.sendNotifications(scheduleDataNotification, 'examCancelForPatient')
				await NotificationServices.sendNotifications(scheduleDataNotification, 'examCancelForPatientSendBackoffice')
			} else {
				await NotificationServices.sendNotifications(scheduleDataNotification, 'consultCancelForPatient')
				await NotificationServices.sendNotifications(scheduleDataNotification, 'consultCancelForPatientSendBackoffice')
			}
		})

		return response.ok({
			type: 'success',
			message: 'Agendamento cancelado com sucesso!',
		})
	}
}
