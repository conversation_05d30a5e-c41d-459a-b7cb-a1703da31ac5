import { BaseCommand } from '@adonisjs/core/build/standalone';
import Database from '@ioc:Adonis/Lucid/Database';
import Exam from 'App/Models/Exam';
import Role from 'App/Models/Role';
import Specialty from 'App/Models/Specialty';
import User from 'App/Models/User';
import UserInfo from 'App/Models/UserInfo';
import { DateTime } from 'luxon';

type InfosXLSXProps = {
	Nome: string;
	"E-mail": string;
	"CNPJ/CPF": string;
	"DDD Celular": number;
	"DDD Telefone": number;
	Celular: number;
	Telefone: number;
	"Data de Nascimento/Funda\u00E7\u00E3o": number;
	Cep: number;
	Endereço: string;
	Número: number;
	Bairro: string;
	Cidade: string;
	Estado: string;
	Complemento?: string;
	"Tipo de atendimento (Presencial, Online ou Ambos)": string;
	"Tipo (M\u00E9dico, Cl\u00EDnica, Hospital, Laborat\u00F3rio)": string;
	Valor: number;
	"Especialidades (Separadas por v\u00EDrgula)": string;
}

export default class SyncDatabase extends BaseCommand {
	public static commandName = 'sync:database'
	public static title = 'Pega os dados da planilha e insere no banco de dados.'
	public static description = 'Pega os dados da planilha e insere no banco de dados.'

	private getProgressBar(currentPercentage: number) {
		const completed = Math.ceil(currentPercentage / 3)
		const incomplete = Math.ceil((100 - currentPercentage) / 3)
		return `[${new Array(completed).join('=')}${new Array(incomplete).join(' ')}]`
	}

	public static settings = {
		loadApp: true,
		stayAlive: false,
	}

	public async run() {
		try {
			this.ui
				.sticker()
				.add(this.colors.bold(this.colors.cyan(SyncDatabase.title)))
				.add('')
				.add(this.colors.gray(SyncDatabase.description))
				.render()

			const confirm = await this.prompt.confirm('Deseja continuar?')

			if (!confirm) return
			const XLSX = require('xlsx');

			// Exemplo de uso
			const filePath = await this.prompt.ask('Qual é o endereço do arquivo excel?')

			if (!filePath) return

			// Função para ler um arquivo Excel e converter para JSON
			function excelToJson(filePath) {
				// Lê o arquivo Excel
				const workbook = XLSX.readFile(filePath);

				// Pega a primeira planilha do arquivo
				const sheetName = workbook.SheetNames[0];
				const sheet = workbook.Sheets[sheetName];

				// Converte a planilha para um array de objetos
				const jsonData = XLSX.utils.sheet_to_json(sheet, { header: 1 });

				// Pega a primeira linha como cabeçalho (keys)
				const headers = jsonData[0];

				// Remove a primeira linha do array (cabeçalho)
				jsonData.shift();

				// Converte para JSON usando as chaves do cabeçalho
				const result = jsonData.map(row => {
					const obj = {};
					headers.forEach((header, index) => {
						obj[header] = row[index];
					});
					return obj;
				});

				return result;
			}

			const jsonData: InfosXLSXProps[] = excelToJson(filePath);

			await Database.transaction(async trx => {
				// const especialidades = [...new Set(jsonData.map(item => item['Especialidades (Separadas por vírgula)'].split(',')))]

				const especialidades: any[] = []
				jsonData.map(item => {
					if (item['Especialidades (Separadas por vírgula)']) {
						if (!(item['Especialidades (Separadas por vírgula)']).split(',').find(item => item.length > 80)) {
							return especialidades.push(...(item['Especialidades (Separadas por vírgula)']).split(',').map(item => item.trimStart()).map(item => item.trimEnd()))
						}
					}
				})

				const exams = await Exam.query()
				const specialties = await Specialty.query()

				const especialidadesFiltered = [...new Set(especialidades)]

				for await (const [index, item] of especialidadesFiltered.entries()) {
					if (!exams.find(exam => exam.name === item)) {
						const newExam = new Exam()
						newExam.merge({
							name: item,
						})
						newExam.useTransaction(trx)
						await newExam.save()
					}

					if (!specialties.find(specialty => specialty.name === item)) {
						const newSpecialty = new Specialty()
						newSpecialty.merge({
							name: item,
						})
						newSpecialty.useTransaction(trx)
						await newSpecialty.save()
					}

					const percent = Math.round(((index + 1) / especialidadesFiltered.length) * 100)
					this.logger.logUpdate(`Progresso de async de especialidades e exames ${this.getProgressBar(percent)} ${percent}%`)
				}

				for await (const [index, item] of jsonData.entries()) {
					const user = new User()
					user.merge({
						email: item['E-mail'],
						password: 'nwiodaniod2AD2@Qqdm??wdw,o',
						type: item['Tipo (Médico, Clínica, Hospital, Laboratório)'] === 'CLINICA' ? 'clinic' :
							item['Tipo (Médico, Clínica, Hospital, Laboratório)'] === 'LABORATORIO' ? 'lab'
								: 'doctor',
					})

					user.useTransaction(trx)
					await user.save()

					const role = await Role.query()
						.where('slug', 'partner')
						.firstOrFail()

					await user.useTransaction(trx).related('roles').sync([role.id])

					const formatDate = () => {
						return XLSX.SSF.parse_date_code(item['Data de Nascimento/Fundação'], { date1904: false })
					}

					const birthDate = item['Data de Nascimento/Fundação']
						? DateTime.fromJSDate(new Date(formatDate().y, formatDate().m - 1, formatDate().d))
						: undefined

					const userInfo = new UserInfo()
					userInfo.merge({
						name: item.Nome,
						userId: user.id,
						legalDocumentNumber: item['CNPJ/CPF'],
						typeDocument: (!!item['CNPJ/CPF'] && item['CNPJ/CPF'].length > 11) ? 'cnpj' : 'cpf',
						dddCell: item['DDD Celular'],
						cell: (
							!!item.Celular && String(item.Celular).length > 2
							&& String(item.Celular).replace(' ', '').replace('-', '').length < 11
							&& !String(item.Celular).includes('RAMAL')
						)
							? Number(String(item.Celular).replace(' ', '').replace('-', '').length > 9 ?
								String(item.Celular).replace(' ', '').replace('-', '').slice(1)
								: String(item.Celular).replace(' ', '').replace('-', '')
							)
							: undefined,
						dddPhone: item['DDD Telefone'],
						phone: (
							!!item.Telefone && String(item.Telefone).length > 2
							&& String(item.Telefone).replace(' ', '').replace('-', '').length < 11
							&& !String(item.Telefone).includes('RAMAL')
						)
							? Number(String(item.Telefone).replace(' ', '').replace('-', '').length > 9 ?
								String(item.Telefone).replace(' ', '').replace('-', '').slice(1)
								: String(item.Telefone).replace(' ', '').replace('-', '')
							) : undefined,
						birthDate,
						zipCode: String(item.Cep),
						street: item.Endereço,
						number: String(item.Número),
						complement: item.Complemento,
						neighborhood: item.Bairro,
						city: item.Cidade,
						state: item.Estado,
						typeOfCare: item['Tipo de atendimento (Presencial, Online ou Ambos)'] === 'Presencial' ? 'in_person' :
							item['Tipo de atendimento (Presencial, Online ou Ambos)'] === 'Online' ? 'video_call' : 'both',
						status: item['Status (Ativo, Inativo, Pontual)'] === 'Ativo' ? 'active' :
							item['Status (Ativo, Inativo, Pontual)'] === 'Inativo' ? 'inactive' : 'punctual',
					})
					userInfo.useTransaction(trx)
					await userInfo.save()

					if (item['Especialidades (Separadas por vírgula)']) {
						const itensSpecialties = (item['Especialidades (Separadas por vírgula)']).split(',').map(item => item.trimStart()).map(item => item.trimEnd())

						const exams = await Exam.query()
							.whereIn('name', itensSpecialties)
							.useTransaction(trx)
						await user.useTransaction(trx).related('exams').attach(exams.map(({ id }) => id))

						const specialties = await Specialty.query()
							.whereIn('name', itensSpecialties)
							.useTransaction(trx)

						await user.useTransaction(trx).related('specialties').attach(specialties.map(({ id }) => id))
					}

					const percent = Math.round(((index + 1) / jsonData.length) * 100)
					this.logger.logUpdate(`Progresso de async dos dados ${this.getProgressBar(percent)} ${percent}%`)
				}
			})

			this.logger.logUpdatePersist()
			await Database.manager.closeAll()
			this.logger.log(`${this.colors.green('Informações atualizadas com sucesso!')} ${this.ui.icons.tick}`)
		} catch (err) {
			this.logger.error(err)
			this.logger.log(`${this.colors.red('Ocorreu um erro ao sincronizar dados')} ${this.ui.icons.cross}`)
			await Database.manager.closeAll()
		}
	}
}
