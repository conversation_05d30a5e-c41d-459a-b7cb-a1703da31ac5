import type { HttpContextContract } from '@ioc:Adonis/Core/HttpContext'
import Database from '@ioc:Adonis/Lucid/Database'
import Role from 'App/Models/Role'
import Upload from 'App/Models/Upload'
import User from 'App/Models/User'
import UserInfo from 'App/Models/UserInfo'
import ActionLogChanges from 'App/Services/ActionLogChanges'

import { StoreValidator, UpdateValidator } from 'App/Validators/Admin/Partner'

export default class PartnerController {
	public async index({ response, request, auth }: HttpContextContract) {
		const { page = 1, limit = 15, search, type } = request.only(['page', 'limit', 'search', 'type'])
		const userLogged = auth.user!
		const partners = await User.query()
			.select('users.id', 'users.secure_id', 'users.email', 'users.type')
			.whereHas('roles', (builder) => {
				builder.whereIn('slug', ['partner'])
			})
			.andWhere((builder) => {
				if (search) {
					builder.whereRaw(`LOWER(users.email) like LOWER('%${search}%')`)
					builder.orWhereHas('userInfo', builder => {
						builder.whereRaw(`LOWER(name) like LOWER('%${search}%')`)
						builder.orWhereRaw(`LOWER(zip_code) like LOWER('%${search}%')`)
					})
				}
			})
			.andWhere((builder) => {
				if (type) {
					builder.where('users.type', type)
				}
			})
			.andWhereNot('users.id', userLogged.id)
			.preload('userInfo')
			.join('user_infos', 'user_infos.user_id', 'users.id')
			.orderBy('user_infos.name', 'asc')
			.paginate(page, limit)

		return response.ok(partners)
	}

	public async store({ request, response, auth }: HttpContextContract) {
		const dataRequest = await request.validate(StoreValidator)

		const {
			avatarSecureId,
			userExists,
			email,
			password,
			...dataUserInfo
		} = dataRequest

		const userLogged = auth.user!

		const user = await User.query()
			.where('email', email)
			.preload('roles')
			.preload('userInfo')
			.first()

		if (userExists && user && user.roles.find((role) => role.name === 'PARTNER')) {
			return response.badRequest({
				type: 'warning',
				message: 'Esse usuário já foi cadastrado como parceiro!',
			})
		}

		await Database.transaction(async (trx) => {
			if (userExists && user) {
				const role = await Role.query().where('name', 'PARTNER').firstOrFail()

				if (!user.roles.find((role) => role.name === 'PARTNER')) {
					await user.useTransaction(trx).related('roles').attach([role.id])
				}

				await ActionLogChanges.saveLogUserChanges({
					userChange: user,
					userChangedData: {
						...dataRequest
					},
					userLogged,
					trx
				})

				const userInfo = user.userInfo
				userInfo.merge({
					...dataUserInfo
				})
				userInfo.useTransaction(trx)
				await userInfo.save()
			} else {
				const newUser = new User()

				if (avatarSecureId) {
					const avatar = await Upload.query().where('secure_id', avatarSecureId).firstOrFail()

					newUser.merge({
						avatarId: avatar.id,
					})
				}

				newUser.merge({
					email,
					password,
				})
				newUser.useTransaction(trx)
				await newUser.save()

				const newUserInfo = new UserInfo()
				newUserInfo.merge({
					userId: newUser.id,
					...dataUserInfo,
				})
				newUserInfo.useTransaction(trx)
				await newUserInfo.save()

				const rolesSearch = await Role.query().where('name', 'PARTNER')
				await newUser
					.useTransaction(trx)
					.related('roles')
					.sync(rolesSearch.map((role) => role.id))
			}
		})

		return response.ok({
			type: 'success',
			message: 'Parceiro cadastrado com sucesso!',
		})
	}

	public async show({ response, params }: HttpContextContract) {
		const user = await User.query()
			.select('id', 'avatar_id', 'secure_id', 'email', 'type')
			.where('secure_id', params.id)
			.andWhereHas('roles', (builder) => {
				builder.whereIn('slug', ['partner'])
			})
			.preload('avatar', (builderAvatar) => {
				builderAvatar.select('id', 'secure_id', 'url', 'name')
			})
			.preload('userInfo')
			.firstOrFail()

		return response.ok(user)
	}

	public async update({ response, request, params, auth }: HttpContextContract) {
		const dataRequest = await request.validate(UpdateValidator)

		const {
			avatarSecureId,
			email,
			password,
			...dataUser
		} = dataRequest

		const userLogged = auth.user!

		const user = await User.query()
			.where('secure_id', params.id)
			.andWhereHas('roles', (builder) => {
				builder.whereIn('slug', ['partner'])
			})
			.preload('userInfo')
			.firstOrFail()

		await Database.transaction(async (trx) => {
			await ActionLogChanges.saveLogUserChanges({
				userChange: user,
				userChangedData: {
					...dataRequest
				},
				userLogged,
				trx
			})

			if (avatarSecureId) {
				const avatar = await Upload.query().where('secure_id', avatarSecureId).firstOrFail()

				user.merge({
					avatarId: avatar.id,
				})
			}

			user.merge({ email, password })
			user.useTransaction(trx)
			await user.save()

			const userInfo = user.userInfo
			userInfo.merge({
				...dataUser,
			})
			userInfo.useTransaction(trx)
			await userInfo.save()
		})

		return response.ok({
			type: 'success',
			message: 'Parceiro atualizado com sucesso!',
		})
	}

	public async destroy({ response, params }: HttpContextContract) {
		const user = await User.query()
			.where('secure_id', params.id)
			.andWhereHas('roles', (builder) => {
				builder.whereIn('slug', ['partner'])
			})
			.firstOrFail()

		user.merge({ deleted: true })
		await user.save()

		return response.ok({
			type: 'success',
			message: 'Parceiro removido com sucesso!',
		})
	}
}
