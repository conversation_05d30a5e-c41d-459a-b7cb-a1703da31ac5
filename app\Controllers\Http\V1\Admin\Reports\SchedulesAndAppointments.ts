import { DateTime } from 'luxon';
import Schedule from 'App/Models/Schedule';
import { schema } from '@ioc:Adonis/Core/Validator';

import type { HttpContextContract } from '@ioc:Adonis/Core/HttpContext';
import Exam from 'App/Models/Exam';
import Specialty from 'App/Models/Specialty';
import State from 'App/Models/State';
import City from 'App/Models/City';

export default class ScheduleAndAppointmentsReportController {
	public async index({ request, response }: HttpContextContract) {
		const {
			targeting,
			examOrSpecialtySecureId,
			stateUFS,
			citiesNames,
			openingDate,
			scheduleOrAppointment,
			scheduleOrAppointmentStatus
		} = request.only([
			'targeting',
			'examOrSpecialtySecureId',
			'stateUFS',
			'citiesNames',
			'openingDate',
			'scheduleOrAppointment',
			'scheduleOrAppointmentStatus'
		])

		const schemaValidator = schema.create({
			page: schema.number.optional(),
			limit: schema.number.optional(),
		});

		const {
			page = 1,
			limit = 15,
		} = await request.validate({
			schema: schemaValidator
		});

		let hasExams: Exam[] | null = null;
		let hasSpecialties: Specialty[] | null = null;

		const examOrSpecialtySecureIds = Array.isArray(examOrSpecialtySecureId)
			? examOrSpecialtySecureId
			: [examOrSpecialtySecureId];

		if (examOrSpecialtySecureId && examOrSpecialtySecureIds.length) {
			hasExams = await Exam.query()
				.whereIn('secure_id', examOrSpecialtySecureIds);

			hasSpecialties = await Specialty.query()
				.whereIn('secure_id', examOrSpecialtySecureIds);
		}

		const schedules = await Schedule.query()
			.where((builder) => {
				if (targeting !== undefined) {
					if (targeting === 'accreditation') {
						builder.where('has_been_accredited', true)
					} else {
						builder.where('has_been_accredited', false)
					}
				}

				builder.where((innerBuilder) => {
					if (hasExams && hasExams.length > 0) {
						innerBuilder.orWhereHas('exam', (examBuilder) => {
							examBuilder.whereIn('id', hasExams!.map((exam) => exam.id));
						});
					}

					if (hasSpecialties && hasSpecialties.length > 0) {
						innerBuilder.orWhereHas('specialty', (specialtyBuilder) => {
							specialtyBuilder.whereIn('id', hasSpecialties!.map((specialty) => specialty.id));
						});
					}
				});

				// if (stateUFS) {
				// 	const stateUFArray = Array.isArray(stateUFS) ? stateUFS : stateUFS.split(',');
				// 	builder.whereIn('state', stateUFArray);
				// }

				// if (citiesNames) {
				// 	const citiesNameArray = Array.isArray(citiesNames) ? citiesNames : citiesNames.split(',');
				// 	builder.whereIn('city', citiesNameArray);
				// }

				if (stateUFS || citiesNames) {
					builder.whereHas('patient', (patientBuilder) => {
						patientBuilder.andWhereHas('userInfo', (userInfoBuilder) => {
							if (stateUFS) {
								const stateUFArray = Array.isArray(stateUFS) ? stateUFS : stateUFS.split(',');
								userInfoBuilder.whereIn('state', stateUFArray);
							}

							if (citiesNames) {
								const citiesNameArray = Array.isArray(citiesNames) ? citiesNames : citiesNames.split(',');
								userInfoBuilder.whereIn('city', citiesNameArray);
							}
						});
					});
				}

				if (openingDate) {
					const openingDateFormatted = DateTime.fromISO(openingDate).toFormat('yyyy-MM-dd');
					builder.whereRaw('DATE(created_at) = ?', [openingDateFormatted]);
				}

				if (scheduleOrAppointment) {
					if (scheduleOrAppointment === 'isAppointment') {
						builder.has('appointment')
					} else {
						builder.doesntHave('appointment')
					}
				}

				if (scheduleOrAppointmentStatus && scheduleOrAppointmentStatus.length > 0) {
					builder.where((statusBuilder) => {
						statusBuilder
							.whereIn('status', scheduleOrAppointmentStatus)
							.orWhereHas('appointment', (appointmentBuilder) => {
								appointmentBuilder.whereIn('status', scheduleOrAppointmentStatus);
							});
					});
				}
			})
			.select(
				'id',
				'secure_id',
				'patient_id',
				'specialty_id',
				'exam_id',
				'created_at',
				'status',
				'has_been_accredited',
				'follow_up_date',
			)
			.preload('patient', (patientBuilder) => {
				patientBuilder.select('id', 'secure_id', 'email', 'parent_id', 'type', 'is_active')
				patientBuilder.preload('userInfo', (userInfoBuilder) => {
					userInfoBuilder.select(
						'id',
						'legal_document_number',
						'name',
						'ddd_cell',
						'cell',
						'birth_date',
						'zip_code',
						'street',
						'number',
						'complement',
						'neighborhood',
						'city',
						'state',
						'origin'
					)
				})
				patientBuilder.preload('partners')

				patientBuilder.preload('parent', (parentBuilder) => {
					parentBuilder.select('id', 'secure_id')
					parentBuilder.preload('userInfo', (userInfoBuilder) => {
						userInfoBuilder.select('id', 'legal_document_number')
					});

					parentBuilder.preload('partners', (partnerBuilder) => {
						partnerBuilder.select('id')
						partnerBuilder.preload('userInfo', (partnerUserInfoBuilder) => {
							partnerUserInfoBuilder.select('name')
						})
					})
				})
			})

			.preload('appointment', (appointmentBuilder) => {
				appointmentBuilder
					.select('secure_id', 'status', 'date', 'partner_id', 'id')
					.preload('partner', (partnerBuilder) => {
						partnerBuilder
							.select('secure_id', 'id')
							.preload('userInfo', (userInfoBuilder) => {
								userInfoBuilder.select('accredited_value');
							});
					})
			})

			.preload('scheduleDatesRequests', (requestBuilder) => {
				requestBuilder
					.select('queryValue', 'queryValueSubsidy', 'queryValuePatient')
					.where('date', 'in', Schedule.query().select('date').from('appointments'))
					.where('partner_id', 'in', Schedule.query().select('partner_id').from('appointments'))
			})

			.preload('exam', (examBuilder) => {
				examBuilder.select('secure_id', 'name')
			})

			.preload('specialty', (specialtyBuilder) => {
				specialtyBuilder.select('secure_id', 'name')
			})

			.preload('observations', (observationBuilder) => {
				observationBuilder.select('secure_id', 'observation')
			})

			.paginate(page, limit);

		return response.ok(schedules);
	}

	public async getExportData({ request, response }: HttpContextContract) {
		const {
			targeting,
			examOrSpecialtySecureId,
			stateUFS,
			citiesNames,
			openingDate,
			scheduleOrAppointment,
			scheduleOrAppointmentStatus
		} = request.only([
			'targeting',
			'examOrSpecialtySecureId',
			'stateUFS',
			'citiesNames',
			'openingDate',
			'scheduleOrAppointment',
			'scheduleOrAppointmentStatus'
		])

		const schemaValidator = schema.create({
			page: schema.number.optional(),
			limit: schema.number.optional(),
		});

		const {
			page = 1,
			limit = 15,
		} = await request.validate({
			schema: schemaValidator
		});

		let hasExams: Exam[] | null = null;
		let hasSpecialties: Specialty[] | null = null;

		const examOrSpecialtySecureIds = Array.isArray(examOrSpecialtySecureId)
			? examOrSpecialtySecureId
			: [examOrSpecialtySecureId];

		if (examOrSpecialtySecureId && examOrSpecialtySecureIds.length) {
			hasExams = await Exam.query()
				.whereIn('secure_id', examOrSpecialtySecureIds);

			hasSpecialties = await Specialty.query()
				.whereIn('secure_id', examOrSpecialtySecureIds);
		}

		const schedulesExport = await Schedule.query()
			.where((builder) => {
				if (targeting !== undefined) {
					if (targeting === 'accreditation') {
						builder.where('has_been_accredited', true)
					} else {
						builder.where('has_been_accredited', false)
					}
				}

				builder.where((innerBuilder) => {
					if (hasExams && hasExams.length > 0) {
						innerBuilder.orWhereHas('exam', (examBuilder) => {
							examBuilder.whereIn('id', hasExams!.map((exam) => exam.id));
						});
					}

					if (hasSpecialties && hasSpecialties.length > 0) {
						innerBuilder.orWhereHas('specialty', (specialtyBuilder) => {
							specialtyBuilder.whereIn('id', hasSpecialties!.map((specialty) => specialty.id));
						});
					}
				});

				if (stateUFS || citiesNames) {
					builder.whereHas('patient', (patientBuilder) => {
						patientBuilder.andWhereHas('userInfo', (userInfoBuilder) => {
							if (stateUFS) {
								const stateUFArray = Array.isArray(stateUFS) ? stateUFS : stateUFS.split(',');
								userInfoBuilder.whereIn('state', stateUFArray);
							}

							if (citiesNames) {
								const citiesNameArray = Array.isArray(citiesNames) ? citiesNames : citiesNames.split(',');
								userInfoBuilder.whereIn('city', citiesNameArray);
							}
						});
					});
				}

				if (openingDate) {
					const openingDateFormatted = DateTime.fromISO(openingDate).toFormat('yyyy-MM-dd');
					builder.whereRaw('DATE(created_at) = ?', [openingDateFormatted]);
				}

				if (scheduleOrAppointment) {
					if (scheduleOrAppointment === 'isAppointment') {
						builder.has('appointment')
					} else {
						builder.doesntHave('appointment')
					}
				}

				if (scheduleOrAppointmentStatus && scheduleOrAppointmentStatus.length > 0) {
					builder.where((statusBuilder) => {
						statusBuilder
							.whereIn('status', scheduleOrAppointmentStatus)
							.orWhereHas('appointment', (appointmentBuilder) => {
								appointmentBuilder.whereIn('status', scheduleOrAppointmentStatus);
							});
					});
				}
			})
			.select(
				'id',
				'secure_id',
				'patient_id',
				'specialty_id',
				'exam_id',
				'created_at',
				'status',
				'has_been_accredited',
				'follow_up_date',
			)
			.preload('patient', (patientBuilder) => {
				patientBuilder.select('id', 'secure_id', 'email', 'parent_id', 'type', 'is_active')
				patientBuilder.preload('userInfo', (userInfoBuilder) => {
					userInfoBuilder.select(
						'id',
						'legal_document_number',
						'name',
						'ddd_cell',
						'cell',
						'birth_date',
						'zip_code',
						'street',
						'number',
						'complement',
						'neighborhood',
						'city',
						'state',
						'origin'
					)
				})
				patientBuilder.preload('partners')

				patientBuilder.preload('parent', (parentBuilder) => {
					parentBuilder.select('id', 'secure_id')
					parentBuilder.preload('userInfo', (userInfoBuilder) => {
						userInfoBuilder.select('id', 'legal_document_number')
					});

					parentBuilder.preload('partners', (partnerBuilder) => {
						partnerBuilder.select('id')
						partnerBuilder.preload('userInfo', (partnerUserInfoBuilder) => {
							partnerUserInfoBuilder.select('name')
						})
					})
				})
			})

			.preload('appointment', (appointmentBuilder) => {
				appointmentBuilder
					.select('secure_id', 'status', 'date', 'partner_id', 'id')
					.preload('partner', (partnerBuilder) => {
						partnerBuilder
							.select('secure_id', 'id')
							.preload('userInfo', (userInfoBuilder) => {
								userInfoBuilder.select('accredited_value');
							});
					})
			})

			.preload('scheduleDatesRequests', (requestBuilder) => {
				requestBuilder
					.select('queryValue', 'queryValueSubsidy', 'queryValuePatient')
					.where('date', 'in', Schedule.query().select('date').from('appointments'))
					.where('partner_id', 'in', Schedule.query().select('partner_id').from('appointments'))
			})

			.preload('exam', (examBuilder) => {
				examBuilder.select('secure_id', 'name')
			})

			.preload('specialty', (specialtyBuilder) => {
				specialtyBuilder.select('secure_id', 'name')
			})

			.preload('observations', (observationBuilder) => {
				observationBuilder.select('secure_id', 'observation')
			})

			.paginate(page, limit);


		return response.ok(schedulesExport);
	}

	public async getDependencyCitiesByStateUFS({ response, params }: HttpContextContract) {
		const stateUFS = params.stateUFS

		const stateUFArray = stateUFS.split(',');

		const rawStates = await State.query()
			.select('id')
			.whereIn('uf', stateUFArray);

		const stateIDs = rawStates.map(state => state.id);

		const cities = await City.query()
			.select('name')
			.whereIn('state_id', stateIDs);

		return response.ok({
			cities: cities
		});
	}
}
