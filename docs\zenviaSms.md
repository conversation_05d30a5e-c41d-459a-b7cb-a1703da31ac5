# Documentação do Evento Zenvia SMS (AdonisJs)
1. [Introdução](#introdução) 📖
2. [Emitindo o Evento](#emitindo-o-evento) 🚀
3. [Envio de SMS para Múltiplos Usuários ou com Corpos Diferentes](#envio-de-sms-para-múltiplos-usuários-ou-com-corpos-diferentes) ✉️

[Voltar](index)

## Introdução

Este documento descreve o evento "new:zenviaSms" e os parâmetros necessários para emitir esse evento usando o AdonisJs. O evento "new:zenviaSms" é utilizado para enviar SMS usando o serviço Zenvia. Este evento permite que você envie SMS com diferentes corpos e destinatários.

## Emitindo o Evento

Para emitir o evento "new:oneSignalNotification", você deve utilizar o seguinte código:

```javascript
Event.emit('new:zenviaSms', {
    content: data.content,
    phone: data.phone // Exemplo: 5587988343936
})
```
Onde:

- content (string): Recebe o corpo da notificação.
- phone (string): Recebe um número de celular para envio do sms.

## Envio de SMS para Múltiplos Usuários ou com Corpos Diferentes

Se você deseja enviar sms para vários usuários ou com corpos diferentes, é necessário usar um loop e emitir o evento dentro desse loop. Cada iteração do loop deve incluir um objeto com os parâmetros apropriados (corpo e phone) e emitir o evento "new:zenviaSms" separadamente para cada destinatário.

Aqui está um exemplo de como fazer isso em um loop:

```javascript
for (const recipient of recipients) {
    Event.emit('new:zenviaSms', {
        content: recipient.content,
        phone: recipient.phone
    })
}
```

Certifique-se de que os parâmetros estejam configurados corretamente de acordo com suas necessidades para garantir o envio correto dos sms usando o Zenvia.
