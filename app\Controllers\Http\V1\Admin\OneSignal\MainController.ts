import type { HttpContextContract } from '@ioc:Adonis/Core/HttpContext'
import Event from '@ioc:Adonis/Core/Event'

export default class MainController {
	public async notificationPush({ request, response }: HttpContextContract) {
		const data = request.only(['title','content','ids'])
	
		Event.emit('new:oneSignalNotification', {
			title: data.title,
			content: data.content,
			ids: data.ids
		})
	
		response.created({
			type: 'success',
			message: 'Sent successfully'
		})
	}

	public async notificationEmail({ request, response }: HttpContextContract) {
		const data = request.only(['subject','content','emails'])
	
		Event.emit('new:oneSignalEmail', {
			subject: data.subject,
			content: data.content,
			emails: data.emails
		})
	
		response.created({
			type: 'success',
			message: 'Sent successfully'
		})
	}

	public async update({ auth, request, response, logger }: HttpContextContract) {
    const { oneSignalKey } = request.only(['oneSignalKey'])
    const userLogged = auth.user!
    await userLogged.load('userInfo')

    try {
      userLogged.userInfo.merge({
        oneSignalKey: oneSignalKey
      })

      await userLogged.userInfo.save()
      return response.ok({
        message: 'OneSignalKey atualizado com sucesso!'
      })
    } catch (error) {
      logger.error(error)
      return response.badRequest({
        message: 'Erro ao atualizar o onesignalkey'
      })
    }
  }
}
