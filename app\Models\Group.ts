import { DateTime } from 'luxon'
import { BaseModel, ManyToMany, beforeCreate, column, manyToMany } from '@ioc:Adonis/Lucid/Orm'

import { v4 as uuid } from 'uuid'
import User from './User'

export default class Group extends BaseModel {
	@column({ isPrimary: true, serializeAs: null })
	public id: number

	@column()
	public secureId: string

	@column()
	public name: string

	@manyToMany(() => User, {
		pivotTable: 'group_accrediteds',
		localKey: 'id',
		pivotForeignKey: 'group_id',
		relatedKey: 'id',
		pivotRelatedForeignKey: 'accredited_id',
	})
	public accrediteds: ManyToMany<typeof User>

	// @manyToMany(() => User, {
	// 	pivotTable: 'group_users',
	// 	localKey: 'id',
	// 	pivotForeignKey: 'group_id',
	// 	relatedKey: 'id',
	// 	pivotRelatedForeignKey: 'user_id',
	// })
	// public users: ManyToMany<typeof User>

	@column.dateTime({
		autoCreate: true,
		serialize: (value: DateTime) => {
			if (value) return value.toFormat('yyyy-MM-dd HH:mm:ss').replace(/\s+/g, 'T')
			return
		},
	})
	public createdAt: DateTime

	@column.dateTime({
		autoCreate: true,
		autoUpdate: true,
		serialize: (value: DateTime) => {
			if (value) return value.toFormat('yyyy-MM-dd HH:mm:ss').replace(/\s+/g, 'T')
			return
		},
	})
	public updatedAt: DateTime

	@beforeCreate()
	public static async createUUID(model: Group) {
		model.secureId = uuid()
	}
}
