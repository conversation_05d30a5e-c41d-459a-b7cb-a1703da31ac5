import { HttpContextContract } from '@ioc:Adonis/Core/HttpContext'
import { SystemRoles } from 'App/Contants/Roles'
import { intersection } from 'lodash'

export default class Acl {
	public async handle(
		{ auth: { user }, response }: HttpContextContract,
		next: () => Promise<void>,
		middlewareParams: SystemRoles
	) {
		if (!user) {
			return response.unauthorized({
				type: 'error',
				message: 'Acesso não autorizado!',
			})
		}

		const userRoles = await user.related('roles').query()
		const userRolesNames = userRoles.map(({ name }) => name)

		if (intersection(userRolesNames, middlewareParams).length < 1) {
			return response.forbidden({
				type: 'warning',
				message: 'Você não possui autorização para acessar está rota!',
			})
		}

		await next()
	}
}
