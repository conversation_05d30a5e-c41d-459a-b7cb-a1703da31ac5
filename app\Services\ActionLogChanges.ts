import { TransactionClientContract } from "@ioc:Adonis/Lucid/Database"
import ActionLog from "App/Models/ActionLog"
import Appointment from "App/Models/Appointment"
import Exam from "App/Models/Exam"
import Group from "App/Models/Group"
import Observations from "App/Models/Observations"
import Permission from "App/Models/Permission"
import Schedule from "App/Models/Schedule"
import Specialty from "App/Models/Specialty"
import Upload from "App/Models/Upload"
import User from "App/Models/User"
import { DateTime } from "luxon"

function formatNameKey(key: string) {
	switch (key) {
		case "name":
			return "Nome"
		case "email":
			return "Email"
		case "legal_document_number":
			return "Documento legal"
		case "legalDocumentNumber":
			return "Documento legal"
		case "gender":
			return "Sexo"
		case "birth_date":
			return "Data de nascimento"
		case "birthDate":
			return "Data de nascimento"
		case "ddd_phone":
			return "DDD telefone"
		case "dddPhone":
			return "DDD telefone"
		case "phone":
			return "Telefone"
		case "ddd_cell":
			return "DDD celular"
		case "dddCell":
			return "DDD celular"
		case "cell":
			return "Celular"
		case "zip_code":
			return "CEP"
		case "zipCode":
			return "CEP"
		case "street":
			return "Rua"
		case "number":
			return "Número"
		case "complement":
			return "Complemento"
		case "neighborhood":
			return "Bairro"
		case "city":
			return "Cidade"
		case "state":
			return "Estado"
		case "avatarSecureId":
			return "Avatar"
		case "adviceRegister":
			return "Registro"
		case "advice_register":
			return "Registro"
		case "paymentMethods":
			return "Métodos de pagamento"
		case "payment_methods":
			return "Métodos de pagamento"
		case "queryValue":
			return "Valor"
		case "query_value":
			return "Valor"
		case "type":
			return "Tipo"
		case "typeOfCare":
			return "Tipo de atendimento"
		case "type_of_care":
			return "Tipo de atendimento"
		case "showAccreditedInApp":
			return "Exibir no app"
		case "specialtiesSecureIds":
			return "Especialidades"
		case "examsSecureIds":
			return "Exames"
		case "tags":
			return "Tags"
		case "thumbSecureId":
			return "Thumb"
		case "active":
			return "Status"
		case "status":
			return "Status"
		case "labelAdvice":
			return "Conselho"
		case "label_advice":
			return "Conselho"
		case "partnerSecureId":
			return "Parceiro"
		case "origin":
			return "Origem"
		case "doctorsSecureIds":
			return "Médicos"
		case "patientsSecureIds":
			return "Pacientes"
		case "usersSecureIds":
			return "Usuários"
		case "accreditedsSecureIds":
			return "Credenciados"
		case "groupSecureId":
			return "Grupo"

		default:
			return ""
	}
}

function formatValue(value: any, uuidToNameMap?: object): string {
	if (uuidToNameMap && typeof value === 'string' && uuidToNameMap.hasOwnProperty(value)) {
		return uuidToNameMap[value] ?? value
	} else if (typeof value === 'boolean') {
		return value === true ? "Ativo" : "Inativo"
	}


	return typeof value === 'string'
		? value.length > 0
			? value
			: "vazio"
		: value ?? "vazio"
}

function compareChanges(savedObject: object, changedObject: object, uuidToNameMap?: object): string[] {
	const diffs: string[] = []

	for (const key in changedObject) {
		if (changedObject.hasOwnProperty(key)) {
			const valueVerify = changedObject[key]

			if (key in savedObject) {
				const completeValue = savedObject[key]
				if (completeValue !== valueVerify) {
					diffs.push(`O campo ${formatNameKey(key)} foi modificado de ${formatValue(completeValue, uuidToNameMap)} para ${formatValue(valueVerify, uuidToNameMap)}`)
				}
			}
		}
	}

	return diffs
}

type UserData = {
	name?: string
	email?: string
	legal_document_number?: string
	gender?: "masculine" | "feminine"
	birth_date?: DateTime | string
	ddd_phone?: number
	phone?: number
	ddd_cell?: number
	cell?: number
	zip_code?: string
	street?: string
	number?: string
	complement?: string
	neighborhood?: string
	city?: string
	state?: string
	avatarSecureId?: string
	adviceRegister?: string
	paymentMethods?: string
	queryValue?: number
	type?: "doctor" | "clinic" | "hospital" | "lab" | "patient" | "dependent" | "admin"
	typeOfCare?: "in_person" | "video_call" | "both"
	status?: "active" | "inactive" | "punctual"
	origin?: string
	showAccreditedInApp?: boolean
	partnerSecureId?: string
	groupSecureId?: string
	specialtiesSecureIds?: string | string[]
	examsSecureIds?: string | string[]
	doctorsSecureIds?: string | string[]
	patientsSecureIds?: string | string[]
}

type SaveLogUserChangesProps = {
	userLogged: User
	userChange: User
	userChangedData: UserData
	trx?: TransactionClientContract
}

type SpecialtyExamData = {
	name?: string
	tags?: string
	active?: boolean
	labelAdvice?: string
	thumbSecureId?: string
}

type SaveLogSpecialtyChangesProps = {
	userLogged: User
	specialtyChange: Specialty
	specialtyChangedData: SpecialtyExamData
	trx?: TransactionClientContract
}

type SaveLogExamChangesProps = {
	userLogged: User
	examChange: Exam
	examChangedData: SpecialtyExamData
	trx?: TransactionClientContract
}

type SaveLogPermissionsProps = {
	userLogged: User
	userChange: User
	permissionsChangedData: Permission[]
	trx?: TransactionClientContract
}

type AppointmentData = {
	currentStatus?: 'open' | 'closed'
	status?:
	'waiting' |
	'approved' |
	'did_not_attend' |
	'realized' |
	'finalized' |
	'canceled' |
	'canceled_by_patient' |
	'canceled_at_patient_request' |
	'canceled_by_backoffice'
}

type ObservationData = {
	status: 'created' | 'deleted' | 'updated';
}

type SaveLogAppointmentProps = {
	userLogged: User
	appointmentChange: Appointment
	appointmentChangedData: AppointmentData
	trx?: TransactionClientContract
}

type ScheduleData = {
	currentStatus: 'open' | 'closed'
	status?:
	"in_schedule" |
	"waiting_backoffice" |
	"waiting_backoffice_network" |
	"budget" |
	"waiting_patient" |
	"approved" |
	"canceled" |
	"canceled_by_patient" |
	"canceled_at_patient_request" |
	"canceled_by_backoffice" |
	"in_accreditation" |
	"no_contact" |
	"no_interest" |
	"lack_request" |
	"info_divergence" |
	"financial_condition" |
	"no_interest_accreditation"
}

type SaveLogScheduleProps = {
	userLogged: User
	scheduleChange: Schedule
	scheduleChangedData: ScheduleData
	trx?: TransactionClientContract
}

type SaveLogScheduleCreateProps = {
	userLogged: User;
	schedule: Schedule;
	trx?: TransactionClientContract;
}

type GroupData = {
	name?: string
	// usersSecureIds?: string | string[]
	// accreditedsSecureIds?: string | string[]
}

type SaveLogGroupProps = {
	userLogged: User
	groupChange: Group
	groupChangedData: GroupData
	trx?: TransactionClientContract
}

type SaveLogObservationProps = {
	userLogged: User;
	observation: Observations;
	observationChangedData: ObservationData;
	trx?: TransactionClientContract;
	from: ActionLog['type'];
}

export default {
	async saveLogUserChanges({ userLogged, userChange, userChangedData, trx }: SaveLogUserChangesProps) {
		await userChange.load("userInfo")
		await userChange.load("avatar")
		await userChange.load("specialties")
		await userChange.load("exams")
		await userChange.load("accreditedGroups")
		await userChange.load("partners", builder => {
			builder.preload("userInfo")
		})
		await userChange.load("doctors", builder => {
			builder.preload("userInfo")
		})
		await userChange.load("patients", builder => {
			builder.preload("userInfo")
		})


		const userSaveDData: UserData = {
			name: userChange.userInfo.name,
			email: userChange.email,
			legal_document_number: userChange.userInfo.legalDocumentNumber,
			gender: userChange.userInfo.gender,
			birth_date: userChange.userInfo.birthDate ? userChange.userInfo.birthDate.toString() : undefined,
			ddd_cell: userChange.userInfo.dddCell,
			cell: userChange.userInfo.cell,
			ddd_phone: userChange.userInfo.dddPhone,
			phone: userChange.userInfo.phone,
			zip_code: userChange.userInfo.zipCode,
			street: userChange.userInfo.street,
			number: userChange.userInfo.number,
			complement: userChange.userInfo.complement,
			neighborhood: userChange.userInfo.neighborhood,
			city: userChange.userInfo.city,
			state: userChange.userInfo.state,
			adviceRegister: userChange.userInfo.adviceRegister,
			paymentMethods: userChange.userInfo.paymentMethods,
			queryValue: userChange.userInfo.queryValue ? userChange.userInfo.queryValue / 100 : undefined,
			type: userChange.type,
			typeOfCare: userChange.userInfo.typeOfCare,
			status: userChange.userInfo.status,
			showAccreditedInApp: userChange.showAccreditedInApp,
			origin: userChange.userInfo.origin,
			groupSecureId: userChange.accreditedGroups[0] ? userChange.accreditedGroups[0].secureId : undefined,
			partnerSecureId: userChange.partners[0] ? userChange.partners[0].secureId : undefined,
			avatarSecureId: userChange.avatar ? userChange.avatar.secureId : undefined,
			specialtiesSecureIds: userChange.specialties.map(specialty => specialty.name).join(","),
			examsSecureIds: userChange.exams.map(exam => exam.name).join(","),
			doctorsSecureIds: userChange.doctors.map(doctor => doctor.userInfo.name).join(","),
			patientsSecureIds: userChange.patients.map(patient => patient.userInfo.name).join(","),
		}

		if (userChangedData.birth_date) userChangedData.birth_date = userChangedData.birth_date.toString()

		if (userChangedData.specialtiesSecureIds) {
			const specialties = await Specialty.query()
				.select('name')
				.whereIn('secure_id', [...userChangedData.specialtiesSecureIds])

			userChangedData.specialtiesSecureIds = specialties.map(specialty => specialty.name).join(",")
		}

		if (userChangedData.examsSecureIds) {
			const specialties = await Exam.query()
				.select('name')
				.whereIn('secure_id', [...userChangedData.examsSecureIds])

			userChangedData.examsSecureIds = specialties.map(exam => exam.name).join(",")
		}

		if (userChangedData.doctorsSecureIds) {
			const users = await User.query()
				.whereIn('secure_id', [...userChangedData.doctorsSecureIds])
				.preload('userInfo')

			userChangedData.doctorsSecureIds = users.map(user => user.userInfo.name).join(",")
		}

		if (userChangedData.patientsSecureIds) {
			const users = await User.query({
				client: trx
			})
				.whereIn('secure_id', [...userChangedData.patientsSecureIds])
				.preload('userInfo')

			userChangedData.patientsSecureIds = users.map(user => user.userInfo.name).join(",")
		}

		const uuidToNameMap = {}

		if (userChangedData.avatarSecureId) {
			const newAvatarUpload = await Upload.query()
				.where('secure_id', userChangedData.avatarSecureId)
				.firstOrFail()

			uuidToNameMap[userChangedData.avatarSecureId] = newAvatarUpload.name

			if (userChange.avatar) {
				uuidToNameMap[userChange.avatar.secureId] = userChange.avatar.name
			}
		}

		if (userChangedData.partnerSecureId) {
			const newPartner = await User.query()
				.where('secure_id', userChangedData.partnerSecureId)
				.preload('userInfo')
				.firstOrFail()

			uuidToNameMap[userChangedData.partnerSecureId] = newPartner.userInfo.name
			if (userChange.partners[0]) {
				uuidToNameMap[userChange.partners[0].secureId] = userChange.partners[0].userInfo.name
			}
		}

		if (userChangedData.groupSecureId) {
			const newGroup = await Group.query()
				.where('secure_id', userChangedData.groupSecureId)
				.firstOrFail()

			uuidToNameMap[userChangedData.groupSecureId] = newGroup.name
			if (userChange.accreditedGroups[0]) {
				uuidToNameMap[userChange.accreditedGroups[0].secureId] = userChange.accreditedGroups[0].name
			}
		}

		const diffs = compareChanges(userSaveDData, userChangedData, uuidToNameMap)


		if (diffs.length) {
			await ActionLog.create({
				chargedData: diffs.join(";"),
				date: DateTime.now(),
				type: 'user',
				chargedId: userChange.id,
				userId: userLogged.id
			}, { client: trx })
		}
	},

	async saveLogSpecialtyChanges({ userLogged, specialtyChange, specialtyChangedData, trx }: SaveLogSpecialtyChangesProps) {
		await specialtyChange.load("thumb")

		const specialtySaveDData: SpecialtyExamData = {
			name: specialtyChange.name,
			tags: specialtyChange.tags,
			active: Boolean(specialtyChange.active),
			labelAdvice: specialtyChange.labelAdvice,
			thumbSecureId: specialtyChange.thumb.secureId,
		}

		const uuidToNameMap = {}

		if (specialtyChangedData.thumbSecureId) {
			const newThumbUpload = await Upload.query()
				.where('secure_id', specialtyChangedData.thumbSecureId)
				.firstOrFail()

			uuidToNameMap[specialtyChangedData.thumbSecureId] = newThumbUpload.name
			uuidToNameMap[specialtyChange.thumb.secureId] = specialtyChange.thumb.name
		}

		const diffs = compareChanges(specialtySaveDData, specialtyChangedData, uuidToNameMap)

		if (diffs.length) {
			await ActionLog.create({
				chargedData: diffs.join(";"),
				date: DateTime.now(),
				type: 'specialty',
				chargedId: specialtyChange.id,
				userId: userLogged.id
			}, { client: trx })
		}
	},

	async saveLogExamChanges({ userLogged, examChange, examChangedData, trx }: SaveLogExamChangesProps) {
		await examChange.load("thumb")

		const userSaveDData: SpecialtyExamData = {
			name: examChange.name,
			tags: examChange.tags,
			active: Boolean(examChange.active),
			thumbSecureId: examChange.thumb.secureId
		}

		const uuidToNameMap = {}

		if (examChangedData.thumbSecureId) {
			const newThumbUpload = await Upload.query()
				.where('secure_id', examChangedData.thumbSecureId)
				.firstOrFail()

			uuidToNameMap[examChangedData.thumbSecureId] = newThumbUpload.name
			uuidToNameMap[examChange.thumb.secureId] = examChange.thumb.name
		}

		const diffs = compareChanges(userSaveDData, examChangedData, uuidToNameMap)

		if (diffs.length) {
			await ActionLog.create({
				chargedData: diffs.join(";"),
				date: DateTime.now(),
				type: 'exam',
				chargedId: examChange.id,
				userId: userLogged.id
			}, { client: trx })
		}
	},

	async saveLogPermissions({ userLogged, userChange, permissionsChangedData, trx }: SaveLogPermissionsProps) {
		await userChange.load('permissions')

		const oldPermissions = userChange.permissions

		//permissões adicionadas (estão nas novas, mas não nas antigas)
		const permissionsAdded = permissionsChangedData.filter(newPermission => !oldPermissions.some(oldPermission => oldPermission.secureId === newPermission.secureId))

		//permissões removidas (estão nas antigas, mas não nas novas)
		const permissionsRemoved = oldPermissions.filter(oldPermission => !permissionsChangedData.some(newPermission => newPermission.secureId === oldPermission.secureId))

		if (permissionsAdded.length || permissionsRemoved.length) {
			const strAdded = permissionsAdded.length ? `Adicionou as permissões ${permissionsAdded.map(permission => permission.description).join(', ')}` : undefined
			const strRemoved = permissionsRemoved.length ? `Removeu as permissões ${permissionsRemoved.map(permission => permission.description).join(', ')}` : undefined

			const chargedData = strAdded && strRemoved
				? `${strAdded};${strRemoved}`
				: strAdded
					? `${strAdded}`
					: `${strRemoved}`

			await ActionLog.create({
				chargedData,
				date: DateTime.now(),
				type: 'user',
				chargedId: userChange.id,
				userId: userLogged.id
			}, { client: trx })
		}
	},

	async saveLogAppointmentChanges({ userLogged, appointmentChange, appointmentChangedData, trx }: SaveLogAppointmentProps) {

		const appointmentSaveDData: AppointmentData = {
			status: appointmentChange.status,
			currentStatus: appointmentChange.currentStatus
		}

		const uuidToNameMap = {
			'waiting': 'Aguardando',
			'approved': 'Aprovado',
			'did_not_attend': 'Não compareceu',
			'realized': 'Realizado',
			'finalized': 'Finalizado',
			'canceled': 'Cancelado',
			'canceled_by_patient': 'Cancelado pelo Paciente',
			'canceled_at_patient_request': 'Cancelado a pedido do Paciente',
			'canceled_by_backoffice': 'Cancelado pelo Backoffice',
			'open': 'Aberto',
			'closed': 'Encerrado'
		}

		const diffs = compareChanges(appointmentSaveDData, appointmentChangedData, uuidToNameMap)

		if (diffs.length) {
			await ActionLog.create({
				chargedData: diffs.join(";"),
				date: DateTime.now(),
				type: 'appointment',
				chargedId: appointmentChange.id,
				userId: userLogged.id
			}, { client: trx })
		}
	},

	async saveLogScheduleChanges({ userLogged, scheduleChange, scheduleChangedData, trx }: SaveLogScheduleProps) {
		await scheduleChange.load('scheduleDatesRequests', builderScheduleDate => {
			builderScheduleDate.preload('partner', builderPartner => {
				builderPartner.preload('userInfo')
			})
		})

		const appointmentSaveDData: ScheduleData = {
			status: scheduleChange.status,
			currentStatus: scheduleChange.currentStatus
		}

		const uuidToNameMap = {
			'waiting_backoffice': 'Aguardando Backoffice',
			'budget': 'Orçamento',
			'waiting_patient': 'Aguardando Paciente',
			'approved': 'Aprovada',
			'canceled': 'Cancelado',
			'canceled_by_patient': 'Cancelado pelo Paciente',
			'canceled_at_patient_request': 'Cancelado a pedido do Paciente',
			'canceled_by_backoffice': 'Cancelado a pedido do Paciente',
			"in_accreditation": "Em credenciamento",
			"no_contact": 'Sem Contato',
			"no_interest": 'Sem Interesse',
			"lack_request": 'Falta Pedido',
			"info_divergence": 'Divergência de Informações',
			"financial_condition": 'Condição Financeira',
			"no_interest_accreditation": 'Sem Interesse Credenciamento',
			'to_check': 'Checar',
			'available': 'Disponível',
			'unavailable': 'Indisponível'
		}

		const diffs = compareChanges(appointmentSaveDData, scheduleChangedData, uuidToNameMap)

		const dates = scheduleChange.scheduleDatesRequests.map(scheduleDate => {
			return `${scheduleDate.date.toFormat('dd/MM/yyyy HH:mm:ss')} do credenciado ${scheduleDate.partner ? scheduleDate.partner.userInfo.name : 'HelloMed'} com a disponibilidade ${uuidToNameMap[scheduleDate.status]}`
		})

		if (diffs.length) {
			await ActionLog.create({
				chargedData: `${diffs.join(";")}. Com os horários: ${dates.join(", ")}`,
				date: DateTime.now(),
				type: 'schedule',
				chargedId: scheduleChange.id,
				userId: userLogged.id
			}, { client: trx })
		}
	},

	async saveLogScheduleCreate({ userLogged, schedule, trx }: SaveLogScheduleCreateProps) {
		await schedule.load('scheduleDatesRequests', builderScheduleDate => {
			builderScheduleDate.preload('partner', builderPartner => {
				builderPartner.preload('userInfo')
			})
		})

		await schedule.load('patient', builder => {
			builder.preload('userInfo')
		})

		if (schedule.examId) {
			await schedule.load('exam')
		}
		if (schedule.specialtyId) {
			await schedule.load('specialty')
		}

		const uuidToNameMap = {
			'waiting_backoffice': 'Aguardando Backoffice',
			'budget': 'Orçamento',
			'waiting_patient': 'Aguardando Paciente',
			'approved': 'Aprovada',
			'canceled': 'Cancelado',
			'canceled_by_patient': 'Cancelado pelo Paciente',
			'canceled_at_patient_request': 'Cancelado a pedido do Paciente',
			'canceled_by_backoffice': 'Cancelado a pedido do Paciente',
			"in_accreditation": "Em credenciamento",
			"no_contact": 'Sem Contato',
			"no_interest": 'Sem Interesse',
			"lack_request": 'Falta Pedido',
			"info_divergence": 'Divergência de Informações',
			"financial_condition": 'Condição Financeira',
			"no_interest_accreditation": 'Sem Interesse Credenciamento',
			'to_check': 'Checar',
			'available': 'Disponível',
			'unavailable': 'Indisponível'
		}

		const dates = schedule.scheduleDatesRequests.map(scheduleDate => {
			return `${scheduleDate.date.toFormat('dd/MM/yyyy HH:mm:ss')} do credenciado ${scheduleDate.partner ? scheduleDate.partner.userInfo.name : 'HelloMed'} com a disponibilidade ${uuidToNameMap[scheduleDate.status]}`
		})

		await ActionLog.create({
			chargedData: `Criou a solicitação de agendamento em ${schedule.createdAt.toFormat('dd/MM/yyyy HH:mm:ss')
				} do paciente ${schedule.patient?.userInfo?.name
				} com o status ${schedule.status === 'budget' ? 'Orçamento' : 'Aguardando backoffice'
				} para ${schedule.specialty?.name ?? schedule.exam?.name}. Com os horários: ${dates.join(", ")}`,
			date: DateTime.now(),
			type: 'schedule',
			chargedId: schedule.id,
			userId: userLogged.id
		}, { client: trx })
	},

	async saveLogGroupChanges({ userLogged, groupChange, groupChangedData, trx }: SaveLogGroupProps) {
		await groupChange.load("accrediteds", builder => {
			builder.preload("userInfo")
		})

		const appointmentSaveDData: GroupData = {
			name: groupChange.name,
		}

		const diffs = compareChanges(appointmentSaveDData, groupChangedData)

		if (diffs.length) {
			await ActionLog.create({
				chargedData: diffs.join(";"),
				date: DateTime.now(),
				type: 'group',
				chargedId: groupChange.id,
				userId: userLogged.id
			}, { client: trx })
		}
	},

	/**
	 *
	 * @param from string; é a tela onde a ação de log foi realizada
	 */
	async saveLogObservationChanges({ userLogged, observation, observationChangedData, trx, from }: SaveLogObservationProps) {
		await observation.load('schedule', builder => {
			builder.preload('patient', builderPatient => {
				builderPatient.preload('userInfo')
			})

			builder.preload('specialty')

			builder.preload('exam')

			builder.preload('appointment')
		})

		const nameMap: { [key: string]: string } = {
			'created': 'Criou',
			'deleted': 'Excluiu',
			'updated': 'Alterou'
		}

		const status = observationChangedData.status;
		const action = nameMap[status as keyof typeof nameMap] || 'Realizou ação desconhecida para';
		const patientName = observation.schedule?.patient?.userInfo?.name;
		const specialty = observation?.schedule?.specialty ? observation.schedule.specialty.name : observation.schedule.exam.name

		const chargeId = observation?.schedule?.appointment?.id ? observation?.schedule?.appointment?.id : observation.schedule.id;

		await ActionLog.create({
			chargedData: `${action} a observação ${observation.observation} no agendamento do paciente ${patientName} para a especialidade/exame: ${specialty}`,
			date: DateTime.now(),
			type: from,
			chargedId: chargeId,
			userId: userLogged.id
		}, { client: trx })
	}
}
