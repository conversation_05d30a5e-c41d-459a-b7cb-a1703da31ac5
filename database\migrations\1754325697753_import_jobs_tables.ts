import BaseSchema from '@ioc:Adonis/Lucid/Schema'

export default class extends BaseSchema {
  protected tableName = 'import_jobs'

  public async up() {
    this.schema.createTable(this.tableName, (table) => {
      table.increments('id')
      table
        .integer('partner_id')
        .unsigned()
        .references('id')
        .inTable('users')
        .onUpdate('CASCADE')
        .onDelete('CASCADE')
      table
        .integer('user_id')
        .unsigned()
        .references('id')
        .inTable('users')
        .onDelete('SET NULL') // Se o admin for deletado, mantém o registro do job
      table
        .enum('status', ['pending', 'processing', 'completed', 'failed_with_errors', 'failed'])
        .notNullable()
        .defaultTo('pending')

      table.integer('total_rows').unsigned().defaultTo(0)
      table.integer('processed_rows').unsigned().defaultTo(0)

      table.string('original_filename').notNullable()
      table.string('stored_file_path').notNullable()
      table.jsonb('errors_report').nullable()

      table.timestamp('completed_at', { useTz: true }).nullable()

      table.timestamp('created_at', { useTz: true })
      table.timestamp('updated_at', { useTz: true })
    })
  }

  public async down() {
    this.schema.dropTable(this.tableName)
  }
}
