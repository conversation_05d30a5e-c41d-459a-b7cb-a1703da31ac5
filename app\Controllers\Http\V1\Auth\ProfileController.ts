import type { HttpContextContract } from '@ioc:Adonis/Core/HttpContext'
import Database from '@ioc:Adonis/Lucid/Database'
import Upload from 'App/Models/Upload'
import User from 'App/Models/User'

import { UpdateValidator } from 'App/Validators/Auth/Profile'

export default class ProfileController {
	public async show({ response, auth }: HttpContextContract) {
		const userLogged = auth.user!
		const user = await User.query()
			.select('id', 'avatar_id', 'secure_id', 'email')
			.where('id', userLogged.id)
			.preload('avatar', (builderAvatar) => {
				builderAvatar.select('id', 'secure_id', 'url', 'name')
			})
			.preload('userInfo')
			.firstOrFail()

		return response.ok(user)
	}

	public async update({ response, request, auth }: HttpContextContract) {
		const userLogged = auth.user!

		const {
			avatarSecureId,
			email,
			password,
			...dataInfo
		} = await request.validate(UpdateValidator)

		const user = await User.query().where('id', userLogged.id).preload('userInfo').firstOrFail()

		await Database.transaction(async (trx) => {
			if (avatarSecureId) {
				const avatar = await Upload.query().where('secure_id', avatarSecureId).firstOrFail()

				user.merge({
					avatarId: avatar.id,
				})
			}

			user.merge({ email, password })
			user.useTransaction(trx)
			await user.save()

			const userInfo = user.userInfo
			userInfo.merge({ ...dataInfo })
			await userInfo.save()
		})

		return response.ok({
			type: 'success',
			message: 'Dados atualizados com sucesso!',
		})
	}
}
