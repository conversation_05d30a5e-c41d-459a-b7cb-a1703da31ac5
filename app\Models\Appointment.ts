import { DateTime } from 'luxon'
import {
	<PERSON>Model,
	beforeCreate,
	BelongsTo,
	belongsTo,
	column,
	hasOne,
	HasOne,
} from '@ioc:Adonis/Lucid/Orm'
import { v4 as uuid } from 'uuid'
import Exam from './Exam'
import User from './User'
import Specialty from './Specialty'
import Schedule from './Schedule'

export default class Appointment extends BaseModel {
	@column({ isPrimary: true, serializeAs: null })
	public id: number

	@column()
	public secureId: string

	@column({ serializeAs: null })
	public userId: number

	@column({ serializeAs: null })
	public patientId: number

	@column({ serializeAs: null })
	public partnerId: number

	@column({ serializeAs: null })
	public specialtyId: number | null

	@column({ serializeAs: null })
	public examId: number | null

	@column({ serializeAs: null })
	public userByCanceledId: number | null

	@column({ serializeAs: null })
	public scheduleId: number

	@column()
	public partnerType: 'doctor' | 'clinic' | 'lab'

	@column.dateTime({
		autoCreate: true, // import
		serialize: (value: DateTime) => {
			if (value) return value.toFormat('yyyy-MM-dd HH:mm:ss').replace(/\s+/g, 'T')
			return
		},
	})
	public date: DateTime

	@column()
	public currentStatus: 'open' | 'closed'

	@column()
	public status:
		'waiting' |
		'approved' |
		'did_not_attend' |
		'realized' |
		'finalized' |
		'canceled' |
		'canceled_by_patient' |
		'canceled_at_patient_request' |
		'canceled_by_backoffice'

	@column.dateTime({
		autoCreate: true,
		serialize: (value: DateTime) => {
			if (value) return value.toFormat('yyyy-MM-dd HH:mm:ss').replace(/\s+/g, 'T')
			return
		},
	})
	public dateCanceled: DateTime | null

	@column()
	public typeCanceled: 'patient' | 'partner' | 'backoffice'

	@column()
	public motiveCanceled: string

	@belongsTo(() => User)
	public user: BelongsTo<typeof User>

	@hasOne(() => User, {
		localKey: 'patientId',
		foreignKey: 'id',
	})
	public patient: HasOne<typeof User>

	@hasOne(() => User, {
		localKey: 'partnerId',
		foreignKey: 'id',
	})
	public partner: HasOne<typeof User>

	@belongsTo(() => Specialty)
	public specialty: BelongsTo<typeof Specialty>

	@belongsTo(() => Exam)
	public exam: BelongsTo<typeof Exam>

	@belongsTo(() => Schedule)
	public schedule: BelongsTo<typeof Schedule>

	@belongsTo(() => User, {
		localKey: 'userByCanceledId',
		foreignKey: 'id',
	})
	public userByCanceled: BelongsTo<typeof User>

	@column.dateTime({
		autoCreate: true,
		serialize: (value: DateTime) => {
			if (value) return value.toFormat('yyyy-MM-dd HH:mm:ss').replace(/\s+/g, 'T')
			return
		},
	})
	public createdAt: DateTime

	@column.dateTime({
		autoCreate: true,
		autoUpdate: true,
		serialize: (value: DateTime) => {
			if (value) return value.toFormat('yyyy-MM-dd HH:mm:ss').replace(/\s+/g, 'T')
			return
		},
	})
	public updatedAt: DateTime

	@beforeCreate()
	public static async createUUID(model: Appointment) {
		model.secureId = uuid()
	}
}
