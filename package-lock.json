{"name": "hello-med", "version": "1.0.0", "lockfileVersion": 3, "requires": true, "packages": {"": {"name": "hello-med", "version": "1.0.0", "dependencies": {"@adonisjs/auth": "^8.2.3", "@adonisjs/core": "^5.8.0", "@adonisjs/drive-s3": "^1.3.2", "@adonisjs/i18n": "^1.5.6", "@adonisjs/lucid": "^18.3.0", "@adonisjs/mail": "^8.2.0", "@adonisjs/repl": "^3.1.0", "@adonisjs/view": "^6.2.0", "@onesignal/node-onesignal": "^2.0.1-beta2", "@zenvia/sdk": "^2.4.4", "adonis5-swagger": "^1.4.1", "date-fns": "^2.30.0", "date-fns-tz": "^2.0.0", "luxon": "^3.3.0", "marked": "^4.3.0", "mysql2": "^3.2.4", "proxy-addr": "^2.0.7", "reflect-metadata": "^0.1.13", "source-map-support": "^0.5.21", "twilio": "^4.18.0", "uuid": "^9.0.0"}, "devDependencies": {"@adonisjs/assembler": "^5.9.5", "@japa/preset-adonis": "^1.2.0", "@japa/runner": "^2.5.1", "@types/proxy-addr": "^2.0.0", "@types/source-map-support": "^0.5.6", "@types/uuid": "^9.0.2", "adonis-preset-ts": "^2.1.0", "eslint": "^8.39.0", "eslint-config-prettier": "^8.8.0", "eslint-plugin-adonis": "^2.1.1", "eslint-plugin-prettier": "^4.2.1", "pino-pretty": "^10.0.0", "prettier": "^2.8.8", "typescript": "~4.6", "youch": "^3.2.3", "youch-terminal": "^2.2.0"}}, "node_modules/@aashutoshrathi/word-wrap": {"version": "1.2.6", "dev": true, "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/@adonisjs/ace": {"version": "11.3.1", "license": "MIT", "dependencies": {"@poppinss/cliui": "^3.0.2", "@poppinss/prompts": "^2.0.2", "@poppinss/utils": "^4.0.4", "fs-extra": "^10.1.0", "getopts": "^2.3.0", "leven": "^3.1.0", "mustache": "^4.2.0", "slash": "^3.0.0", "term-size": "^2.2.1"}, "peerDependencies": {"@adonisjs/application": "^5.0.0"}}, "node_modules/@adonisjs/ace/node_modules/@poppinss/utils": {"version": "4.0.4", "license": "MIT", "dependencies": {"@poppinss/file-generator": "^1.0.2", "@types/bytes": "^3.1.1", "@types/he": "^1.1.2", "bytes": "^3.1.2", "change-case": "^4.1.2", "cuid": "^2.1.8", "flattie": "^1.1.0", "fs-readdir-recursive": "^1.1.0", "he": "^1.2.0", "kind-of": "^6.0.3", "lodash": "^4.17.21", "ms": "^2.1.3", "pluralize": "^8.0.0", "require-all": "^3.0.0", "resolve-from": "^5.0.0", "slugify": "^1.6.5", "truncatise": "0.0.8"}}, "node_modules/@adonisjs/application": {"version": "5.3.0", "license": "MIT", "dependencies": {"@adonisjs/config": "^3.0.9", "@adonisjs/env": "^3.0.9", "@adonisjs/fold": "^8.2.0", "@adonisjs/logger": "^4.1.5", "@adonisjs/profiler": "^6.0.9", "@poppinss/utils": "^5.0.0", "semver": "^7.3.8"}}, "node_modules/@adonisjs/assembler": {"version": "5.9.5", "dev": true, "license": "MIT", "dependencies": {"@adonisjs/application": "^5.2.5", "@adonisjs/env": "^3.0.9", "@adonisjs/ioc-transformer": "^2.3.4", "@adonisjs/require-ts": "^2.0.13", "@adonisjs/sink": "^5.4.2", "@poppinss/chokidar-ts": "^3.3.5", "@poppinss/cliui": "^3.0.5", "@poppinss/utils": "^5.0.0", "cpy": "^8.1.2", "emittery": "^0.13.1", "execa": "^5.1.1", "fs-extra": "^10.1.0", "get-port": "^5.1.1", "glob-parent": "^6.0.2", "has-yarn": "^2.1.0", "picomatch": "^2.3.1", "slash": "^3.0.0"}, "peerDependencies": {"@adonisjs/core": "^5.1.0"}}, "node_modules/@adonisjs/auth": {"version": "8.2.3", "license": "MIT", "dependencies": {"@poppinss/hooks": "^5.0.3", "@poppinss/utils": "^5.0.0", "luxon": "^3.0.4"}, "peerDependencies": {"@adonisjs/core": "^5.7.1", "@adonisjs/i18n": "^1.5.0", "@adonisjs/lucid": "^18.0.0", "@adonisjs/redis": "^7.2.0", "@adonisjs/session": "^6.2.0"}, "peerDependenciesMeta": {"@adonisjs/i18n": {"optional": true}, "@adonisjs/lucid": {"optional": true}, "@adonisjs/redis": {"optional": true}, "@adonisjs/session": {"optional": true}}}, "node_modules/@adonisjs/bodyparser": {"version": "8.1.9", "license": "MIT", "dependencies": {"@poppinss/co-body": "^1.1.3", "@poppinss/multiparty": "^2.0.1", "@poppinss/utils": "^5.0.0", "bytes": "^3.1.2", "file-type": "^16.5.4", "fs-extra": "^10.1.0", "media-typer": "^1.1.0", "slash": "^3.0.0"}, "peerDependencies": {"@adonisjs/application": "^5.0.0", "@adonisjs/drive": "^2.0.0", "@adonisjs/http-server": "^5.0.0"}}, "node_modules/@adonisjs/config": {"version": "3.0.9", "license": "MIT", "dependencies": {"@poppinss/utils": "^5.0.0"}}, "node_modules/@adonisjs/core": {"version": "5.9.0", "license": "MIT", "dependencies": {"@adonisjs/ace": "^11.3.1", "@adonisjs/application": "^5.3.0", "@adonisjs/bodyparser": "^8.1.7", "@adonisjs/drive": "^2.3.0", "@adonisjs/encryption": "^4.0.8", "@adonisjs/events": "^7.2.1", "@adonisjs/hash": "^7.2.2", "@adonisjs/http-server": "^5.12.0", "@adonisjs/validator": "^12.4.1", "@poppinss/cliui": "^3.0.5", "@poppinss/manager": "^5.0.2", "@poppinss/utils": "^5.0.0", "fs-extra": "^10.1.0", "macroable": "^7.0.2", "memfs": "^3.4.12", "serve-static": "^1.15.0", "stringify-attributes": "^2.0.0"}}, "node_modules/@adonisjs/drive": {"version": "2.3.0", "license": "MIT", "dependencies": {"@poppinss/manager": "^5.0.2", "@poppinss/utils": "^5.0.0", "@types/fs-extra": "^9.0.13", "etag": "^1.8.1", "fs-extra": "^10.1.0", "memfs": "^3.4.7"}, "peerDependencies": {"@adonisjs/application": "^5.0.0", "@adonisjs/http-server": "^5.0.0"}}, "node_modules/@adonisjs/drive-s3": {"version": "1.3.3", "license": "MIT", "dependencies": {"@aws-sdk/client-s3": "^3.405.0", "@aws-sdk/lib-storage": "^3.405.0", "@aws-sdk/middleware-endpoint": "^3.374.0", "@aws-sdk/s3-request-presigner": "^3.405.0", "get-stream": "^6.0.1"}, "engines": {"node": ">14.16.0"}, "peerDependencies": {"@adonisjs/core": "^5.0.0"}}, "node_modules/@adonisjs/encryption": {"version": "4.0.8", "license": "MIT", "dependencies": {"@poppinss/utils": "^4.0.3"}, "peerDependencies": {"@adonisjs/application": "^5.0.0"}}, "node_modules/@adonisjs/encryption/node_modules/@poppinss/utils": {"version": "4.0.4", "license": "MIT", "dependencies": {"@poppinss/file-generator": "^1.0.2", "@types/bytes": "^3.1.1", "@types/he": "^1.1.2", "bytes": "^3.1.2", "change-case": "^4.1.2", "cuid": "^2.1.8", "flattie": "^1.1.0", "fs-readdir-recursive": "^1.1.0", "he": "^1.2.0", "kind-of": "^6.0.3", "lodash": "^4.17.21", "ms": "^2.1.3", "pluralize": "^8.0.0", "require-all": "^3.0.0", "resolve-from": "^5.0.0", "slugify": "^1.6.5", "truncatise": "0.0.8"}}, "node_modules/@adonisjs/env": {"version": "3.0.9", "license": "MIT", "dependencies": {"@poppinss/utils": "^4.0.2", "dotenv": "^16.0.0", "validator": "^13.7.0"}}, "node_modules/@adonisjs/env/node_modules/@poppinss/utils": {"version": "4.0.4", "license": "MIT", "dependencies": {"@poppinss/file-generator": "^1.0.2", "@types/bytes": "^3.1.1", "@types/he": "^1.1.2", "bytes": "^3.1.2", "change-case": "^4.1.2", "cuid": "^2.1.8", "flattie": "^1.1.0", "fs-readdir-recursive": "^1.1.0", "he": "^1.2.0", "kind-of": "^6.0.3", "lodash": "^4.17.21", "ms": "^2.1.3", "pluralize": "^8.0.0", "require-all": "^3.0.0", "resolve-from": "^5.0.0", "slugify": "^1.6.5", "truncatise": "0.0.8"}}, "node_modules/@adonisjs/events": {"version": "7.2.1", "license": "MIT", "dependencies": {"emittery": "^0.10.0"}, "peerDependencies": {"@adonisjs/application": "^5.0.0"}}, "node_modules/@adonisjs/events/node_modules/emittery": {"version": "0.10.2", "license": "MIT", "engines": {"node": ">=12"}, "funding": {"url": "https://github.com/sindresorhus/emittery?sponsor=1"}}, "node_modules/@adonisjs/fold": {"version": "8.2.0", "license": "MIT", "dependencies": {"@poppinss/utils": "^4.0.4"}}, "node_modules/@adonisjs/fold/node_modules/@poppinss/utils": {"version": "4.0.4", "license": "MIT", "dependencies": {"@poppinss/file-generator": "^1.0.2", "@types/bytes": "^3.1.1", "@types/he": "^1.1.2", "bytes": "^3.1.2", "change-case": "^4.1.2", "cuid": "^2.1.8", "flattie": "^1.1.0", "fs-readdir-recursive": "^1.1.0", "he": "^1.2.0", "kind-of": "^6.0.3", "lodash": "^4.17.21", "ms": "^2.1.3", "pluralize": "^8.0.0", "require-all": "^3.0.0", "resolve-from": "^5.0.0", "slugify": "^1.6.5", "truncatise": "0.0.8"}}, "node_modules/@adonisjs/hash": {"version": "7.2.2", "license": "MIT", "dependencies": {"@phc/format": "^1.0.0", "@poppinss/manager": "^5.0.2", "@poppinss/utils": "^5.0.0"}, "peerDependencies": {"@adonisjs/application": "^5.0.0"}}, "node_modules/@adonisjs/http-server": {"version": "5.12.0", "license": "MIT", "dependencies": {"@poppinss/matchit": "^3.1.2", "@poppinss/utils": "^5.0.0", "accepts": "^1.3.8", "co-compose": "^7.0.2", "content-disposition": "^0.5.4", "cookie": "^0.5.0", "destroy": "^1.2.0", "encodeurl": "^1.0.2", "etag": "^1.8.1", "fresh": "^0.5.2", "haye": "^3.0.0", "macroable": "^7.0.2", "mime-types": "^2.1.35", "ms": "^2.1.3", "on-finished": "^2.4.1", "pluralize": "^8.0.0", "proxy-addr": "^2.0.7", "qs": "^6.11.0", "tmp-cache": "^1.1.0", "type-is": "^1.6.18", "vary": "^1.1.2"}, "peerDependencies": {"@adonisjs/application": "^5.0.0", "@adonisjs/encryption": "^4.0.0"}}, "node_modules/@adonisjs/i18n": {"version": "1.6.0", "license": "MIT", "dependencies": {"@poppinss/colors": "^3.0.2", "@poppinss/intl-formatter": "^2.0.3", "@poppinss/utils": "^4.0.4", "accepts": "^1.3.8", "intl-messageformat": "^10.0.1", "luxon": "^2.4.0", "yaml": "^2.1.0"}, "peerDependencies": {"@adonisjs/core": "^5.4.0"}}, "node_modules/@adonisjs/i18n/node_modules/@poppinss/utils": {"version": "4.0.4", "license": "MIT", "dependencies": {"@poppinss/file-generator": "^1.0.2", "@types/bytes": "^3.1.1", "@types/he": "^1.1.2", "bytes": "^3.1.2", "change-case": "^4.1.2", "cuid": "^2.1.8", "flattie": "^1.1.0", "fs-readdir-recursive": "^1.1.0", "he": "^1.2.0", "kind-of": "^6.0.3", "lodash": "^4.17.21", "ms": "^2.1.3", "pluralize": "^8.0.0", "require-all": "^3.0.0", "resolve-from": "^5.0.0", "slugify": "^1.6.5", "truncatise": "0.0.8"}}, "node_modules/@adonisjs/i18n/node_modules/luxon": {"version": "2.5.2", "license": "MIT", "engines": {"node": ">=12"}}, "node_modules/@adonisjs/ioc-transformer": {"version": "2.3.4", "dev": true, "license": "MIT"}, "node_modules/@adonisjs/logger": {"version": "4.1.5", "license": "MIT", "dependencies": {"@poppinss/utils": "^5.0.0", "@types/pino": "^6.3.12", "abstract-logging": "^2.0.1", "pino": "^6.14.0"}}, "node_modules/@adonisjs/lucid": {"version": "18.4.0", "license": "MIT", "dependencies": {"@faker-js/faker": "^8.0.1", "@poppinss/hooks": "^5.0.3", "@poppinss/utils": "^5.0.0", "fast-deep-equal": "^3.1.3", "igniculus": "^1.5.0", "knex": "^2.4.2", "knex-dynamic-connection": "^3.0.1", "luxon": "^3.3.0", "macroable": "^7.0.2", "pretty-hrtime": "^1.0.3", "qs": "^6.11.2", "slash": "^3.0.0", "tarn": "^3.0.2"}, "peerDependencies": {"@adonisjs/core": "^5.1.0"}}, "node_modules/@adonisjs/mail": {"version": "8.2.1", "license": "MIT", "dependencies": {"@poppinss/colors": "^3.0.3", "@poppinss/manager": "^5.0.2", "@poppinss/utils": "^4.0.4", "fastq": "^1.15.0", "get-stream": "^6.0.1", "got": "^11.8.2", "ical-generator": "^4.0.0", "multi-part": "^3.0.0", "nodemailer": "^6.9.1"}, "peerDependencies": {"@adonisjs/core": "^5.1.0", "@adonisjs/view": "^6.0.0"}}, "node_modules/@adonisjs/mail/node_modules/@poppinss/utils": {"version": "4.0.4", "license": "MIT", "dependencies": {"@poppinss/file-generator": "^1.0.2", "@types/bytes": "^3.1.1", "@types/he": "^1.1.2", "bytes": "^3.1.2", "change-case": "^4.1.2", "cuid": "^2.1.8", "flattie": "^1.1.0", "fs-readdir-recursive": "^1.1.0", "he": "^1.2.0", "kind-of": "^6.0.3", "lodash": "^4.17.21", "ms": "^2.1.3", "pluralize": "^8.0.0", "require-all": "^3.0.0", "resolve-from": "^5.0.0", "slugify": "^1.6.5", "truncatise": "0.0.8"}}, "node_modules/@adonisjs/profiler": {"version": "6.0.9", "license": "MIT", "dependencies": {"@poppinss/utils": "^4.0.3", "jest-worker": "^27.5.1"}, "peerDependencies": {"@adonisjs/logger": "^4.0.0"}}, "node_modules/@adonisjs/profiler/node_modules/@poppinss/utils": {"version": "4.0.4", "license": "MIT", "dependencies": {"@poppinss/file-generator": "^1.0.2", "@types/bytes": "^3.1.1", "@types/he": "^1.1.2", "bytes": "^3.1.2", "change-case": "^4.1.2", "cuid": "^2.1.8", "flattie": "^1.1.0", "fs-readdir-recursive": "^1.1.0", "he": "^1.2.0", "kind-of": "^6.0.3", "lodash": "^4.17.21", "ms": "^2.1.3", "pluralize": "^8.0.0", "require-all": "^3.0.0", "resolve-from": "^5.0.0", "slugify": "^1.6.5", "truncatise": "0.0.8"}}, "node_modules/@adonisjs/repl": {"version": "3.1.11", "license": "MIT", "dependencies": {"@poppinss/colors": "^3.0.2", "node-repl-await": "^0.1.2", "parse-imports": "0.0.5", "string-width": "^4.2.2"}, "peerDependencies": {"@adonisjs/core": "^5.1.0"}}, "node_modules/@adonisjs/require-ts": {"version": "2.0.13", "dev": true, "license": "MIT", "dependencies": {"@poppinss/utils": "^5.0.0", "debug": "^4.3.4", "find-cache-dir": "^3.3.2", "fs-extra": "^10.1.0", "normalize-path": "^3.0.0", "pirates": "^4.0.5", "rev-hash": "^3.0.0", "source-map-support": "^0.5.21"}}, "node_modules/@adonisjs/sink": {"version": "5.4.3", "dev": true, "license": "MIT", "dependencies": {"@poppinss/cliui": "^3.0.5", "@poppinss/prompts": "^2.0.2", "@poppinss/utils": "^5.0.0", "cp-file": "^9.1.0", "fs-extra": "^10.1.0", "marked": "^4.2.12", "marked-terminal": "^5.1.1", "mrm-core": "7.1.13", "mustache": "^4.2.0", "open": "^8.4.2"}, "peerDependencies": {"@adonisjs/application": "^5.0.0"}}, "node_modules/@adonisjs/validator": {"version": "12.5.0", "license": "MIT", "dependencies": {"@poppinss/utils": "^5.0.0", "@types/luxon": "^3.3.1", "@types/validator": "^13.11.1", "luxon": "^3.4.1", "normalize-url": "^6.1.0", "tmp-cache": "^1.1.0", "validator": "^13.11.0"}, "peerDependencies": {"@adonisjs/application": "^5.0.0", "@adonisjs/bodyparser": "^8.0.0", "@adonisjs/http-server": "^5.0.0"}}, "node_modules/@adonisjs/view": {"version": "6.2.0", "license": "MIT", "dependencies": {"edge-error": "^2.0.8", "edge-supercharged": "^3.1.1", "edge.js": "^5.5.0"}, "peerDependencies": {"@adonisjs/core": "^5.7.0"}}, "node_modules/@apidevtools/json-schema-ref-parser": {"version": "9.1.2", "license": "MIT", "dependencies": {"@jsdevtools/ono": "^7.1.3", "@types/json-schema": "^7.0.6", "call-me-maybe": "^1.0.1", "js-yaml": "^4.1.0"}}, "node_modules/@apidevtools/json-schema-ref-parser/node_modules/argparse": {"version": "2.0.1", "license": "Python-2.0"}, "node_modules/@apidevtools/json-schema-ref-parser/node_modules/js-yaml": {"version": "4.1.0", "license": "MIT", "dependencies": {"argparse": "^2.0.1"}, "bin": {"js-yaml": "bin/js-yaml.js"}}, "node_modules/@apidevtools/openapi-schemas": {"version": "2.1.0", "license": "MIT", "engines": {"node": ">=10"}}, "node_modules/@apidevtools/swagger-methods": {"version": "3.0.2", "license": "MIT"}, "node_modules/@arr/every": {"version": "1.0.1", "license": "MIT", "engines": {"node": ">=4"}}, "node_modules/@aws-crypto/crc32": {"version": "3.0.0", "license": "Apache-2.0", "dependencies": {"@aws-crypto/util": "^3.0.0", "@aws-sdk/types": "^3.222.0", "tslib": "^1.11.1"}}, "node_modules/@aws-crypto/crc32/node_modules/tslib": {"version": "1.14.1", "license": "0BSD"}, "node_modules/@aws-crypto/crc32c": {"version": "3.0.0", "license": "Apache-2.0", "dependencies": {"@aws-crypto/util": "^3.0.0", "@aws-sdk/types": "^3.222.0", "tslib": "^1.11.1"}}, "node_modules/@aws-crypto/crc32c/node_modules/tslib": {"version": "1.14.1", "license": "0BSD"}, "node_modules/@aws-crypto/ie11-detection": {"version": "3.0.0", "license": "Apache-2.0", "dependencies": {"tslib": "^1.11.1"}}, "node_modules/@aws-crypto/ie11-detection/node_modules/tslib": {"version": "1.14.1", "license": "0BSD"}, "node_modules/@aws-crypto/sha1-browser": {"version": "3.0.0", "license": "Apache-2.0", "dependencies": {"@aws-crypto/ie11-detection": "^3.0.0", "@aws-crypto/supports-web-crypto": "^3.0.0", "@aws-crypto/util": "^3.0.0", "@aws-sdk/types": "^3.222.0", "@aws-sdk/util-locate-window": "^3.0.0", "@aws-sdk/util-utf8-browser": "^3.0.0", "tslib": "^1.11.1"}}, "node_modules/@aws-crypto/sha1-browser/node_modules/tslib": {"version": "1.14.1", "license": "0BSD"}, "node_modules/@aws-crypto/sha256-browser": {"version": "3.0.0", "license": "Apache-2.0", "dependencies": {"@aws-crypto/ie11-detection": "^3.0.0", "@aws-crypto/sha256-js": "^3.0.0", "@aws-crypto/supports-web-crypto": "^3.0.0", "@aws-crypto/util": "^3.0.0", "@aws-sdk/types": "^3.222.0", "@aws-sdk/util-locate-window": "^3.0.0", "@aws-sdk/util-utf8-browser": "^3.0.0", "tslib": "^1.11.1"}}, "node_modules/@aws-crypto/sha256-browser/node_modules/tslib": {"version": "1.14.1", "license": "0BSD"}, "node_modules/@aws-crypto/sha256-js": {"version": "3.0.0", "license": "Apache-2.0", "dependencies": {"@aws-crypto/util": "^3.0.0", "@aws-sdk/types": "^3.222.0", "tslib": "^1.11.1"}}, "node_modules/@aws-crypto/sha256-js/node_modules/tslib": {"version": "1.14.1", "license": "0BSD"}, "node_modules/@aws-crypto/supports-web-crypto": {"version": "3.0.0", "license": "Apache-2.0", "dependencies": {"tslib": "^1.11.1"}}, "node_modules/@aws-crypto/supports-web-crypto/node_modules/tslib": {"version": "1.14.1", "license": "0BSD"}, "node_modules/@aws-crypto/util": {"version": "3.0.0", "license": "Apache-2.0", "dependencies": {"@aws-sdk/types": "^3.222.0", "@aws-sdk/util-utf8-browser": "^3.0.0", "tslib": "^1.11.1"}}, "node_modules/@aws-crypto/util/node_modules/tslib": {"version": "1.14.1", "license": "0BSD"}, "node_modules/@aws-sdk/client-s3": {"version": "3.412.0", "license": "Apache-2.0", "dependencies": {"@aws-crypto/sha1-browser": "3.0.0", "@aws-crypto/sha256-browser": "3.0.0", "@aws-crypto/sha256-js": "3.0.0", "@aws-sdk/client-sts": "3.410.0", "@aws-sdk/credential-provider-node": "3.410.0", "@aws-sdk/middleware-bucket-endpoint": "3.410.0", "@aws-sdk/middleware-expect-continue": "3.410.0", "@aws-sdk/middleware-flexible-checksums": "3.410.0", "@aws-sdk/middleware-host-header": "3.410.0", "@aws-sdk/middleware-location-constraint": "3.410.0", "@aws-sdk/middleware-logger": "3.410.0", "@aws-sdk/middleware-recursion-detection": "3.410.0", "@aws-sdk/middleware-sdk-s3": "3.410.0", "@aws-sdk/middleware-signing": "3.410.0", "@aws-sdk/middleware-ssec": "3.410.0", "@aws-sdk/middleware-user-agent": "3.410.0", "@aws-sdk/signature-v4-multi-region": "3.412.0", "@aws-sdk/types": "3.410.0", "@aws-sdk/util-endpoints": "3.410.0", "@aws-sdk/util-user-agent-browser": "3.410.0", "@aws-sdk/util-user-agent-node": "3.410.0", "@aws-sdk/xml-builder": "3.310.0", "@smithy/config-resolver": "^2.0.7", "@smithy/eventstream-serde-browser": "^2.0.6", "@smithy/eventstream-serde-config-resolver": "^2.0.6", "@smithy/eventstream-serde-node": "^2.0.6", "@smithy/fetch-http-handler": "^2.1.2", "@smithy/hash-blob-browser": "^2.0.6", "@smithy/hash-node": "^2.0.6", "@smithy/hash-stream-node": "^2.0.6", "@smithy/invalid-dependency": "^2.0.6", "@smithy/md5-js": "^2.0.6", "@smithy/middleware-content-length": "^2.0.8", "@smithy/middleware-endpoint": "^2.0.6", "@smithy/middleware-retry": "^2.0.9", "@smithy/middleware-serde": "^2.0.6", "@smithy/middleware-stack": "^2.0.0", "@smithy/node-config-provider": "^2.0.9", "@smithy/node-http-handler": "^2.1.2", "@smithy/protocol-http": "^3.0.2", "@smithy/smithy-client": "^2.1.3", "@smithy/types": "^2.3.0", "@smithy/url-parser": "^2.0.6", "@smithy/util-base64": "^2.0.0", "@smithy/util-body-length-browser": "^2.0.0", "@smithy/util-body-length-node": "^2.1.0", "@smithy/util-defaults-mode-browser": "^2.0.7", "@smithy/util-defaults-mode-node": "^2.0.9", "@smithy/util-retry": "^2.0.0", "@smithy/util-stream": "^2.0.9", "@smithy/util-utf8": "^2.0.0", "@smithy/util-waiter": "^2.0.6", "fast-xml-parser": "4.2.5", "tslib": "^2.5.0"}, "engines": {"node": ">=14.0.0"}}, "node_modules/@aws-sdk/client-sso": {"version": "3.410.0", "license": "Apache-2.0", "dependencies": {"@aws-crypto/sha256-browser": "3.0.0", "@aws-crypto/sha256-js": "3.0.0", "@aws-sdk/middleware-host-header": "3.410.0", "@aws-sdk/middleware-logger": "3.410.0", "@aws-sdk/middleware-recursion-detection": "3.410.0", "@aws-sdk/middleware-user-agent": "3.410.0", "@aws-sdk/types": "3.410.0", "@aws-sdk/util-endpoints": "3.410.0", "@aws-sdk/util-user-agent-browser": "3.410.0", "@aws-sdk/util-user-agent-node": "3.410.0", "@smithy/config-resolver": "^2.0.7", "@smithy/fetch-http-handler": "^2.1.2", "@smithy/hash-node": "^2.0.6", "@smithy/invalid-dependency": "^2.0.6", "@smithy/middleware-content-length": "^2.0.8", "@smithy/middleware-endpoint": "^2.0.6", "@smithy/middleware-retry": "^2.0.9", "@smithy/middleware-serde": "^2.0.6", "@smithy/middleware-stack": "^2.0.0", "@smithy/node-config-provider": "^2.0.9", "@smithy/node-http-handler": "^2.1.2", "@smithy/protocol-http": "^3.0.2", "@smithy/smithy-client": "^2.1.3", "@smithy/types": "^2.3.0", "@smithy/url-parser": "^2.0.6", "@smithy/util-base64": "^2.0.0", "@smithy/util-body-length-browser": "^2.0.0", "@smithy/util-body-length-node": "^2.1.0", "@smithy/util-defaults-mode-browser": "^2.0.7", "@smithy/util-defaults-mode-node": "^2.0.9", "@smithy/util-retry": "^2.0.0", "@smithy/util-utf8": "^2.0.0", "tslib": "^2.5.0"}, "engines": {"node": ">=14.0.0"}}, "node_modules/@aws-sdk/client-sts": {"version": "3.410.0", "license": "Apache-2.0", "dependencies": {"@aws-crypto/sha256-browser": "3.0.0", "@aws-crypto/sha256-js": "3.0.0", "@aws-sdk/credential-provider-node": "3.410.0", "@aws-sdk/middleware-host-header": "3.410.0", "@aws-sdk/middleware-logger": "3.410.0", "@aws-sdk/middleware-recursion-detection": "3.410.0", "@aws-sdk/middleware-sdk-sts": "3.410.0", "@aws-sdk/middleware-signing": "3.410.0", "@aws-sdk/middleware-user-agent": "3.410.0", "@aws-sdk/types": "3.410.0", "@aws-sdk/util-endpoints": "3.410.0", "@aws-sdk/util-user-agent-browser": "3.410.0", "@aws-sdk/util-user-agent-node": "3.410.0", "@smithy/config-resolver": "^2.0.7", "@smithy/fetch-http-handler": "^2.1.2", "@smithy/hash-node": "^2.0.6", "@smithy/invalid-dependency": "^2.0.6", "@smithy/middleware-content-length": "^2.0.8", "@smithy/middleware-endpoint": "^2.0.6", "@smithy/middleware-retry": "^2.0.9", "@smithy/middleware-serde": "^2.0.6", "@smithy/middleware-stack": "^2.0.0", "@smithy/node-config-provider": "^2.0.9", "@smithy/node-http-handler": "^2.1.2", "@smithy/protocol-http": "^3.0.2", "@smithy/smithy-client": "^2.1.3", "@smithy/types": "^2.3.0", "@smithy/url-parser": "^2.0.6", "@smithy/util-base64": "^2.0.0", "@smithy/util-body-length-browser": "^2.0.0", "@smithy/util-body-length-node": "^2.1.0", "@smithy/util-defaults-mode-browser": "^2.0.7", "@smithy/util-defaults-mode-node": "^2.0.9", "@smithy/util-retry": "^2.0.0", "@smithy/util-utf8": "^2.0.0", "fast-xml-parser": "4.2.5", "tslib": "^2.5.0"}, "engines": {"node": ">=14.0.0"}}, "node_modules/@aws-sdk/credential-provider-env": {"version": "3.410.0", "license": "Apache-2.0", "dependencies": {"@aws-sdk/types": "3.410.0", "@smithy/property-provider": "^2.0.0", "@smithy/types": "^2.3.0", "tslib": "^2.5.0"}, "engines": {"node": ">=14.0.0"}}, "node_modules/@aws-sdk/credential-provider-ini": {"version": "3.410.0", "license": "Apache-2.0", "dependencies": {"@aws-sdk/credential-provider-env": "3.410.0", "@aws-sdk/credential-provider-process": "3.410.0", "@aws-sdk/credential-provider-sso": "3.410.0", "@aws-sdk/credential-provider-web-identity": "3.410.0", "@aws-sdk/types": "3.410.0", "@smithy/credential-provider-imds": "^2.0.0", "@smithy/property-provider": "^2.0.0", "@smithy/shared-ini-file-loader": "^2.0.6", "@smithy/types": "^2.3.0", "tslib": "^2.5.0"}, "engines": {"node": ">=14.0.0"}}, "node_modules/@aws-sdk/credential-provider-node": {"version": "3.410.0", "license": "Apache-2.0", "dependencies": {"@aws-sdk/credential-provider-env": "3.410.0", "@aws-sdk/credential-provider-ini": "3.410.0", "@aws-sdk/credential-provider-process": "3.410.0", "@aws-sdk/credential-provider-sso": "3.410.0", "@aws-sdk/credential-provider-web-identity": "3.410.0", "@aws-sdk/types": "3.410.0", "@smithy/credential-provider-imds": "^2.0.0", "@smithy/property-provider": "^2.0.0", "@smithy/shared-ini-file-loader": "^2.0.6", "@smithy/types": "^2.3.0", "tslib": "^2.5.0"}, "engines": {"node": ">=14.0.0"}}, "node_modules/@aws-sdk/credential-provider-process": {"version": "3.410.0", "license": "Apache-2.0", "dependencies": {"@aws-sdk/types": "3.410.0", "@smithy/property-provider": "^2.0.0", "@smithy/shared-ini-file-loader": "^2.0.6", "@smithy/types": "^2.3.0", "tslib": "^2.5.0"}, "engines": {"node": ">=14.0.0"}}, "node_modules/@aws-sdk/credential-provider-sso": {"version": "3.410.0", "license": "Apache-2.0", "dependencies": {"@aws-sdk/client-sso": "3.410.0", "@aws-sdk/token-providers": "3.410.0", "@aws-sdk/types": "3.410.0", "@smithy/property-provider": "^2.0.0", "@smithy/shared-ini-file-loader": "^2.0.6", "@smithy/types": "^2.3.0", "tslib": "^2.5.0"}, "engines": {"node": ">=14.0.0"}}, "node_modules/@aws-sdk/credential-provider-web-identity": {"version": "3.410.0", "license": "Apache-2.0", "dependencies": {"@aws-sdk/types": "3.410.0", "@smithy/property-provider": "^2.0.0", "@smithy/types": "^2.3.0", "tslib": "^2.5.0"}, "engines": {"node": ">=14.0.0"}}, "node_modules/@aws-sdk/lib-storage": {"version": "3.412.0", "license": "Apache-2.0", "dependencies": {"@smithy/abort-controller": "^2.0.1", "@smithy/middleware-endpoint": "^2.0.6", "@smithy/smithy-client": "^2.1.3", "buffer": "5.6.0", "events": "3.3.0", "stream-browserify": "3.0.0", "tslib": "^2.5.0"}, "engines": {"node": ">=14.0.0"}, "peerDependencies": {"@aws-sdk/client-s3": "^3.0.0"}}, "node_modules/@aws-sdk/middleware-bucket-endpoint": {"version": "3.410.0", "license": "Apache-2.0", "dependencies": {"@aws-sdk/types": "3.410.0", "@aws-sdk/util-arn-parser": "3.310.0", "@smithy/node-config-provider": "^2.0.9", "@smithy/protocol-http": "^3.0.2", "@smithy/types": "^2.3.0", "@smithy/util-config-provider": "^2.0.0", "tslib": "^2.5.0"}, "engines": {"node": ">=14.0.0"}}, "node_modules/@aws-sdk/middleware-endpoint": {"version": "3.374.0", "license": "Apache-2.0", "dependencies": {"@smithy/middleware-endpoint": "^1.0.2", "tslib": "^2.5.0"}, "engines": {"node": ">=14.0.0"}}, "node_modules/@aws-sdk/middleware-endpoint/node_modules/@smithy/middleware-endpoint": {"version": "1.1.0", "license": "Apache-2.0", "dependencies": {"@smithy/middleware-serde": "^1.1.0", "@smithy/types": "^1.2.0", "@smithy/url-parser": "^1.1.0", "@smithy/util-middleware": "^1.1.0", "tslib": "^2.5.0"}, "engines": {"node": ">=14.0.0"}}, "node_modules/@aws-sdk/middleware-endpoint/node_modules/@smithy/middleware-serde": {"version": "1.1.0", "license": "Apache-2.0", "dependencies": {"@smithy/types": "^1.2.0", "tslib": "^2.5.0"}, "engines": {"node": ">=14.0.0"}}, "node_modules/@aws-sdk/middleware-endpoint/node_modules/@smithy/querystring-parser": {"version": "1.1.0", "license": "Apache-2.0", "dependencies": {"@smithy/types": "^1.2.0", "tslib": "^2.5.0"}, "engines": {"node": ">=14.0.0"}}, "node_modules/@aws-sdk/middleware-endpoint/node_modules/@smithy/types": {"version": "1.2.0", "license": "Apache-2.0", "dependencies": {"tslib": "^2.5.0"}, "engines": {"node": ">=14.0.0"}}, "node_modules/@aws-sdk/middleware-endpoint/node_modules/@smithy/url-parser": {"version": "1.1.0", "license": "Apache-2.0", "dependencies": {"@smithy/querystring-parser": "^1.1.0", "@smithy/types": "^1.2.0", "tslib": "^2.5.0"}}, "node_modules/@aws-sdk/middleware-endpoint/node_modules/@smithy/util-middleware": {"version": "1.1.0", "license": "Apache-2.0", "dependencies": {"tslib": "^2.5.0"}, "engines": {"node": ">=14.0.0"}}, "node_modules/@aws-sdk/middleware-expect-continue": {"version": "3.410.0", "license": "Apache-2.0", "dependencies": {"@aws-sdk/types": "3.410.0", "@smithy/protocol-http": "^3.0.2", "@smithy/types": "^2.3.0", "tslib": "^2.5.0"}, "engines": {"node": ">=14.0.0"}}, "node_modules/@aws-sdk/middleware-flexible-checksums": {"version": "3.410.0", "license": "Apache-2.0", "dependencies": {"@aws-crypto/crc32": "3.0.0", "@aws-crypto/crc32c": "3.0.0", "@aws-sdk/types": "3.410.0", "@smithy/is-array-buffer": "^2.0.0", "@smithy/protocol-http": "^3.0.2", "@smithy/types": "^2.3.0", "@smithy/util-utf8": "^2.0.0", "tslib": "^2.5.0"}, "engines": {"node": ">=14.0.0"}}, "node_modules/@aws-sdk/middleware-host-header": {"version": "3.410.0", "license": "Apache-2.0", "dependencies": {"@aws-sdk/types": "3.410.0", "@smithy/protocol-http": "^3.0.2", "@smithy/types": "^2.3.0", "tslib": "^2.5.0"}, "engines": {"node": ">=14.0.0"}}, "node_modules/@aws-sdk/middleware-location-constraint": {"version": "3.410.0", "license": "Apache-2.0", "dependencies": {"@aws-sdk/types": "3.410.0", "@smithy/types": "^2.3.0", "tslib": "^2.5.0"}, "engines": {"node": ">=14.0.0"}}, "node_modules/@aws-sdk/middleware-logger": {"version": "3.410.0", "license": "Apache-2.0", "dependencies": {"@aws-sdk/types": "3.410.0", "@smithy/types": "^2.3.0", "tslib": "^2.5.0"}, "engines": {"node": ">=14.0.0"}}, "node_modules/@aws-sdk/middleware-recursion-detection": {"version": "3.410.0", "license": "Apache-2.0", "dependencies": {"@aws-sdk/types": "3.410.0", "@smithy/protocol-http": "^3.0.2", "@smithy/types": "^2.3.0", "tslib": "^2.5.0"}, "engines": {"node": ">=14.0.0"}}, "node_modules/@aws-sdk/middleware-sdk-s3": {"version": "3.410.0", "license": "Apache-2.0", "dependencies": {"@aws-sdk/types": "3.410.0", "@aws-sdk/util-arn-parser": "3.310.0", "@smithy/protocol-http": "^3.0.2", "@smithy/types": "^2.3.0", "tslib": "^2.5.0"}, "engines": {"node": ">=14.0.0"}}, "node_modules/@aws-sdk/middleware-sdk-sts": {"version": "3.410.0", "license": "Apache-2.0", "dependencies": {"@aws-sdk/middleware-signing": "3.410.0", "@aws-sdk/types": "3.410.0", "@smithy/types": "^2.3.0", "tslib": "^2.5.0"}, "engines": {"node": ">=14.0.0"}}, "node_modules/@aws-sdk/middleware-signing": {"version": "3.410.0", "license": "Apache-2.0", "dependencies": {"@aws-sdk/types": "3.410.0", "@smithy/property-provider": "^2.0.0", "@smithy/protocol-http": "^3.0.2", "@smithy/signature-v4": "^2.0.0", "@smithy/types": "^2.3.0", "@smithy/util-middleware": "^2.0.0", "tslib": "^2.5.0"}, "engines": {"node": ">=14.0.0"}}, "node_modules/@aws-sdk/middleware-ssec": {"version": "3.410.0", "license": "Apache-2.0", "dependencies": {"@aws-sdk/types": "3.410.0", "@smithy/types": "^2.3.0", "tslib": "^2.5.0"}, "engines": {"node": ">=14.0.0"}}, "node_modules/@aws-sdk/middleware-user-agent": {"version": "3.410.0", "license": "Apache-2.0", "dependencies": {"@aws-sdk/types": "3.410.0", "@aws-sdk/util-endpoints": "3.410.0", "@smithy/protocol-http": "^3.0.2", "@smithy/types": "^2.3.0", "tslib": "^2.5.0"}, "engines": {"node": ">=14.0.0"}}, "node_modules/@aws-sdk/s3-request-presigner": {"version": "3.412.0", "license": "Apache-2.0", "dependencies": {"@aws-sdk/signature-v4-multi-region": "3.412.0", "@aws-sdk/types": "3.410.0", "@aws-sdk/util-format-url": "3.410.0", "@smithy/middleware-endpoint": "^2.0.6", "@smithy/protocol-http": "^3.0.2", "@smithy/smithy-client": "^2.1.3", "@smithy/types": "^2.3.0", "tslib": "^2.5.0"}, "engines": {"node": ">=14.0.0"}}, "node_modules/@aws-sdk/signature-v4-multi-region": {"version": "3.412.0", "license": "Apache-2.0", "dependencies": {"@aws-sdk/types": "3.410.0", "@smithy/protocol-http": "^3.0.2", "@smithy/signature-v4": "^2.0.0", "@smithy/types": "^2.3.0", "tslib": "^2.5.0"}, "engines": {"node": ">=14.0.0"}}, "node_modules/@aws-sdk/token-providers": {"version": "3.410.0", "license": "Apache-2.0", "dependencies": {"@aws-crypto/sha256-browser": "3.0.0", "@aws-crypto/sha256-js": "3.0.0", "@aws-sdk/middleware-host-header": "3.410.0", "@aws-sdk/middleware-logger": "3.410.0", "@aws-sdk/middleware-recursion-detection": "3.410.0", "@aws-sdk/middleware-user-agent": "3.410.0", "@aws-sdk/types": "3.410.0", "@aws-sdk/util-endpoints": "3.410.0", "@aws-sdk/util-user-agent-browser": "3.410.0", "@aws-sdk/util-user-agent-node": "3.410.0", "@smithy/config-resolver": "^2.0.7", "@smithy/fetch-http-handler": "^2.1.2", "@smithy/hash-node": "^2.0.6", "@smithy/invalid-dependency": "^2.0.6", "@smithy/middleware-content-length": "^2.0.8", "@smithy/middleware-endpoint": "^2.0.6", "@smithy/middleware-retry": "^2.0.9", "@smithy/middleware-serde": "^2.0.6", "@smithy/middleware-stack": "^2.0.0", "@smithy/node-config-provider": "^2.0.9", "@smithy/node-http-handler": "^2.1.2", "@smithy/property-provider": "^2.0.0", "@smithy/protocol-http": "^3.0.2", "@smithy/shared-ini-file-loader": "^2.0.6", "@smithy/smithy-client": "^2.1.3", "@smithy/types": "^2.3.0", "@smithy/url-parser": "^2.0.6", "@smithy/util-base64": "^2.0.0", "@smithy/util-body-length-browser": "^2.0.0", "@smithy/util-body-length-node": "^2.1.0", "@smithy/util-defaults-mode-browser": "^2.0.7", "@smithy/util-defaults-mode-node": "^2.0.9", "@smithy/util-retry": "^2.0.0", "@smithy/util-utf8": "^2.0.0", "tslib": "^2.5.0"}, "engines": {"node": ">=14.0.0"}}, "node_modules/@aws-sdk/types": {"version": "3.410.0", "license": "Apache-2.0", "dependencies": {"@smithy/types": "^2.3.0", "tslib": "^2.5.0"}, "engines": {"node": ">=14.0.0"}}, "node_modules/@aws-sdk/util-arn-parser": {"version": "3.310.0", "license": "Apache-2.0", "dependencies": {"tslib": "^2.5.0"}, "engines": {"node": ">=14.0.0"}}, "node_modules/@aws-sdk/util-endpoints": {"version": "3.410.0", "license": "Apache-2.0", "dependencies": {"@aws-sdk/types": "3.410.0", "tslib": "^2.5.0"}, "engines": {"node": ">=14.0.0"}}, "node_modules/@aws-sdk/util-format-url": {"version": "3.410.0", "license": "Apache-2.0", "dependencies": {"@aws-sdk/types": "3.410.0", "@smithy/querystring-builder": "^2.0.6", "@smithy/types": "^2.3.0", "tslib": "^2.5.0"}, "engines": {"node": ">=14.0.0"}}, "node_modules/@aws-sdk/util-locate-window": {"version": "3.310.0", "license": "Apache-2.0", "dependencies": {"tslib": "^2.5.0"}, "engines": {"node": ">=14.0.0"}}, "node_modules/@aws-sdk/util-user-agent-browser": {"version": "3.410.0", "license": "Apache-2.0", "dependencies": {"@aws-sdk/types": "3.410.0", "@smithy/types": "^2.3.0", "bowser": "^2.11.0", "tslib": "^2.5.0"}}, "node_modules/@aws-sdk/util-user-agent-node": {"version": "3.410.0", "license": "Apache-2.0", "dependencies": {"@aws-sdk/types": "3.410.0", "@smithy/node-config-provider": "^2.0.9", "@smithy/types": "^2.3.0", "tslib": "^2.5.0"}, "engines": {"node": ">=14.0.0"}, "peerDependencies": {"aws-crt": ">=1.0.0"}, "peerDependenciesMeta": {"aws-crt": {"optional": true}}}, "node_modules/@aws-sdk/util-utf8-browser": {"version": "3.259.0", "license": "Apache-2.0", "dependencies": {"tslib": "^2.3.1"}}, "node_modules/@aws-sdk/xml-builder": {"version": "3.310.0", "license": "Apache-2.0", "dependencies": {"tslib": "^2.5.0"}, "engines": {"node": ">=14.0.0"}}, "node_modules/@babel/runtime": {"version": "7.22.15", "license": "MIT", "dependencies": {"regenerator-runtime": "^0.14.0"}, "engines": {"node": ">=6.9.0"}}, "node_modules/@colors/colors": {"version": "1.5.0", "license": "MIT", "optional": true, "engines": {"node": ">=0.1.90"}}, "node_modules/@eslint-community/eslint-utils": {"version": "4.4.0", "dev": true, "license": "MIT", "dependencies": {"eslint-visitor-keys": "^3.3.0"}, "engines": {"node": "^12.22.0 || ^14.17.0 || >=16.0.0"}, "peerDependencies": {"eslint": "^6.0.0 || ^7.0.0 || >=8.0.0"}}, "node_modules/@eslint-community/regexpp": {"version": "4.8.1", "dev": true, "license": "MIT", "engines": {"node": "^12.0.0 || ^14.0.0 || >=16.0.0"}}, "node_modules/@eslint/eslintrc": {"version": "2.1.2", "dev": true, "license": "MIT", "dependencies": {"ajv": "^6.12.4", "debug": "^4.3.2", "espree": "^9.6.0", "globals": "^13.19.0", "ignore": "^5.2.0", "import-fresh": "^3.2.1", "js-yaml": "^4.1.0", "minimatch": "^3.1.2", "strip-json-comments": "^3.1.1"}, "engines": {"node": "^12.22.0 || ^14.17.0 || >=16.0.0"}, "funding": {"url": "https://opencollective.com/eslint"}}, "node_modules/@eslint/eslintrc/node_modules/argparse": {"version": "2.0.1", "dev": true, "license": "Python-2.0"}, "node_modules/@eslint/eslintrc/node_modules/js-yaml": {"version": "4.1.0", "dev": true, "license": "MIT", "dependencies": {"argparse": "^2.0.1"}, "bin": {"js-yaml": "bin/js-yaml.js"}}, "node_modules/@eslint/js": {"version": "8.49.0", "dev": true, "license": "MIT", "engines": {"node": "^12.22.0 || ^14.17.0 || >=16.0.0"}}, "node_modules/@faker-js/faker": {"version": "8.0.2", "funding": [{"type": "opencollective", "url": "https://opencollective.com/fakerjs"}], "license": "MIT", "engines": {"node": "^14.17.0 || ^16.13.0 || >=18.0.0", "npm": ">=6.14.13"}}, "node_modules/@formatjs/ecma402-abstract": {"version": "1.17.2", "license": "MIT", "dependencies": {"@formatjs/intl-localematcher": "0.4.2", "tslib": "^2.4.0"}}, "node_modules/@formatjs/fast-memoize": {"version": "2.2.0", "license": "MIT", "dependencies": {"tslib": "^2.4.0"}}, "node_modules/@formatjs/icu-messageformat-parser": {"version": "2.6.2", "license": "MIT", "dependencies": {"@formatjs/ecma402-abstract": "1.17.2", "@formatjs/icu-skeleton-parser": "1.6.2", "tslib": "^2.4.0"}}, "node_modules/@formatjs/icu-skeleton-parser": {"version": "1.6.2", "license": "MIT", "dependencies": {"@formatjs/ecma402-abstract": "1.17.2", "tslib": "^2.4.0"}}, "node_modules/@formatjs/intl-localematcher": {"version": "0.4.2", "license": "MIT", "dependencies": {"tslib": "^2.4.0"}}, "node_modules/@humanwhocodes/config-array": {"version": "0.11.11", "dev": true, "license": "Apache-2.0", "dependencies": {"@humanwhocodes/object-schema": "^1.2.1", "debug": "^4.1.1", "minimatch": "^3.0.5"}, "engines": {"node": ">=10.10.0"}}, "node_modules/@humanwhocodes/module-importer": {"version": "1.0.1", "dev": true, "license": "Apache-2.0", "engines": {"node": ">=12.22"}, "funding": {"type": "github", "url": "https://github.com/sponsors/nzakas"}}, "node_modules/@humanwhocodes/object-schema": {"version": "1.2.1", "dev": true, "license": "BSD-3-<PERSON><PERSON>"}, "node_modules/@japa/api-client": {"version": "1.4.4", "dev": true, "license": "MIT", "dependencies": {"@poppinss/hooks": "^6.0.2-0", "@types/superagent": "^4.1.16", "cookie": "^0.5.0", "macroable": "^7.0.2", "set-cookie-parser": "^2.5.1", "superagent": "^8.0.9"}, "peerDependencies": {"@japa/runner": "^2.2.3"}}, "node_modules/@japa/api-client/node_modules/@poppinss/hooks": {"version": "6.0.2-0", "dev": true, "license": "MIT"}, "node_modules/@japa/assert": {"version": "1.4.1", "dev": true, "license": "MIT", "dependencies": {"@types/chai": "^4.3.4", "api-contract-validator": "^2.2.8", "chai": "^4.3.7", "macroable": "^7.0.2"}, "peerDependencies": {"@japa/runner": "^2.1.1"}}, "node_modules/@japa/base-reporter": {"version": "1.1.2", "dev": true, "license": "MIT", "dependencies": {"@japa/errors-printer": "^2.1.0", "@poppinss/cliui": "^3.0.5", "ms": "^2.1.3"}}, "node_modules/@japa/core": {"version": "7.3.3", "dev": true, "license": "MIT", "dependencies": {"@poppinss/hooks": "^6.0.2-0", "async-retry": "^1.3.3", "emittery": "^0.13.1", "macroable": "^7.0.2", "time-span": "^4.0.0"}}, "node_modules/@japa/core/node_modules/@poppinss/hooks": {"version": "6.0.2-0", "dev": true, "license": "MIT"}, "node_modules/@japa/errors-printer": {"version": "2.1.0", "dev": true, "license": "MIT", "dependencies": {"@poppinss/cliui": "^3.0.5", "jest-diff": "^29.4.1", "youch": "^3.2.3", "youch-terminal": "^2.2.0"}}, "node_modules/@japa/preset-adonis": {"version": "1.2.0", "dev": true, "license": "MIT", "dependencies": {"@japa/api-client": "^1.4.2", "@japa/assert": "^1.3.6", "@japa/run-failed-tests": "^1.1.0", "@japa/spec-reporter": "^1.3.0"}, "peerDependencies": {"@adonisjs/core": "^5.0.0", "@japa/runner": "^2.0.0"}}, "node_modules/@japa/run-failed-tests": {"version": "1.1.1", "dev": true, "license": "MIT", "dependencies": {"@poppinss/cliui": "^3.0.5", "find-cache-dir": "^3.3.2", "fs-extra": "^11.1.0"}, "peerDependencies": {"@japa/runner": "^2.2.3"}}, "node_modules/@japa/run-failed-tests/node_modules/fs-extra": {"version": "11.1.1", "dev": true, "license": "MIT", "dependencies": {"graceful-fs": "^4.2.0", "jsonfile": "^6.0.1", "universalify": "^2.0.0"}, "engines": {"node": ">=14.14"}}, "node_modules/@japa/runner": {"version": "2.5.1", "dev": true, "license": "MIT", "dependencies": {"@japa/core": "^7.3.2", "@japa/errors-printer": "^2.1.0", "@poppinss/cliui": "^3.0.5", "@poppinss/hooks": "^6.0.2-0", "fast-glob": "^3.2.12", "getopts": "^2.3.0", "inclusion": "^1.0.1"}}, "node_modules/@japa/runner/node_modules/@poppinss/hooks": {"version": "6.0.2-0", "dev": true, "license": "MIT"}, "node_modules/@japa/spec-reporter": {"version": "1.3.3", "dev": true, "license": "MIT", "dependencies": {"@japa/base-reporter": "^1.1.1", "@japa/errors-printer": "^2.1.0", "@poppinss/cliui": "^3.0.5", "ms": "^2.1.3"}}, "node_modules/@jest/schemas": {"version": "29.6.3", "dev": true, "license": "MIT", "dependencies": {"@sinclair/typebox": "^0.27.8"}, "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}}, "node_modules/@jest/types": {"version": "25.5.0", "dev": true, "license": "MIT", "dependencies": {"@types/istanbul-lib-coverage": "^2.0.0", "@types/istanbul-reports": "^1.1.1", "@types/yargs": "^15.0.0", "chalk": "^3.0.0"}, "engines": {"node": ">= 8.3"}}, "node_modules/@jsdevtools/ono": {"version": "7.1.3", "license": "MIT"}, "node_modules/@mrmlnc/readdir-enhanced": {"version": "2.2.1", "dev": true, "license": "MIT", "dependencies": {"call-me-maybe": "^1.0.1", "glob-to-regexp": "^0.3.0"}, "engines": {"node": ">=4"}}, "node_modules/@nodelib/fs.scandir": {"version": "2.1.5", "dev": true, "license": "MIT", "dependencies": {"@nodelib/fs.stat": "2.0.5", "run-parallel": "^1.1.9"}, "engines": {"node": ">= 8"}}, "node_modules/@nodelib/fs.stat": {"version": "2.0.5", "dev": true, "license": "MIT", "engines": {"node": ">= 8"}}, "node_modules/@nodelib/fs.walk": {"version": "1.2.8", "dev": true, "license": "MIT", "dependencies": {"@nodelib/fs.scandir": "2.1.5", "fastq": "^1.6.0"}, "engines": {"node": ">= 8"}}, "node_modules/@onesignal/node-onesignal": {"version": "2.0.1-beta2", "resolved": "https://registry.npmjs.org/@onesignal/node-onesignal/-/node-onesignal-2.0.1-beta2.tgz", "integrity": "sha512-UP1QNhXN3qtqrs2aeMP4bctCEggC0VMHgUjnwu5RGKOArUUN/lWFLJuMqcZzW3PldOvH6h9Q8f9HRouXc0QPWg==", "dependencies": {"@types/node": "^20.4.5", "@types/node-fetch": "^2.5.7", "btoa": "^1.2.1", "es6-promise": "^4.2.4", "form-data": "^2.5.0", "node-fetch": "^2.6.0", "url-parse": "^1.4.3"}}, "node_modules/@onesignal/node-onesignal/node_modules/form-data": {"version": "2.5.1", "resolved": "https://registry.npmjs.org/form-data/-/form-data-2.5.1.tgz", "integrity": "sha512-m21N3WOmEEURgk6B9GLOE4RuWOFf28Lhh9qGYeNlGq4VDXUlJy2th2slBNU8Gp8EzloYZOibZJ7t5ecIrFSjVA==", "dependencies": {"asynckit": "^0.4.0", "combined-stream": "^1.0.6", "mime-types": "^2.1.12"}, "engines": {"node": ">= 0.12"}}, "node_modules/@phc/format": {"version": "1.0.0", "license": "MIT", "engines": {"node": ">=10"}}, "node_modules/@poppinss/chokidar-ts": {"version": "3.3.5", "dev": true, "license": "MIT", "dependencies": {"builtin-modules": "^3.2.0", "chokidar": "^3.5.3", "debug": "^4.3.4", "emittery": "^0.10.2", "fs-extra": "^10.0.1", "mem": "^8.1.1", "picomatch": "^2.3.1"}}, "node_modules/@poppinss/chokidar-ts/node_modules/emittery": {"version": "0.10.2", "dev": true, "license": "MIT", "engines": {"node": ">=12"}, "funding": {"url": "https://github.com/sindresorhus/emittery?sponsor=1"}}, "node_modules/@poppinss/cliui": {"version": "3.0.5", "license": "MIT", "dependencies": {"@poppinss/colors": "^3.0.3", "cli-boxes": "^3.0.0", "cli-table3": "^0.6.3", "color-support": "^1.1.3", "log-update": "^4.0.0", "pretty-hrtime": "^1.0.3", "string-width": "^4.2.2"}}, "node_modules/@poppinss/co-body": {"version": "1.2.0", "license": "MIT", "dependencies": {"@poppinss/utils": "^5.0.0", "inflation": "^2.0.0", "qs": "^6.11.0", "raw-body": "^2.5.1", "type-is": "^1.6.18"}}, "node_modules/@poppinss/colors": {"version": "3.0.3", "license": "MIT", "dependencies": {"color-support": "^1.1.3", "kleur": "^4.1.5"}}, "node_modules/@poppinss/file-generator": {"version": "1.0.2", "license": "MIT", "dependencies": {"bytes": "^3.1.2"}}, "node_modules/@poppinss/hooks": {"version": "5.0.3", "license": "MIT", "peerDependencies": {"@adonisjs/application": ">=4.0.0"}, "peerDependenciesMeta": {"@adonisjs/application": {"optional": true}}}, "node_modules/@poppinss/inspect": {"version": "1.0.1", "license": "MIT", "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/@poppinss/intl-formatter": {"version": "2.0.3", "license": "MIT", "dependencies": {"moize": "^6.1.0"}}, "node_modules/@poppinss/manager": {"version": "5.0.2", "license": "MIT"}, "node_modules/@poppinss/matchit": {"version": "3.1.2", "license": "MIT", "dependencies": {"@arr/every": "^1.0.0"}}, "node_modules/@poppinss/multiparty": {"version": "2.0.1", "license": "MIT", "dependencies": {"http-errors": "^2.0.0", "safe-buffer": "5.2.1", "uid-safe": "2.1.5"}}, "node_modules/@poppinss/prompts": {"version": "2.0.2", "license": "MIT", "dependencies": {"@poppinss/colors": "^3.0.2", "enquirer": "^2.3.6"}}, "node_modules/@poppinss/utils": {"version": "5.0.0", "license": "MIT", "dependencies": {"@poppinss/file-generator": "^1.0.2", "@types/bytes": "^3.1.1", "@types/he": "^1.1.2", "bytes": "^3.1.2", "change-case": "^4.1.2", "cuid": "^2.1.8", "flattie": "^1.1.0", "fs-readdir-recursive": "^1.1.0", "he": "^1.2.0", "kind-of": "^6.0.3", "lodash": "^4.17.21", "ms": "^2.1.3", "pluralize": "^8.0.0", "require-all": "^3.0.0", "resolve-from": "^5.0.0", "slugify": "^1.6.5", "truncatise": "0.0.8"}}, "node_modules/@sinclair/typebox": {"version": "0.27.8", "dev": true, "license": "MIT"}, "node_modules/@sindresorhus/is": {"version": "4.6.0", "license": "MIT", "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/sindresorhus/is?sponsor=1"}}, "node_modules/@smithy/abort-controller": {"version": "2.0.7", "license": "Apache-2.0", "dependencies": {"@smithy/types": "^2.3.1", "tslib": "^2.5.0"}, "engines": {"node": ">=14.0.0"}}, "node_modules/@smithy/chunked-blob-reader": {"version": "2.0.0", "license": "Apache-2.0", "dependencies": {"tslib": "^2.5.0"}}, "node_modules/@smithy/chunked-blob-reader-native": {"version": "2.0.0", "license": "Apache-2.0", "dependencies": {"@smithy/util-base64": "^2.0.0", "tslib": "^2.5.0"}}, "node_modules/@smithy/config-resolver": {"version": "2.0.8", "license": "Apache-2.0", "dependencies": {"@smithy/node-config-provider": "^2.0.10", "@smithy/types": "^2.3.1", "@smithy/util-config-provider": "^2.0.0", "@smithy/util-middleware": "^2.0.0", "tslib": "^2.5.0"}, "engines": {"node": ">=14.0.0"}}, "node_modules/@smithy/credential-provider-imds": {"version": "2.0.10", "license": "Apache-2.0", "dependencies": {"@smithy/node-config-provider": "^2.0.10", "@smithy/property-provider": "^2.0.8", "@smithy/types": "^2.3.1", "@smithy/url-parser": "^2.0.7", "tslib": "^2.5.0"}, "engines": {"node": ">=14.0.0"}}, "node_modules/@smithy/eventstream-codec": {"version": "2.0.7", "license": "Apache-2.0", "dependencies": {"@aws-crypto/crc32": "3.0.0", "@smithy/types": "^2.3.1", "@smithy/util-hex-encoding": "^2.0.0", "tslib": "^2.5.0"}}, "node_modules/@smithy/eventstream-serde-browser": {"version": "2.0.7", "license": "Apache-2.0", "dependencies": {"@smithy/eventstream-serde-universal": "^2.0.7", "@smithy/types": "^2.3.1", "tslib": "^2.5.0"}, "engines": {"node": ">=14.0.0"}}, "node_modules/@smithy/eventstream-serde-config-resolver": {"version": "2.0.7", "license": "Apache-2.0", "dependencies": {"@smithy/types": "^2.3.1", "tslib": "^2.5.0"}, "engines": {"node": ">=14.0.0"}}, "node_modules/@smithy/eventstream-serde-node": {"version": "2.0.7", "license": "Apache-2.0", "dependencies": {"@smithy/eventstream-serde-universal": "^2.0.7", "@smithy/types": "^2.3.1", "tslib": "^2.5.0"}, "engines": {"node": ">=14.0.0"}}, "node_modules/@smithy/eventstream-serde-universal": {"version": "2.0.7", "license": "Apache-2.0", "dependencies": {"@smithy/eventstream-codec": "^2.0.7", "@smithy/types": "^2.3.1", "tslib": "^2.5.0"}, "engines": {"node": ">=14.0.0"}}, "node_modules/@smithy/fetch-http-handler": {"version": "2.1.3", "license": "Apache-2.0", "dependencies": {"@smithy/protocol-http": "^3.0.3", "@smithy/querystring-builder": "^2.0.7", "@smithy/types": "^2.3.1", "@smithy/util-base64": "^2.0.0", "tslib": "^2.5.0"}}, "node_modules/@smithy/hash-blob-browser": {"version": "2.0.7", "license": "Apache-2.0", "dependencies": {"@smithy/chunked-blob-reader": "^2.0.0", "@smithy/chunked-blob-reader-native": "^2.0.0", "@smithy/types": "^2.3.1", "tslib": "^2.5.0"}}, "node_modules/@smithy/hash-node": {"version": "2.0.7", "license": "Apache-2.0", "dependencies": {"@smithy/types": "^2.3.1", "@smithy/util-buffer-from": "^2.0.0", "@smithy/util-utf8": "^2.0.0", "tslib": "^2.5.0"}, "engines": {"node": ">=14.0.0"}}, "node_modules/@smithy/hash-stream-node": {"version": "2.0.7", "license": "Apache-2.0", "dependencies": {"@smithy/types": "^2.3.1", "@smithy/util-utf8": "^2.0.0", "tslib": "^2.5.0"}, "engines": {"node": ">=14.0.0"}}, "node_modules/@smithy/invalid-dependency": {"version": "2.0.7", "license": "Apache-2.0", "dependencies": {"@smithy/types": "^2.3.1", "tslib": "^2.5.0"}}, "node_modules/@smithy/is-array-buffer": {"version": "2.0.0", "license": "Apache-2.0", "dependencies": {"tslib": "^2.5.0"}, "engines": {"node": ">=14.0.0"}}, "node_modules/@smithy/md5-js": {"version": "2.0.7", "license": "Apache-2.0", "dependencies": {"@smithy/types": "^2.3.1", "@smithy/util-utf8": "^2.0.0", "tslib": "^2.5.0"}}, "node_modules/@smithy/middleware-content-length": {"version": "2.0.9", "license": "Apache-2.0", "dependencies": {"@smithy/protocol-http": "^3.0.3", "@smithy/types": "^2.3.1", "tslib": "^2.5.0"}, "engines": {"node": ">=14.0.0"}}, "node_modules/@smithy/middleware-endpoint": {"version": "2.0.7", "license": "Apache-2.0", "dependencies": {"@smithy/middleware-serde": "^2.0.7", "@smithy/types": "^2.3.1", "@smithy/url-parser": "^2.0.7", "@smithy/util-middleware": "^2.0.0", "tslib": "^2.5.0"}, "engines": {"node": ">=14.0.0"}}, "node_modules/@smithy/middleware-retry": {"version": "2.0.10", "license": "Apache-2.0", "dependencies": {"@smithy/node-config-provider": "^2.0.10", "@smithy/protocol-http": "^3.0.3", "@smithy/service-error-classification": "^2.0.0", "@smithy/types": "^2.3.1", "@smithy/util-middleware": "^2.0.0", "@smithy/util-retry": "^2.0.0", "tslib": "^2.5.0", "uuid": "^8.3.2"}, "engines": {"node": ">=14.0.0"}}, "node_modules/@smithy/middleware-retry/node_modules/uuid": {"version": "8.3.2", "license": "MIT", "bin": {"uuid": "dist/bin/uuid"}}, "node_modules/@smithy/middleware-serde": {"version": "2.0.7", "license": "Apache-2.0", "dependencies": {"@smithy/types": "^2.3.1", "tslib": "^2.5.0"}, "engines": {"node": ">=14.0.0"}}, "node_modules/@smithy/middleware-stack": {"version": "2.0.0", "license": "Apache-2.0", "dependencies": {"tslib": "^2.5.0"}, "engines": {"node": ">=14.0.0"}}, "node_modules/@smithy/node-config-provider": {"version": "2.0.10", "license": "Apache-2.0", "dependencies": {"@smithy/property-provider": "^2.0.8", "@smithy/shared-ini-file-loader": "^2.0.9", "@smithy/types": "^2.3.1", "tslib": "^2.5.0"}, "engines": {"node": ">=14.0.0"}}, "node_modules/@smithy/node-http-handler": {"version": "2.1.3", "license": "Apache-2.0", "dependencies": {"@smithy/abort-controller": "^2.0.7", "@smithy/protocol-http": "^3.0.3", "@smithy/querystring-builder": "^2.0.7", "@smithy/types": "^2.3.1", "tslib": "^2.5.0"}, "engines": {"node": ">=14.0.0"}}, "node_modules/@smithy/property-provider": {"version": "2.0.8", "license": "Apache-2.0", "dependencies": {"@smithy/types": "^2.3.1", "tslib": "^2.5.0"}, "engines": {"node": ">=14.0.0"}}, "node_modules/@smithy/protocol-http": {"version": "3.0.3", "license": "Apache-2.0", "dependencies": {"@smithy/types": "^2.3.1", "tslib": "^2.5.0"}, "engines": {"node": ">=14.0.0"}}, "node_modules/@smithy/querystring-builder": {"version": "2.0.7", "license": "Apache-2.0", "dependencies": {"@smithy/types": "^2.3.1", "@smithy/util-uri-escape": "^2.0.0", "tslib": "^2.5.0"}, "engines": {"node": ">=14.0.0"}}, "node_modules/@smithy/querystring-parser": {"version": "2.0.7", "license": "Apache-2.0", "dependencies": {"@smithy/types": "^2.3.1", "tslib": "^2.5.0"}, "engines": {"node": ">=14.0.0"}}, "node_modules/@smithy/service-error-classification": {"version": "2.0.0", "license": "Apache-2.0", "engines": {"node": ">=14.0.0"}}, "node_modules/@smithy/shared-ini-file-loader": {"version": "2.0.9", "license": "Apache-2.0", "dependencies": {"@smithy/types": "^2.3.1", "tslib": "^2.5.0"}, "engines": {"node": ">=14.0.0"}}, "node_modules/@smithy/signature-v4": {"version": "2.0.7", "license": "Apache-2.0", "dependencies": {"@smithy/eventstream-codec": "^2.0.7", "@smithy/is-array-buffer": "^2.0.0", "@smithy/types": "^2.3.1", "@smithy/util-hex-encoding": "^2.0.0", "@smithy/util-middleware": "^2.0.0", "@smithy/util-uri-escape": "^2.0.0", "@smithy/util-utf8": "^2.0.0", "tslib": "^2.5.0"}, "engines": {"node": ">=14.0.0"}}, "node_modules/@smithy/smithy-client": {"version": "2.1.4", "license": "Apache-2.0", "dependencies": {"@smithy/middleware-stack": "^2.0.0", "@smithy/types": "^2.3.1", "@smithy/util-stream": "^2.0.10", "tslib": "^2.5.0"}, "engines": {"node": ">=14.0.0"}}, "node_modules/@smithy/types": {"version": "2.3.1", "license": "Apache-2.0", "dependencies": {"tslib": "^2.5.0"}, "engines": {"node": ">=14.0.0"}}, "node_modules/@smithy/url-parser": {"version": "2.0.7", "license": "Apache-2.0", "dependencies": {"@smithy/querystring-parser": "^2.0.7", "@smithy/types": "^2.3.1", "tslib": "^2.5.0"}}, "node_modules/@smithy/util-base64": {"version": "2.0.0", "license": "Apache-2.0", "dependencies": {"@smithy/util-buffer-from": "^2.0.0", "tslib": "^2.5.0"}, "engines": {"node": ">=14.0.0"}}, "node_modules/@smithy/util-body-length-browser": {"version": "2.0.0", "license": "Apache-2.0", "dependencies": {"tslib": "^2.5.0"}}, "node_modules/@smithy/util-body-length-node": {"version": "2.1.0", "license": "Apache-2.0", "dependencies": {"tslib": "^2.5.0"}, "engines": {"node": ">=14.0.0"}}, "node_modules/@smithy/util-buffer-from": {"version": "2.0.0", "license": "Apache-2.0", "dependencies": {"@smithy/is-array-buffer": "^2.0.0", "tslib": "^2.5.0"}, "engines": {"node": ">=14.0.0"}}, "node_modules/@smithy/util-config-provider": {"version": "2.0.0", "license": "Apache-2.0", "dependencies": {"tslib": "^2.5.0"}, "engines": {"node": ">=14.0.0"}}, "node_modules/@smithy/util-defaults-mode-browser": {"version": "2.0.8", "license": "Apache-2.0", "dependencies": {"@smithy/property-provider": "^2.0.8", "@smithy/types": "^2.3.1", "bowser": "^2.11.0", "tslib": "^2.5.0"}, "engines": {"node": ">= 10.0.0"}}, "node_modules/@smithy/util-defaults-mode-node": {"version": "2.0.10", "license": "Apache-2.0", "dependencies": {"@smithy/config-resolver": "^2.0.8", "@smithy/credential-provider-imds": "^2.0.10", "@smithy/node-config-provider": "^2.0.10", "@smithy/property-provider": "^2.0.8", "@smithy/types": "^2.3.1", "tslib": "^2.5.0"}, "engines": {"node": ">= 10.0.0"}}, "node_modules/@smithy/util-hex-encoding": {"version": "2.0.0", "license": "Apache-2.0", "dependencies": {"tslib": "^2.5.0"}, "engines": {"node": ">=14.0.0"}}, "node_modules/@smithy/util-middleware": {"version": "2.0.0", "license": "Apache-2.0", "dependencies": {"tslib": "^2.5.0"}, "engines": {"node": ">=14.0.0"}}, "node_modules/@smithy/util-retry": {"version": "2.0.0", "license": "Apache-2.0", "dependencies": {"@smithy/service-error-classification": "^2.0.0", "tslib": "^2.5.0"}, "engines": {"node": ">= 14.0.0"}}, "node_modules/@smithy/util-stream": {"version": "2.0.10", "license": "Apache-2.0", "dependencies": {"@smithy/fetch-http-handler": "^2.1.3", "@smithy/node-http-handler": "^2.1.3", "@smithy/types": "^2.3.1", "@smithy/util-base64": "^2.0.0", "@smithy/util-buffer-from": "^2.0.0", "@smithy/util-hex-encoding": "^2.0.0", "@smithy/util-utf8": "^2.0.0", "tslib": "^2.5.0"}, "engines": {"node": ">=14.0.0"}}, "node_modules/@smithy/util-uri-escape": {"version": "2.0.0", "license": "Apache-2.0", "dependencies": {"tslib": "^2.5.0"}, "engines": {"node": ">=14.0.0"}}, "node_modules/@smithy/util-utf8": {"version": "2.0.0", "license": "Apache-2.0", "dependencies": {"@smithy/util-buffer-from": "^2.0.0", "tslib": "^2.5.0"}, "engines": {"node": ">=14.0.0"}}, "node_modules/@smithy/util-waiter": {"version": "2.0.7", "license": "Apache-2.0", "dependencies": {"@smithy/abort-controller": "^2.0.7", "@smithy/types": "^2.3.1", "tslib": "^2.5.0"}, "engines": {"node": ">=14.0.0"}}, "node_modules/@szmarczak/http-timer": {"version": "4.0.6", "license": "MIT", "dependencies": {"defer-to-connect": "^2.0.0"}, "engines": {"node": ">=10"}}, "node_modules/@tokenizer/token": {"version": "0.3.0", "license": "MIT"}, "node_modules/@types/bytes": {"version": "3.1.1", "license": "MIT"}, "node_modules/@types/cacheable-request": {"version": "6.0.3", "license": "MIT", "dependencies": {"@types/http-cache-semantics": "*", "@types/keyv": "^3.1.4", "@types/node": "*", "@types/responselike": "^1.0.0"}}, "node_modules/@types/chai": {"version": "4.3.6", "dev": true, "license": "MIT"}, "node_modules/@types/cookiejar": {"version": "2.1.2", "dev": true, "license": "MIT"}, "node_modules/@types/fs-extra": {"version": "9.0.13", "license": "MIT", "dependencies": {"@types/node": "*"}}, "node_modules/@types/glob": {"version": "7.2.0", "dev": true, "license": "MIT", "dependencies": {"@types/minimatch": "*", "@types/node": "*"}}, "node_modules/@types/he": {"version": "1.2.0", "license": "MIT"}, "node_modules/@types/http-cache-semantics": {"version": "4.0.1", "license": "MIT"}, "node_modules/@types/istanbul-lib-coverage": {"version": "2.0.4", "dev": true, "license": "MIT"}, "node_modules/@types/istanbul-lib-report": {"version": "3.0.0", "dev": true, "license": "MIT", "dependencies": {"@types/istanbul-lib-coverage": "*"}}, "node_modules/@types/istanbul-reports": {"version": "1.1.2", "dev": true, "license": "MIT", "dependencies": {"@types/istanbul-lib-coverage": "*", "@types/istanbul-lib-report": "*"}}, "node_modules/@types/json-schema": {"version": "7.0.12", "license": "MIT"}, "node_modules/@types/keyv": {"version": "3.1.4", "license": "MIT", "dependencies": {"@types/node": "*"}}, "node_modules/@types/luxon": {"version": "3.3.2", "license": "MIT"}, "node_modules/@types/minimatch": {"version": "5.1.2", "dev": true, "license": "MIT"}, "node_modules/@types/node": {"version": "20.6.0", "license": "MIT"}, "node_modules/@types/node-fetch": {"version": "2.6.5", "resolved": "https://registry.npmjs.org/@types/node-fetch/-/node-fetch-2.6.5.tgz", "integrity": "sha512-OZsUlr2nxvkqUFLSaY2ZbA+P1q22q+KrlxWOn/38RX+u5kTkYL2mTujEpzUhGkS+K/QCYp9oagfXG39XOzyySg==", "dependencies": {"@types/node": "*", "form-data": "^4.0.0"}}, "node_modules/@types/pino": {"version": "6.3.12", "license": "MIT", "dependencies": {"@types/node": "*", "@types/pino-pretty": "*", "@types/pino-std-serializers": "*", "sonic-boom": "^2.1.0"}}, "node_modules/@types/pino-pretty": {"version": "5.0.0", "deprecated": "This is a stub types definition. pino-pretty provides its own type definitions, so you do not need this installed.", "license": "MIT", "dependencies": {"pino-pretty": "*"}}, "node_modules/@types/pino-std-serializers": {"version": "4.0.0", "deprecated": "This is a stub types definition. pino-std-serializers provides its own type definitions, so you do not need this installed.", "license": "MIT", "dependencies": {"pino-std-serializers": "*"}}, "node_modules/@types/proxy-addr": {"version": "2.0.0", "dev": true, "license": "MIT", "dependencies": {"@types/node": "*"}}, "node_modules/@types/responselike": {"version": "1.0.0", "license": "MIT", "dependencies": {"@types/node": "*"}}, "node_modules/@types/semver": {"version": "7.5.2", "dev": true, "license": "MIT"}, "node_modules/@types/source-map-support": {"version": "0.5.7", "dev": true, "license": "MIT", "dependencies": {"source-map": "^0.6.0"}}, "node_modules/@types/superagent": {"version": "4.1.18", "dev": true, "license": "MIT", "dependencies": {"@types/cookiejar": "*", "@types/node": "*"}}, "node_modules/@types/uuid": {"version": "9.0.4", "dev": true, "license": "MIT"}, "node_modules/@types/validator": {"version": "13.11.1", "license": "MIT"}, "node_modules/@types/yargs": {"version": "15.0.15", "dev": true, "license": "MIT", "dependencies": {"@types/yargs-parser": "*"}}, "node_modules/@types/yargs-parser": {"version": "21.0.0", "dev": true, "license": "MIT"}, "node_modules/@typescript-eslint/eslint-plugin": {"version": "5.62.0", "dev": true, "license": "MIT", "dependencies": {"@eslint-community/regexpp": "^4.4.0", "@typescript-eslint/scope-manager": "5.62.0", "@typescript-eslint/type-utils": "5.62.0", "@typescript-eslint/utils": "5.62.0", "debug": "^4.3.4", "graphemer": "^1.4.0", "ignore": "^5.2.0", "natural-compare-lite": "^1.4.0", "semver": "^7.3.7", "tsutils": "^3.21.0"}, "engines": {"node": "^12.22.0 || ^14.17.0 || >=16.0.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/typescript-eslint"}, "peerDependencies": {"@typescript-eslint/parser": "^5.0.0", "eslint": "^6.0.0 || ^7.0.0 || ^8.0.0"}, "peerDependenciesMeta": {"typescript": {"optional": true}}}, "node_modules/@typescript-eslint/parser": {"version": "5.62.0", "dev": true, "license": "BSD-2-<PERSON><PERSON>", "dependencies": {"@typescript-eslint/scope-manager": "5.62.0", "@typescript-eslint/types": "5.62.0", "@typescript-eslint/typescript-estree": "5.62.0", "debug": "^4.3.4"}, "engines": {"node": "^12.22.0 || ^14.17.0 || >=16.0.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/typescript-eslint"}, "peerDependencies": {"eslint": "^6.0.0 || ^7.0.0 || ^8.0.0"}, "peerDependenciesMeta": {"typescript": {"optional": true}}}, "node_modules/@typescript-eslint/scope-manager": {"version": "5.62.0", "dev": true, "license": "MIT", "dependencies": {"@typescript-eslint/types": "5.62.0", "@typescript-eslint/visitor-keys": "5.62.0"}, "engines": {"node": "^12.22.0 || ^14.17.0 || >=16.0.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/typescript-eslint"}}, "node_modules/@typescript-eslint/type-utils": {"version": "5.62.0", "dev": true, "license": "MIT", "dependencies": {"@typescript-eslint/typescript-estree": "5.62.0", "@typescript-eslint/utils": "5.62.0", "debug": "^4.3.4", "tsutils": "^3.21.0"}, "engines": {"node": "^12.22.0 || ^14.17.0 || >=16.0.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/typescript-eslint"}, "peerDependencies": {"eslint": "*"}, "peerDependenciesMeta": {"typescript": {"optional": true}}}, "node_modules/@typescript-eslint/types": {"version": "5.62.0", "dev": true, "license": "MIT", "engines": {"node": "^12.22.0 || ^14.17.0 || >=16.0.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/typescript-eslint"}}, "node_modules/@typescript-eslint/typescript-estree": {"version": "5.62.0", "dev": true, "license": "BSD-2-<PERSON><PERSON>", "dependencies": {"@typescript-eslint/types": "5.62.0", "@typescript-eslint/visitor-keys": "5.62.0", "debug": "^4.3.4", "globby": "^11.1.0", "is-glob": "^4.0.3", "semver": "^7.3.7", "tsutils": "^3.21.0"}, "engines": {"node": "^12.22.0 || ^14.17.0 || >=16.0.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/typescript-eslint"}, "peerDependenciesMeta": {"typescript": {"optional": true}}}, "node_modules/@typescript-eslint/typescript-estree/node_modules/array-union": {"version": "2.1.0", "dev": true, "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/@typescript-eslint/typescript-estree/node_modules/dir-glob": {"version": "3.0.1", "dev": true, "license": "MIT", "dependencies": {"path-type": "^4.0.0"}, "engines": {"node": ">=8"}}, "node_modules/@typescript-eslint/typescript-estree/node_modules/globby": {"version": "11.1.0", "dev": true, "license": "MIT", "dependencies": {"array-union": "^2.1.0", "dir-glob": "^3.0.1", "fast-glob": "^3.2.9", "ignore": "^5.2.0", "merge2": "^1.4.1", "slash": "^3.0.0"}, "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/@typescript-eslint/typescript-estree/node_modules/path-type": {"version": "4.0.0", "dev": true, "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/@typescript-eslint/utils": {"version": "5.62.0", "dev": true, "license": "MIT", "dependencies": {"@eslint-community/eslint-utils": "^4.2.0", "@types/json-schema": "^7.0.9", "@types/semver": "^7.3.12", "@typescript-eslint/scope-manager": "5.62.0", "@typescript-eslint/types": "5.62.0", "@typescript-eslint/typescript-estree": "5.62.0", "eslint-scope": "^5.1.1", "semver": "^7.3.7"}, "engines": {"node": "^12.22.0 || ^14.17.0 || >=16.0.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/typescript-eslint"}, "peerDependencies": {"eslint": "^6.0.0 || ^7.0.0 || ^8.0.0"}}, "node_modules/@typescript-eslint/utils/node_modules/eslint-scope": {"version": "5.1.1", "dev": true, "license": "BSD-2-<PERSON><PERSON>", "dependencies": {"esrecurse": "^4.3.0", "estraverse": "^4.1.1"}, "engines": {"node": ">=8.0.0"}}, "node_modules/@typescript-eslint/utils/node_modules/estraverse": {"version": "4.3.0", "dev": true, "license": "BSD-2-<PERSON><PERSON>", "engines": {"node": ">=4.0"}}, "node_modules/@typescript-eslint/visitor-keys": {"version": "5.62.0", "dev": true, "license": "MIT", "dependencies": {"@typescript-eslint/types": "5.62.0", "eslint-visitor-keys": "^3.3.0"}, "engines": {"node": "^12.22.0 || ^14.17.0 || >=16.0.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/typescript-eslint"}}, "node_modules/@zenvia/sdk": {"version": "2.4.4", "resolved": "https://registry.npmjs.org/@zenvia/sdk/-/sdk-2.4.4.tgz", "integrity": "sha512-RQQ1j3biBxcf/dQL1jYTYNIuROgH/qA28772PfesBJN4CyNwQE4zi9by0c5zqEgNRSZ8rTJCdvY1Ten0+EqQIA==", "dependencies": {"express": "^4.17.1", "request": "^2.88.2", "request-promise": "^4.2.6"}}, "node_modules/abort-controller": {"version": "3.0.0", "license": "MIT", "dependencies": {"event-target-shim": "^5.0.0"}, "engines": {"node": ">=6.5"}}, "node_modules/abstract-logging": {"version": "2.0.1", "license": "MIT"}, "node_modules/accepts": {"version": "1.3.8", "license": "MIT", "dependencies": {"mime-types": "~2.1.34", "negotiator": "0.6.3"}, "engines": {"node": ">= 0.6"}}, "node_modules/acorn": {"version": "8.10.0", "license": "MIT", "bin": {"acorn": "bin/acorn"}, "engines": {"node": ">=0.4.0"}}, "node_modules/acorn-class-fields": {"version": "1.0.0", "license": "MIT", "dependencies": {"acorn-private-class-elements": "^1.0.0"}, "engines": {"node": ">=4.8.2"}, "peerDependencies": {"acorn": "^6 || ^7 || ^8"}}, "node_modules/acorn-jsx": {"version": "5.3.2", "dev": true, "license": "MIT", "peerDependencies": {"acorn": "^6.0.0 || ^7.0.0 || ^8.0.0"}}, "node_modules/acorn-private-class-elements": {"version": "1.0.0", "license": "MIT", "engines": {"node": ">=4.8.2"}, "peerDependencies": {"acorn": "^6.1.0 || ^7 || ^8"}}, "node_modules/acorn-private-methods": {"version": "1.0.0", "license": "MIT", "dependencies": {"acorn-private-class-elements": "^1.0.0"}, "engines": {"node": ">=4.8.2"}, "peerDependencies": {"acorn": "^6 || ^7 || ^8"}}, "node_modules/acorn-static-class-features": {"version": "1.0.0", "license": "MIT", "dependencies": {"acorn-private-class-elements": "^1.0.0"}, "engines": {"node": ">=4.8.2"}, "peerDependencies": {"acorn": "^6.1.0 || ^7 || ^8"}}, "node_modules/acorn-walk": {"version": "8.2.0", "license": "MIT", "engines": {"node": ">=0.4.0"}}, "node_modules/adonis-preset-ts": {"version": "2.1.0", "dev": true, "license": "MIT"}, "node_modules/adonis5-swagger": {"version": "1.4.1", "resolved": "https://registry.npmjs.org/adonis5-swagger/-/adonis5-swagger-1.4.1.tgz", "integrity": "sha512-XO5tD1R9sJSxdaRWwQ2FhfsQwHgUgx2RU6JPJi4vPn09aXAMH9RvwsRWYPIlhFQyUVMP0toFyxfFyFT2eRjyMA==", "dependencies": {"mime": "^3.0.0", "swagger-jsdoc": "^6.1.0", "swagger-ui-dist": "^4.1.3"}}, "node_modules/adonis5-swagger/node_modules/mime": {"version": "3.0.0", "resolved": "https://registry.npmjs.org/mime/-/mime-3.0.0.tgz", "integrity": "sha512-jSCU7/VB1loIWBZe14aEYHU/+1UMEHoaO7qxCOVJOw9GgH72VAWppxNcjU+x9a2k3GSIBXNKxXQFqRvvZ7vr3A==", "bin": {"mime": "cli.js"}, "engines": {"node": ">=10.0.0"}}, "node_modules/agent-base": {"version": "6.0.2", "resolved": "https://registry.npmjs.org/agent-base/-/agent-base-6.0.2.tgz", "integrity": "sha512-RZNwNclF7+MS/8bDg70amg32dyeZGZxiDuQmZxKLAlQjr3jGyLx+4Kkk58UO7D2QdgFIQCovuSuZESne6RG6XQ==", "dependencies": {"debug": "4"}, "engines": {"node": ">= 6.0.0"}}, "node_modules/aggregate-error": {"version": "3.1.0", "dev": true, "license": "MIT", "dependencies": {"clean-stack": "^2.0.0", "indent-string": "^4.0.0"}, "engines": {"node": ">=8"}}, "node_modules/ajv": {"version": "6.12.6", "license": "MIT", "dependencies": {"fast-deep-equal": "^3.1.1", "fast-json-stable-stringify": "^2.0.0", "json-schema-traverse": "^0.4.1", "uri-js": "^4.2.2"}, "funding": {"type": "github", "url": "https://github.com/sponsors/epoberezkin"}}, "node_modules/ansi-colors": {"version": "4.1.3", "license": "MIT", "engines": {"node": ">=6"}}, "node_modules/ansi-escapes": {"version": "4.3.2", "license": "MIT", "dependencies": {"type-fest": "^0.21.3"}, "engines": {"node": ">=8"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/ansi-escapes/node_modules/type-fest": {"version": "0.21.3", "license": "(MIT OR CC0-1.0)", "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/ansi-regex": {"version": "5.0.1", "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/ansi-styles": {"version": "4.3.0", "license": "MIT", "dependencies": {"color-convert": "^2.0.1"}, "engines": {"node": ">=8"}, "funding": {"url": "https://github.com/chalk/ansi-styles?sponsor=1"}}, "node_modules/ansicolors": {"version": "0.3.2", "resolved": "https://registry.npmjs.org/ansicolors/-/ansicolors-0.3.2.tgz", "integrity": "sha512-QXu7BPrP29VllRxH8GwB7x5iX5qWKAAMLqKQGWTeLWVlNHNOpVMJ91dsxQAIWXpjuW5wqvxu3Jd/nRjrJ+0pqg==", "dev": true}, "node_modules/anymatch": {"version": "3.1.3", "dev": true, "license": "ISC", "dependencies": {"normalize-path": "^3.0.0", "picomatch": "^2.0.4"}, "engines": {"node": ">= 8"}}, "node_modules/api-contract-validator": {"version": "2.2.8", "dev": true, "license": "Apache-2.0", "dependencies": {"api-schema-builder": "^2.0.10", "chalk": "^3.0.0", "columnify": "^1.5.4", "jest-diff": "^25.5.0", "jest-matcher-utils": "^25.5.0", "lodash.flatten": "^4.4.0", "lodash.get": "^4.4.2", "lodash.set": "^4.3.2", "uri-js": "^4.4.1"}, "engines": {"node": ">=8"}}, "node_modules/api-contract-validator/node_modules/diff-sequences": {"version": "25.2.6", "dev": true, "license": "MIT", "engines": {"node": ">= 8.3"}}, "node_modules/api-contract-validator/node_modules/jest-diff": {"version": "25.5.0", "dev": true, "license": "MIT", "dependencies": {"chalk": "^3.0.0", "diff-sequences": "^25.2.6", "jest-get-type": "^25.2.6", "pretty-format": "^25.5.0"}, "engines": {"node": ">= 8.3"}}, "node_modules/api-contract-validator/node_modules/jest-get-type": {"version": "25.2.6", "dev": true, "license": "MIT", "engines": {"node": ">= 8.3"}}, "node_modules/api-contract-validator/node_modules/pretty-format": {"version": "25.5.0", "dev": true, "license": "MIT", "dependencies": {"@jest/types": "^25.5.0", "ansi-regex": "^5.0.0", "ansi-styles": "^4.0.0", "react-is": "^16.12.0"}, "engines": {"node": ">= 8.3"}}, "node_modules/api-contract-validator/node_modules/react-is": {"version": "16.13.1", "dev": true, "license": "MIT"}, "node_modules/api-schema-builder": {"version": "2.0.11", "dev": true, "license": "Apache-2.0", "dependencies": {"ajv": "^6.12.6", "clone-deep": "^4.0.1", "decimal.js": "^10.3.1", "js-yaml": "^3.14.1", "json-schema-deref-sync": "^0.14.0", "lodash.get": "^4.4.2", "openapi-schema-validator": "^3.0.3", "swagger-parser": "^10.0.3"}, "engines": {"node": ">=8"}}, "node_modules/argparse": {"version": "1.0.10", "dev": true, "license": "MIT", "dependencies": {"sprintf-js": "~1.0.2"}}, "node_modules/arr-diff": {"version": "4.0.0", "dev": true, "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/arr-flatten": {"version": "1.1.0", "dev": true, "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/arr-union": {"version": "3.1.0", "dev": true, "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/array-flatten": {"version": "1.1.1", "resolved": "https://registry.npmjs.org/array-flatten/-/array-flatten-1.1.1.tgz", "integrity": "sha512-PCVAQswWemu6UdxsDFFX/+gVeYqKAod3D3UVm91jHwynguOwAvYPhx8nNlM++NqRcK6CxxpUafjmhIdKiHibqg=="}, "node_modules/array-union": {"version": "1.0.2", "dev": true, "license": "MIT", "dependencies": {"array-uniq": "^1.0.1"}, "engines": {"node": ">=0.10.0"}}, "node_modules/array-uniq": {"version": "1.0.3", "dev": true, "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/array-unique": {"version": "0.3.2", "dev": true, "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/arrify": {"version": "2.0.1", "dev": true, "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/as-table": {"version": "1.0.55", "dev": true, "license": "MIT", "dependencies": {"printable-characters": "^1.0.42"}}, "node_modules/asap": {"version": "2.0.6", "dev": true, "license": "MIT"}, "node_modules/asn1": {"version": "0.2.6", "resolved": "https://registry.npmjs.org/asn1/-/asn1-0.2.6.tgz", "integrity": "sha512-ix/FxPn0MDjeyJ7i/yoHGFt/EX6LyNbxSEhPPXODPL+KB0VPk86UYfL0lMdy+KCnv+fmvIzySwaK5COwqVbWTQ==", "dependencies": {"safer-buffer": "~2.1.0"}}, "node_modules/assert-plus": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/assert-plus/-/assert-plus-1.0.0.tgz", "integrity": "sha512-NfJ4UzBCcQGLDlQq7nHxH+tv3kyZ0hHQqF5BO6J7tNJeP5do1llPr8dZ8zHonfhAu0PHAdMkSo+8o0wxg9lZWw==", "engines": {"node": ">=0.8"}}, "node_modules/assertion-error": {"version": "1.1.0", "dev": true, "license": "MIT", "engines": {"node": "*"}}, "node_modules/assign-symbols": {"version": "1.0.0", "dev": true, "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/astral-regex": {"version": "2.0.0", "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/astring": {"version": "1.8.6", "license": "MIT", "bin": {"astring": "bin/astring"}}, "node_modules/async-retry": {"version": "1.3.3", "dev": true, "license": "MIT", "dependencies": {"retry": "0.13.1"}}, "node_modules/asynckit": {"version": "0.4.0", "license": "MIT"}, "node_modules/atob": {"version": "2.1.2", "dev": true, "license": "(MIT OR Apache-2.0)", "bin": {"atob": "bin/atob.js"}, "engines": {"node": ">= 4.5.0"}}, "node_modules/atomic-sleep": {"version": "1.0.0", "license": "MIT", "engines": {"node": ">=8.0.0"}}, "node_modules/aws-sign2": {"version": "0.7.0", "resolved": "https://registry.npmjs.org/aws-sign2/-/aws-sign2-0.7.0.tgz", "integrity": "sha512-08kcGqnYf/YmjoRhfxyu+CLxBjUtHLXLXX/vUfx9l2LYzG3c1m61nrpyFUZI6zeS+Li/wWMMidD9KgrqtGq3mA==", "engines": {"node": "*"}}, "node_modules/aws4": {"version": "1.12.0", "resolved": "https://registry.npmjs.org/aws4/-/aws4-1.12.0.tgz", "integrity": "sha512-NmWvPnx0F1SfrQbYwOi7OeaNGokp9XhzNioJ/CSBs8Qa4vxug81mhJEAVZwxXuBmYB5KDRfMq/F3RR0BIU7sWg=="}, "node_modules/axios": {"version": "0.26.1", "resolved": "https://registry.npmjs.org/axios/-/axios-0.26.1.tgz", "integrity": "sha512-fPwcX4EvnSHuInCMItEhAGnaSEXRBjtzh9fOtsE6E1G6p7vl7edEeZe11QHf18+6+9gR5PbKV/sGKNaD8YaMeA==", "dependencies": {"follow-redirects": "^1.14.8"}}, "node_modules/babel-code-frame": {"version": "6.26.0", "dev": true, "license": "MIT", "dependencies": {"chalk": "^1.1.3", "esutils": "^2.0.2", "js-tokens": "^3.0.2"}}, "node_modules/babel-code-frame/node_modules/ansi-regex": {"version": "2.1.1", "dev": true, "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/babel-code-frame/node_modules/ansi-styles": {"version": "2.2.1", "dev": true, "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/babel-code-frame/node_modules/chalk": {"version": "1.1.3", "dev": true, "license": "MIT", "dependencies": {"ansi-styles": "^2.2.1", "escape-string-regexp": "^1.0.2", "has-ansi": "^2.0.0", "strip-ansi": "^3.0.0", "supports-color": "^2.0.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/babel-code-frame/node_modules/escape-string-regexp": {"version": "1.0.5", "dev": true, "license": "MIT", "engines": {"node": ">=0.8.0"}}, "node_modules/babel-code-frame/node_modules/strip-ansi": {"version": "3.0.1", "dev": true, "license": "MIT", "dependencies": {"ansi-regex": "^2.0.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/babel-code-frame/node_modules/supports-color": {"version": "2.0.0", "dev": true, "license": "MIT", "engines": {"node": ">=0.8.0"}}, "node_modules/balanced-match": {"version": "1.0.2", "license": "MIT"}, "node_modules/base": {"version": "0.11.2", "dev": true, "license": "MIT", "dependencies": {"cache-base": "^1.0.1", "class-utils": "^0.3.5", "component-emitter": "^1.2.1", "define-property": "^1.0.0", "isobject": "^3.0.1", "mixin-deep": "^1.2.0", "pascalcase": "^0.1.1"}, "engines": {"node": ">=0.10.0"}}, "node_modules/base/node_modules/define-property": {"version": "1.0.0", "dev": true, "license": "MIT", "dependencies": {"is-descriptor": "^1.0.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/base64-js": {"version": "1.5.1", "funding": [{"type": "github", "url": "https://github.com/sponsors/feross"}, {"type": "patreon", "url": "https://www.patreon.com/feross"}, {"type": "consulting", "url": "https://feross.org/support"}], "license": "MIT"}, "node_modules/bcrypt-pbkdf": {"version": "1.0.2", "resolved": "https://registry.npmjs.org/bcrypt-pbkdf/-/bcrypt-pbkdf-1.0.2.tgz", "integrity": "sha512-qeFIXtP4MSoi6NLqO12WfqARWWuCKi2Rn/9hJLEmtB5yTNr9DqFWkJRCf2qShWzPeAMRnOgCrq0sg/KLv5ES9w==", "dependencies": {"tweetnacl": "^0.14.3"}}, "node_modules/binary-extensions": {"version": "2.2.0", "dev": true, "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/bluebird": {"version": "3.7.2", "resolved": "https://registry.npmjs.org/bluebird/-/bluebird-3.7.2.tgz", "integrity": "sha512-XpNj6GDQzdfW+r2Wnn7xiSAd7TM3jzkxGXBGTtWKuSXv1xUV+azxAm8jdWZN06QTQk+2N2XB9jRDkvbmQmcRtg=="}, "node_modules/body-parser": {"version": "1.20.1", "resolved": "https://registry.npmjs.org/body-parser/-/body-parser-1.20.1.tgz", "integrity": "sha512-jWi7abTbYwajOytWCQc37VulmWiRae5RyTpaCyDcS5/lMdtwSz5lOpDE67srw/HYe35f1z3fDQw+3txg7gNtWw==", "dependencies": {"bytes": "3.1.2", "content-type": "~1.0.4", "debug": "2.6.9", "depd": "2.0.0", "destroy": "1.2.0", "http-errors": "2.0.0", "iconv-lite": "0.4.24", "on-finished": "2.4.1", "qs": "6.11.0", "raw-body": "2.5.1", "type-is": "~1.6.18", "unpipe": "1.0.0"}, "engines": {"node": ">= 0.8", "npm": "1.2.8000 || >= 1.4.16"}}, "node_modules/body-parser/node_modules/debug": {"version": "2.6.9", "resolved": "https://registry.npmjs.org/debug/-/debug-2.6.9.tgz", "integrity": "sha512-bC7ElrdJaJnPbAP+1EotYvqZsb3ecl5wi6Bfi6BJTUcNowp6cvspg0jXznRTKDjm/E7AdgFBVeAPVMNcKGsHMA==", "dependencies": {"ms": "2.0.0"}}, "node_modules/body-parser/node_modules/iconv-lite": {"version": "0.4.24", "resolved": "https://registry.npmjs.org/iconv-lite/-/iconv-lite-0.4.24.tgz", "integrity": "sha512-v3MXnZAcvnywkTUEZomIActle7RXXeedOR31wwl7VlyoXO4Qi9arvSenNQWne1TcRwhCL1HwLI21bEqdpj8/rA==", "dependencies": {"safer-buffer": ">= 2.1.2 < 3"}, "engines": {"node": ">=0.10.0"}}, "node_modules/body-parser/node_modules/ms": {"version": "2.0.0", "resolved": "https://registry.npmjs.org/ms/-/ms-2.0.0.tgz", "integrity": "sha512-Tpp60P6IUJDTuOq/5Z8cdskzJujfwqfOTkrwIwj7IRISpnkJnT6SyJ4PCPnGMoFjC9ddhal5KVIYtAt97ix05A=="}, "node_modules/body-parser/node_modules/qs": {"version": "6.11.0", "resolved": "https://registry.npmjs.org/qs/-/qs-6.11.0.tgz", "integrity": "sha512-MvjoMCJwEarSbUYk5O+nmoSzSutSsTwF85zcHPQ9OrlFoZOYIjaqBAJIqIXjptyD5vThxGq52Xu/MaJzRkIk4Q==", "dependencies": {"side-channel": "^1.0.4"}, "engines": {"node": ">=0.6"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/body-parser/node_modules/raw-body": {"version": "2.5.1", "resolved": "https://registry.npmjs.org/raw-body/-/raw-body-2.5.1.tgz", "integrity": "sha512-qqJBtEyVgS0ZmPGdCFPWJ3FreoqvG4MVQln/kCgF7Olq95IbOp0/BWyMwbdtn4VTvkM8Y7khCQ2Xgk/tcrCXig==", "dependencies": {"bytes": "3.1.2", "http-errors": "2.0.0", "iconv-lite": "0.4.24", "unpipe": "1.0.0"}, "engines": {"node": ">= 0.8"}}, "node_modules/bowser": {"version": "2.11.0", "license": "MIT"}, "node_modules/brace-expansion": {"version": "1.1.11", "license": "MIT", "dependencies": {"balanced-match": "^1.0.0", "concat-map": "0.0.1"}}, "node_modules/braces": {"version": "3.0.2", "dev": true, "license": "MIT", "dependencies": {"fill-range": "^7.0.1"}, "engines": {"node": ">=8"}}, "node_modules/btoa": {"version": "1.2.1", "resolved": "https://registry.npmjs.org/btoa/-/btoa-1.2.1.tgz", "integrity": "sha512-SB4/MIGlsiVkMcHmT+pSmIPoNDoHg+7cMzmt3Uxt628MTz2487DKSqK/fuhFBrkuqrYv5UCEnACpF4dTFNKc/g==", "bin": {"btoa": "bin/btoa.js"}, "engines": {"node": ">= 0.4.0"}}, "node_modules/buffer": {"version": "5.6.0", "license": "MIT", "dependencies": {"base64-js": "^1.0.2", "ieee754": "^1.1.4"}}, "node_modules/buffer-alloc": {"version": "1.2.0", "license": "MIT", "dependencies": {"buffer-alloc-unsafe": "^1.1.0", "buffer-fill": "^1.0.0"}}, "node_modules/buffer-alloc-unsafe": {"version": "1.1.0", "license": "MIT"}, "node_modules/buffer-equal-constant-time": {"version": "1.0.1", "resolved": "https://registry.npmjs.org/buffer-equal-constant-time/-/buffer-equal-constant-time-1.0.1.tgz", "integrity": "sha512-zRpUiDwd/xk6ADqPMATG8vc9VPrkck7T07OIx0gnjmJAnHnTVXNQG3vfvWNuiZIkwu9KrKdA1iJKfsfTVxE6NA=="}, "node_modules/buffer-fill": {"version": "1.0.0", "license": "MIT"}, "node_modules/buffer-from": {"version": "1.1.2", "license": "MIT"}, "node_modules/builtin-modules": {"version": "3.3.0", "dev": true, "license": "MIT", "engines": {"node": ">=6"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/builtins": {"version": "1.0.3", "dev": true, "license": "MIT"}, "node_modules/bytes": {"version": "3.1.2", "license": "MIT", "engines": {"node": ">= 0.8"}}, "node_modules/cache-base": {"version": "1.0.1", "dev": true, "license": "MIT", "dependencies": {"collection-visit": "^1.0.0", "component-emitter": "^1.2.1", "get-value": "^2.0.6", "has-value": "^1.0.0", "isobject": "^3.0.1", "set-value": "^2.0.0", "to-object-path": "^0.3.0", "union-value": "^1.0.0", "unset-value": "^1.0.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/cacheable-lookup": {"version": "5.0.4", "license": "MIT", "engines": {"node": ">=10.6.0"}}, "node_modules/cacheable-request": {"version": "7.0.4", "license": "MIT", "dependencies": {"clone-response": "^1.0.2", "get-stream": "^5.1.0", "http-cache-semantics": "^4.0.0", "keyv": "^4.0.0", "lowercase-keys": "^2.0.0", "normalize-url": "^6.0.1", "responselike": "^2.0.0"}, "engines": {"node": ">=8"}}, "node_modules/cacheable-request/node_modules/get-stream": {"version": "5.2.0", "license": "MIT", "dependencies": {"pump": "^3.0.0"}, "engines": {"node": ">=8"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/call-bind": {"version": "1.0.2", "license": "MIT", "dependencies": {"function-bind": "^1.1.1", "get-intrinsic": "^1.0.2"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/call-me-maybe": {"version": "1.0.2", "license": "MIT"}, "node_modules/callsites": {"version": "3.1.0", "dev": true, "license": "MIT", "engines": {"node": ">=6"}}, "node_modules/camel-case": {"version": "4.1.2", "license": "MIT", "dependencies": {"pascal-case": "^3.1.2", "tslib": "^2.0.3"}}, "node_modules/capital-case": {"version": "1.0.4", "license": "MIT", "dependencies": {"no-case": "^3.0.4", "tslib": "^2.0.3", "upper-case-first": "^2.0.2"}}, "node_modules/cardinal": {"version": "2.1.1", "resolved": "https://registry.npmjs.org/cardinal/-/cardinal-2.1.1.tgz", "integrity": "sha512-JSr5eOgoEymtYHBjNWyjrMqet9Am2miJhlfKNdqLp6zoeAh0KN5dRAcxlecj5mAJrmQomgiOBj35xHLrFjqBpw==", "dev": true, "dependencies": {"ansicolors": "~0.3.2", "redeyed": "~2.1.0"}, "bin": {"cdl": "bin/cdl.js"}}, "node_modules/caseless": {"version": "0.12.0", "resolved": "https://registry.npmjs.org/caseless/-/caseless-0.12.0.tgz", "integrity": "sha512-4tYFyifaFfGacoiObjJegolkwSU4xQNGbVgUiNYVUxbQ2x2lUsFvY4hVgVzGiIe6WLOPqycWXA40l+PWsxthUw=="}, "node_modules/chai": {"version": "4.3.8", "dev": true, "license": "MIT", "dependencies": {"assertion-error": "^1.1.0", "check-error": "^1.0.2", "deep-eql": "^4.1.2", "get-func-name": "^2.0.0", "loupe": "^2.3.1", "pathval": "^1.1.1", "type-detect": "^4.0.5"}, "engines": {"node": ">=4"}}, "node_modules/chalk": {"version": "3.0.0", "dev": true, "license": "MIT", "dependencies": {"ansi-styles": "^4.1.0", "supports-color": "^7.1.0"}, "engines": {"node": ">=8"}}, "node_modules/change-case": {"version": "4.1.2", "license": "MIT", "dependencies": {"camel-case": "^4.1.2", "capital-case": "^1.0.4", "constant-case": "^3.0.4", "dot-case": "^3.0.4", "header-case": "^2.0.4", "no-case": "^3.0.4", "param-case": "^3.0.4", "pascal-case": "^3.1.2", "path-case": "^3.0.4", "sentence-case": "^3.0.4", "snake-case": "^3.0.4", "tslib": "^2.0.3"}}, "node_modules/charenc": {"version": "0.0.2", "dev": true, "license": "BSD-3-<PERSON><PERSON>", "engines": {"node": "*"}}, "node_modules/check-error": {"version": "1.0.2", "dev": true, "license": "MIT", "engines": {"node": "*"}}, "node_modules/chokidar": {"version": "3.5.3", "dev": true, "funding": [{"type": "individual", "url": "https://paulmillr.com/funding/"}], "license": "MIT", "dependencies": {"anymatch": "~3.1.2", "braces": "~3.0.2", "glob-parent": "~5.1.2", "is-binary-path": "~2.1.0", "is-glob": "~4.0.1", "normalize-path": "~3.0.0", "readdirp": "~3.6.0"}, "engines": {"node": ">= 8.10.0"}, "optionalDependencies": {"fsevents": "~2.3.2"}}, "node_modules/chokidar/node_modules/glob-parent": {"version": "5.1.2", "dev": true, "license": "ISC", "dependencies": {"is-glob": "^4.0.1"}, "engines": {"node": ">= 6"}}, "node_modules/class-utils": {"version": "0.3.6", "dev": true, "license": "MIT", "dependencies": {"arr-union": "^3.1.0", "define-property": "^0.2.5", "isobject": "^3.0.0", "static-extend": "^0.1.1"}, "engines": {"node": ">=0.10.0"}}, "node_modules/class-utils/node_modules/define-property": {"version": "0.2.5", "dev": true, "license": "MIT", "dependencies": {"is-descriptor": "^0.1.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/class-utils/node_modules/is-accessor-descriptor": {"version": "0.1.6", "dev": true, "license": "MIT", "dependencies": {"kind-of": "^3.0.2"}, "engines": {"node": ">=0.10.0"}}, "node_modules/class-utils/node_modules/is-accessor-descriptor/node_modules/kind-of": {"version": "3.2.2", "dev": true, "license": "MIT", "dependencies": {"is-buffer": "^1.1.5"}, "engines": {"node": ">=0.10.0"}}, "node_modules/class-utils/node_modules/is-data-descriptor": {"version": "0.1.4", "dev": true, "license": "MIT", "dependencies": {"kind-of": "^3.0.2"}, "engines": {"node": ">=0.10.0"}}, "node_modules/class-utils/node_modules/is-data-descriptor/node_modules/kind-of": {"version": "3.2.2", "dev": true, "license": "MIT", "dependencies": {"is-buffer": "^1.1.5"}, "engines": {"node": ">=0.10.0"}}, "node_modules/class-utils/node_modules/is-descriptor": {"version": "0.1.6", "dev": true, "license": "MIT", "dependencies": {"is-accessor-descriptor": "^0.1.6", "is-data-descriptor": "^0.1.4", "kind-of": "^5.0.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/class-utils/node_modules/kind-of": {"version": "5.1.0", "dev": true, "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/clean-stack": {"version": "2.2.0", "dev": true, "license": "MIT", "engines": {"node": ">=6"}}, "node_modules/cli-boxes": {"version": "3.0.0", "license": "MIT", "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/cli-cursor": {"version": "3.1.0", "license": "MIT", "dependencies": {"restore-cursor": "^3.1.0"}, "engines": {"node": ">=8"}}, "node_modules/cli-table3": {"version": "0.6.3", "license": "MIT", "dependencies": {"string-width": "^4.2.0"}, "engines": {"node": "10.* || >= 12.*"}, "optionalDependencies": {"@colors/colors": "1.5.0"}}, "node_modules/clone": {"version": "2.1.2", "dev": true, "license": "MIT", "engines": {"node": ">=0.8"}}, "node_modules/clone-deep": {"version": "4.0.1", "dev": true, "license": "MIT", "dependencies": {"is-plain-object": "^2.0.4", "kind-of": "^6.0.2", "shallow-clone": "^3.0.0"}, "engines": {"node": ">=6"}}, "node_modules/clone-response": {"version": "1.0.3", "license": "MIT", "dependencies": {"mimic-response": "^1.0.0"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/co-compose": {"version": "7.0.3", "license": "MIT"}, "node_modules/collection-visit": {"version": "1.0.0", "dev": true, "license": "MIT", "dependencies": {"map-visit": "^1.0.0", "object-visit": "^1.0.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/color-convert": {"version": "2.0.1", "license": "MIT", "dependencies": {"color-name": "~1.1.4"}, "engines": {"node": ">=7.0.0"}}, "node_modules/color-name": {"version": "1.1.4", "license": "MIT"}, "node_modules/color-support": {"version": "1.1.3", "license": "ISC", "bin": {"color-support": "bin.js"}}, "node_modules/colorette": {"version": "2.0.19", "license": "MIT"}, "node_modules/columnify": {"version": "1.6.0", "dev": true, "license": "MIT", "dependencies": {"strip-ansi": "^6.0.1", "wcwidth": "^1.0.0"}, "engines": {"node": ">=8.0.0"}}, "node_modules/combined-stream": {"version": "1.0.8", "license": "MIT", "dependencies": {"delayed-stream": "~1.0.0"}, "engines": {"node": ">= 0.8"}}, "node_modules/commander": {"version": "10.0.1", "license": "MIT", "engines": {"node": ">=14"}}, "node_modules/comment-json": {"version": "2.4.2", "dev": true, "license": "MIT", "dependencies": {"core-util-is": "^1.0.2", "esprima": "^4.0.1", "has-own-prop": "^2.0.0", "repeat-string": "^1.6.1"}, "engines": {"node": ">= 6"}}, "node_modules/commondir": {"version": "1.0.1", "dev": true, "license": "MIT"}, "node_modules/component-emitter": {"version": "1.3.0", "dev": true, "license": "MIT"}, "node_modules/concat-map": {"version": "0.0.1", "license": "MIT"}, "node_modules/constant-case": {"version": "3.0.4", "license": "MIT", "dependencies": {"no-case": "^3.0.4", "tslib": "^2.0.3", "upper-case": "^2.0.2"}}, "node_modules/content-disposition": {"version": "0.5.4", "license": "MIT", "dependencies": {"safe-buffer": "5.2.1"}, "engines": {"node": ">= 0.6"}}, "node_modules/content-type": {"version": "1.0.5", "resolved": "https://registry.npmjs.org/content-type/-/content-type-1.0.5.tgz", "integrity": "sha512-nTjqfcBFEipKdXCv4YDQWCfmcLZKm81ldF0pAopTvyrFGVbcR6P/VAAd5G7N+0tTr8QqiU0tFadD6FK4NtJwOA==", "engines": {"node": ">= 0.6"}}, "node_modules/convert-hrtime": {"version": "3.0.0", "dev": true, "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/cookie": {"version": "0.5.0", "license": "MIT", "engines": {"node": ">= 0.6"}}, "node_modules/cookie-signature": {"version": "1.0.6", "resolved": "https://registry.npmjs.org/cookie-signature/-/cookie-signature-1.0.6.tgz", "integrity": "sha512-QADzlaHc8icV8I7vbaJXJwod9HWYp8uCqf1xa4OfNu1T7JVxQIrUgOWtHdNDtPiywmFbiS12VjotIXLrKM3orQ=="}, "node_modules/cookiejar": {"version": "2.1.4", "dev": true, "license": "MIT"}, "node_modules/copy-descriptor": {"version": "0.1.1", "dev": true, "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/core-util-is": {"version": "1.0.3", "dev": true, "license": "MIT"}, "node_modules/cp-file": {"version": "9.1.0", "dev": true, "license": "MIT", "dependencies": {"graceful-fs": "^4.1.2", "make-dir": "^3.0.0", "nested-error-stacks": "^2.0.0", "p-event": "^4.1.0"}, "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/cpy": {"version": "8.1.2", "dev": true, "license": "MIT", "dependencies": {"arrify": "^2.0.1", "cp-file": "^7.0.0", "globby": "^9.2.0", "has-glob": "^1.0.0", "junk": "^3.1.0", "nested-error-stacks": "^2.1.0", "p-all": "^2.1.0", "p-filter": "^2.1.0", "p-map": "^3.0.0"}, "engines": {"node": ">=8"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/cpy/node_modules/cp-file": {"version": "7.0.0", "dev": true, "license": "MIT", "dependencies": {"graceful-fs": "^4.1.2", "make-dir": "^3.0.0", "nested-error-stacks": "^2.0.0", "p-event": "^4.1.0"}, "engines": {"node": ">=8"}}, "node_modules/cross-spawn": {"version": "7.0.3", "dev": true, "license": "MIT", "dependencies": {"path-key": "^3.1.0", "shebang-command": "^2.0.0", "which": "^2.0.1"}, "engines": {"node": ">= 8"}}, "node_modules/crypt": {"version": "0.0.2", "dev": true, "license": "BSD-3-<PERSON><PERSON>", "engines": {"node": "*"}}, "node_modules/cuid": {"version": "2.1.8", "license": "MIT"}, "node_modules/dag-map": {"version": "1.0.2", "dev": true, "license": "MIT"}, "node_modules/dashdash": {"version": "1.14.1", "resolved": "https://registry.npmjs.org/dashdash/-/dashdash-1.14.1.tgz", "integrity": "sha512-jRFi8UDGo6j+odZiEpjazZaWqEal3w/basFjQHQEwVtZJGDpxbH1MeYluwCS8Xq5wmLJooDlMgvVarmWfGM44g==", "dependencies": {"assert-plus": "^1.0.0"}, "engines": {"node": ">=0.10"}}, "node_modules/data-uri-to-buffer": {"version": "2.0.2", "dev": true, "license": "MIT"}, "node_modules/date-fns": {"version": "2.30.0", "license": "MIT", "dependencies": {"@babel/runtime": "^7.21.0"}, "engines": {"node": ">=0.11"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/date-fns"}}, "node_modules/date-fns-tz": {"version": "2.0.0", "resolved": "https://registry.npmjs.org/date-fns-tz/-/date-fns-tz-2.0.0.tgz", "integrity": "sha512-OAtcLdB9vxSXTWHdT8b398ARImVwQMyjfYGkKD2zaGpHseG2UPHbHjXELReErZFxWdSLph3c2zOaaTyHfOhERQ==", "peerDependencies": {"date-fns": ">=2.0.0"}}, "node_modules/dateformat": {"version": "4.6.3", "license": "MIT", "engines": {"node": "*"}}, "node_modules/dayjs": {"version": "1.11.10", "resolved": "https://registry.npmjs.org/dayjs/-/dayjs-1.11.10.tgz", "integrity": "sha512-vjAczensTgRcqDERK0SR2XMwsF/tSvnvlv6VcF2GIhg6Sx4yOIt/irsr1RDJsKiIyBzJDpCoXiWWq28MqH2cnQ=="}, "node_modules/debug": {"version": "4.3.4", "license": "MIT", "dependencies": {"ms": "2.1.2"}, "engines": {"node": ">=6.0"}, "peerDependenciesMeta": {"supports-color": {"optional": true}}}, "node_modules/debug/node_modules/ms": {"version": "2.1.2", "license": "MIT"}, "node_modules/decimal.js": {"version": "10.4.3", "dev": true, "license": "MIT"}, "node_modules/decode-uri-component": {"version": "0.2.2", "dev": true, "license": "MIT", "engines": {"node": ">=0.10"}}, "node_modules/decompress-response": {"version": "6.0.0", "license": "MIT", "dependencies": {"mimic-response": "^3.1.0"}, "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/decompress-response/node_modules/mimic-response": {"version": "3.1.0", "license": "MIT", "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/deep-eql": {"version": "4.1.3", "dev": true, "license": "MIT", "dependencies": {"type-detect": "^4.0.0"}, "engines": {"node": ">=6"}}, "node_modules/deep-extend": {"version": "0.6.0", "dev": true, "license": "MIT", "engines": {"node": ">=4.0.0"}}, "node_modules/deep-is": {"version": "0.1.4", "dev": true, "license": "MIT"}, "node_modules/defaults": {"version": "1.0.4", "dev": true, "license": "MIT", "dependencies": {"clone": "^1.0.2"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/defaults/node_modules/clone": {"version": "1.0.4", "dev": true, "license": "MIT", "engines": {"node": ">=0.8"}}, "node_modules/defer-to-connect": {"version": "2.0.1", "license": "MIT", "engines": {"node": ">=10"}}, "node_modules/define-lazy-prop": {"version": "2.0.0", "dev": true, "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/define-property": {"version": "2.0.2", "dev": true, "license": "MIT", "dependencies": {"is-descriptor": "^1.0.2", "isobject": "^3.0.1"}, "engines": {"node": ">=0.10.0"}}, "node_modules/delayed-stream": {"version": "1.0.0", "license": "MIT", "engines": {"node": ">=0.4.0"}}, "node_modules/denque": {"version": "2.1.0", "license": "Apache-2.0", "engines": {"node": ">=0.10"}}, "node_modules/depd": {"version": "2.0.0", "license": "MIT", "engines": {"node": ">= 0.8"}}, "node_modules/destroy": {"version": "1.2.0", "license": "MIT", "engines": {"node": ">= 0.8", "npm": "1.2.8000 || >= 1.4.16"}}, "node_modules/detect-indent": {"version": "6.1.0", "dev": true, "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/dezalgo": {"version": "1.0.4", "dev": true, "license": "ISC", "dependencies": {"asap": "^2.0.0", "wrappy": "1"}}, "node_modules/diff-sequences": {"version": "29.6.3", "dev": true, "license": "MIT", "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}}, "node_modules/dir-glob": {"version": "2.2.2", "dev": true, "license": "MIT", "dependencies": {"path-type": "^3.0.0"}, "engines": {"node": ">=4"}}, "node_modules/doctrine": {"version": "3.0.0", "license": "Apache-2.0", "dependencies": {"esutils": "^2.0.2"}, "engines": {"node": ">=6.0.0"}}, "node_modules/dot-case": {"version": "3.0.4", "license": "MIT", "dependencies": {"no-case": "^3.0.4", "tslib": "^2.0.3"}}, "node_modules/dotenv": {"version": "16.3.1", "license": "BSD-2-<PERSON><PERSON>", "engines": {"node": ">=12"}, "funding": {"url": "https://github.com/motdotla/dotenv?sponsor=1"}}, "node_modules/ecc-jsbn": {"version": "0.1.2", "resolved": "https://registry.npmjs.org/ecc-jsbn/-/ecc-jsbn-0.1.2.tgz", "integrity": "sha512-eh9O+hwRHNbG4BLTjEl3nw044CkGm5X6LoaCf7LPp7UU8Qrt47JYNi6nPX8xjW97TKGKm1ouctg0QSpZe9qrnw==", "dependencies": {"jsbn": "~0.1.0", "safer-buffer": "^2.1.0"}}, "node_modules/ecdsa-sig-formatter": {"version": "1.0.11", "resolved": "https://registry.npmjs.org/ecdsa-sig-formatter/-/ecdsa-sig-formatter-1.0.11.tgz", "integrity": "sha512-nagl3RYrbNv6kQkeJIpt6NJZy8twLB/2vtz6yN9Z4vRKHN4/QZJIEbqohALSgwKdnksuY3k5Addp5lg8sVoVcQ==", "dependencies": {"safe-buffer": "^5.0.1"}}, "node_modules/edge-error": {"version": "2.0.8", "license": "MIT"}, "node_modules/edge-lexer": {"version": "5.0.2", "license": "MIT", "dependencies": {"edge-error": "^3.0.0"}}, "node_modules/edge-lexer/node_modules/edge-error": {"version": "3.0.0", "license": "MIT"}, "node_modules/edge-parser": {"version": "8.2.2", "license": "MIT", "dependencies": {"acorn": "^8.8.2", "astring": "^1.8.4", "edge-error": "^3.0.0", "edge-lexer": "^5.0.2", "js-stringify": "^1.0.2"}}, "node_modules/edge-parser/node_modules/edge-error": {"version": "3.0.0", "license": "MIT"}, "node_modules/edge-supercharged": {"version": "3.1.1", "license": "MIT", "dependencies": {"@poppinss/utils": "^3.1.3", "slash": "^3.0.0"}}, "node_modules/edge-supercharged/node_modules/@poppinss/utils": {"version": "3.3.1", "license": "MIT", "dependencies": {"@types/bytes": "^3.1.1", "@types/he": "^1.1.2", "buffer-alloc": "^1.2.0", "bytes": "^3.1.0", "change-case": "^4.1.2", "cuid": "^2.1.8", "flattie": "^1.1.0", "fs-readdir-recursive": "^1.1.0", "he": "^1.2.0", "kind-of": "^6.0.3", "lodash": "^4.17.21", "ms": "^2.1.3", "pluralize": "^8.0.0", "require-all": "^3.0.0", "resolve-from": "^5.0.0", "slugify": "^1.6.1", "truncatise": "0.0.8"}}, "node_modules/edge.js": {"version": "5.5.1", "license": "MIT", "dependencies": {"@poppinss/inspect": "^1.0.1", "@poppinss/utils": "^5.0.0", "edge-error": "^3.0.0", "edge-lexer": "^5.0.2", "edge-parser": "^8.2.1", "js-stringify": "^1.0.2", "macroable": "^7.0.1", "stringify-attributes": "^2.0.0"}}, "node_modules/edge.js/node_modules/edge-error": {"version": "3.0.0", "license": "MIT"}, "node_modules/editorconfig": {"version": "0.15.3", "dev": true, "license": "MIT", "dependencies": {"commander": "^2.19.0", "lru-cache": "^4.1.5", "semver": "^5.6.0", "sigmund": "^1.0.1"}, "bin": {"editorconfig": "bin/editorconfig"}}, "node_modules/editorconfig/node_modules/commander": {"version": "2.20.3", "dev": true, "license": "MIT"}, "node_modules/editorconfig/node_modules/semver": {"version": "5.7.2", "dev": true, "license": "ISC", "bin": {"semver": "bin/semver"}}, "node_modules/ee-first": {"version": "1.1.1", "license": "MIT"}, "node_modules/emittery": {"version": "0.13.1", "dev": true, "license": "MIT", "engines": {"node": ">=12"}, "funding": {"url": "https://github.com/sindresorhus/emittery?sponsor=1"}}, "node_modules/emoji-regex": {"version": "8.0.0", "license": "MIT"}, "node_modules/encodeurl": {"version": "1.0.2", "license": "MIT", "engines": {"node": ">= 0.8"}}, "node_modules/end-of-stream": {"version": "1.4.4", "license": "MIT", "dependencies": {"once": "^1.4.0"}}, "node_modules/enquirer": {"version": "2.4.1", "license": "MIT", "dependencies": {"ansi-colors": "^4.1.1", "strip-ansi": "^6.0.1"}, "engines": {"node": ">=8.6"}}, "node_modules/es-module-lexer": {"version": "0.3.26", "license": "MIT"}, "node_modules/es6-promise": {"version": "4.2.8", "resolved": "https://registry.npmjs.org/es6-promise/-/es6-promise-4.2.8.tgz", "integrity": "sha512-HJDGx5daxeIvxdBxvG2cb9g4tEvwIk3i8+nhX0yGrYmZUzbkdg8QbDevheDB8gd0//uPj4c1EQua8Q+MViT0/w=="}, "node_modules/escalade": {"version": "3.1.1", "license": "MIT", "engines": {"node": ">=6"}}, "node_modules/escape-goat": {"version": "2.1.1", "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/escape-html": {"version": "1.0.3", "license": "MIT"}, "node_modules/escape-string-regexp": {"version": "4.0.0", "dev": true, "license": "MIT", "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/eslint": {"version": "8.49.0", "dev": true, "license": "MIT", "dependencies": {"@eslint-community/eslint-utils": "^4.2.0", "@eslint-community/regexpp": "^4.6.1", "@eslint/eslintrc": "^2.1.2", "@eslint/js": "8.49.0", "@humanwhocodes/config-array": "^0.11.11", "@humanwhocodes/module-importer": "^1.0.1", "@nodelib/fs.walk": "^1.2.8", "ajv": "^6.12.4", "chalk": "^4.0.0", "cross-spawn": "^7.0.2", "debug": "^4.3.2", "doctrine": "^3.0.0", "escape-string-regexp": "^4.0.0", "eslint-scope": "^7.2.2", "eslint-visitor-keys": "^3.4.3", "espree": "^9.6.1", "esquery": "^1.4.2", "esutils": "^2.0.2", "fast-deep-equal": "^3.1.3", "file-entry-cache": "^6.0.1", "find-up": "^5.0.0", "glob-parent": "^6.0.2", "globals": "^13.19.0", "graphemer": "^1.4.0", "ignore": "^5.2.0", "imurmurhash": "^0.1.4", "is-glob": "^4.0.0", "is-path-inside": "^3.0.3", "js-yaml": "^4.1.0", "json-stable-stringify-without-jsonify": "^1.0.1", "levn": "^0.4.1", "lodash.merge": "^4.6.2", "minimatch": "^3.1.2", "natural-compare": "^1.4.0", "optionator": "^0.9.3", "strip-ansi": "^6.0.1", "text-table": "^0.2.0"}, "bin": {"eslint": "bin/eslint.js"}, "engines": {"node": "^12.22.0 || ^14.17.0 || >=16.0.0"}, "funding": {"url": "https://opencollective.com/eslint"}}, "node_modules/eslint-config-prettier": {"version": "8.10.0", "dev": true, "license": "MIT", "bin": {"eslint-config-prettier": "bin/cli.js"}, "peerDependencies": {"eslint": ">=7.0.0"}}, "node_modules/eslint-plugin-adonis": {"version": "2.1.1", "dev": true, "license": "MIT", "dependencies": {"@typescript-eslint/eslint-plugin": "^5.37.0", "@typescript-eslint/parser": "^5.37.0"}, "engines": {"node": ">=10.0.0"}, "peerDependencies": {"eslint": "^8.0.0"}}, "node_modules/eslint-plugin-prettier": {"version": "4.2.1", "dev": true, "license": "MIT", "dependencies": {"prettier-linter-helpers": "^1.0.0"}, "engines": {"node": ">=12.0.0"}, "peerDependencies": {"eslint": ">=7.28.0", "prettier": ">=2.0.0"}, "peerDependenciesMeta": {"eslint-config-prettier": {"optional": true}}}, "node_modules/eslint-scope": {"version": "7.2.2", "dev": true, "license": "BSD-2-<PERSON><PERSON>", "dependencies": {"esrecurse": "^4.3.0", "estraverse": "^5.2.0"}, "engines": {"node": "^12.22.0 || ^14.17.0 || >=16.0.0"}, "funding": {"url": "https://opencollective.com/eslint"}}, "node_modules/eslint-visitor-keys": {"version": "3.4.3", "dev": true, "license": "Apache-2.0", "engines": {"node": "^12.22.0 || ^14.17.0 || >=16.0.0"}, "funding": {"url": "https://opencollective.com/eslint"}}, "node_modules/eslint/node_modules/argparse": {"version": "2.0.1", "dev": true, "license": "Python-2.0"}, "node_modules/eslint/node_modules/chalk": {"version": "4.1.2", "dev": true, "license": "MIT", "dependencies": {"ansi-styles": "^4.1.0", "supports-color": "^7.1.0"}, "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/chalk/chalk?sponsor=1"}}, "node_modules/eslint/node_modules/js-yaml": {"version": "4.1.0", "dev": true, "license": "MIT", "dependencies": {"argparse": "^2.0.1"}, "bin": {"js-yaml": "bin/js-yaml.js"}}, "node_modules/esm": {"version": "3.2.25", "license": "MIT", "engines": {"node": ">=6"}}, "node_modules/espree": {"version": "9.6.1", "dev": true, "license": "BSD-2-<PERSON><PERSON>", "dependencies": {"acorn": "^8.9.0", "acorn-jsx": "^5.3.2", "eslint-visitor-keys": "^3.4.1"}, "engines": {"node": "^12.22.0 || ^14.17.0 || >=16.0.0"}, "funding": {"url": "https://opencollective.com/eslint"}}, "node_modules/esprima": {"version": "4.0.1", "dev": true, "license": "BSD-2-<PERSON><PERSON>", "bin": {"esparse": "bin/esparse.js", "esvalidate": "bin/esvalidate.js"}, "engines": {"node": ">=4"}}, "node_modules/esquery": {"version": "1.5.0", "dev": true, "license": "BSD-3-<PERSON><PERSON>", "dependencies": {"estraverse": "^5.1.0"}, "engines": {"node": ">=0.10"}}, "node_modules/esrecurse": {"version": "4.3.0", "dev": true, "license": "BSD-2-<PERSON><PERSON>", "dependencies": {"estraverse": "^5.2.0"}, "engines": {"node": ">=4.0"}}, "node_modules/estraverse": {"version": "5.3.0", "dev": true, "license": "BSD-2-<PERSON><PERSON>", "engines": {"node": ">=4.0"}}, "node_modules/esutils": {"version": "2.0.3", "license": "BSD-2-<PERSON><PERSON>", "engines": {"node": ">=0.10.0"}}, "node_modules/etag": {"version": "1.8.1", "license": "MIT", "engines": {"node": ">= 0.6"}}, "node_modules/event-target-shim": {"version": "5.0.1", "license": "MIT", "engines": {"node": ">=6"}}, "node_modules/events": {"version": "3.3.0", "license": "MIT", "engines": {"node": ">=0.8.x"}}, "node_modules/execa": {"version": "5.1.1", "dev": true, "license": "MIT", "dependencies": {"cross-spawn": "^7.0.3", "get-stream": "^6.0.0", "human-signals": "^2.1.0", "is-stream": "^2.0.0", "merge-stream": "^2.0.0", "npm-run-path": "^4.0.1", "onetime": "^5.1.2", "signal-exit": "^3.0.3", "strip-final-newline": "^2.0.0"}, "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/sindresorhus/execa?sponsor=1"}}, "node_modules/expand-brackets": {"version": "2.1.4", "dev": true, "license": "MIT", "dependencies": {"debug": "^2.3.3", "define-property": "^0.2.5", "extend-shallow": "^2.0.1", "posix-character-classes": "^0.1.0", "regex-not": "^1.0.0", "snapdragon": "^0.8.1", "to-regex": "^3.0.1"}, "engines": {"node": ">=0.10.0"}}, "node_modules/expand-brackets/node_modules/debug": {"version": "2.6.9", "dev": true, "license": "MIT", "dependencies": {"ms": "2.0.0"}}, "node_modules/expand-brackets/node_modules/define-property": {"version": "0.2.5", "dev": true, "license": "MIT", "dependencies": {"is-descriptor": "^0.1.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/expand-brackets/node_modules/extend-shallow": {"version": "2.0.1", "dev": true, "license": "MIT", "dependencies": {"is-extendable": "^0.1.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/expand-brackets/node_modules/is-accessor-descriptor": {"version": "0.1.6", "dev": true, "license": "MIT", "dependencies": {"kind-of": "^3.0.2"}, "engines": {"node": ">=0.10.0"}}, "node_modules/expand-brackets/node_modules/is-accessor-descriptor/node_modules/kind-of": {"version": "3.2.2", "dev": true, "license": "MIT", "dependencies": {"is-buffer": "^1.1.5"}, "engines": {"node": ">=0.10.0"}}, "node_modules/expand-brackets/node_modules/is-data-descriptor": {"version": "0.1.4", "dev": true, "license": "MIT", "dependencies": {"kind-of": "^3.0.2"}, "engines": {"node": ">=0.10.0"}}, "node_modules/expand-brackets/node_modules/is-data-descriptor/node_modules/kind-of": {"version": "3.2.2", "dev": true, "license": "MIT", "dependencies": {"is-buffer": "^1.1.5"}, "engines": {"node": ">=0.10.0"}}, "node_modules/expand-brackets/node_modules/is-descriptor": {"version": "0.1.6", "dev": true, "license": "MIT", "dependencies": {"is-accessor-descriptor": "^0.1.6", "is-data-descriptor": "^0.1.4", "kind-of": "^5.0.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/expand-brackets/node_modules/is-extendable": {"version": "0.1.1", "dev": true, "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/expand-brackets/node_modules/kind-of": {"version": "5.1.0", "dev": true, "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/expand-brackets/node_modules/ms": {"version": "2.0.0", "dev": true, "license": "MIT"}, "node_modules/express": {"version": "4.18.2", "resolved": "https://registry.npmjs.org/express/-/express-4.18.2.tgz", "integrity": "sha512-5/PsL6iGPdfQ/lKM1UuielYgv3BUoJfz1aUwU9vHZ+J7gyvwdQXFEBIEIaxeGf0GIcreATNyBExtalisDbuMqQ==", "dependencies": {"accepts": "~1.3.8", "array-flatten": "1.1.1", "body-parser": "1.20.1", "content-disposition": "0.5.4", "content-type": "~1.0.4", "cookie": "0.5.0", "cookie-signature": "1.0.6", "debug": "2.6.9", "depd": "2.0.0", "encodeurl": "~1.0.2", "escape-html": "~1.0.3", "etag": "~1.8.1", "finalhandler": "1.2.0", "fresh": "0.5.2", "http-errors": "2.0.0", "merge-descriptors": "1.0.1", "methods": "~1.1.2", "on-finished": "2.4.1", "parseurl": "~1.3.3", "path-to-regexp": "0.1.7", "proxy-addr": "~2.0.7", "qs": "6.11.0", "range-parser": "~1.2.1", "safe-buffer": "5.2.1", "send": "0.18.0", "serve-static": "1.15.0", "setprototypeof": "1.2.0", "statuses": "2.0.1", "type-is": "~1.6.18", "utils-merge": "1.0.1", "vary": "~1.1.2"}, "engines": {"node": ">= 0.10.0"}}, "node_modules/express/node_modules/debug": {"version": "2.6.9", "resolved": "https://registry.npmjs.org/debug/-/debug-2.6.9.tgz", "integrity": "sha512-bC7ElrdJaJnPbAP+1EotYvqZsb3ecl5wi6Bfi6BJTUcNowp6cvspg0jXznRTKDjm/E7AdgFBVeAPVMNcKGsHMA==", "dependencies": {"ms": "2.0.0"}}, "node_modules/express/node_modules/ms": {"version": "2.0.0", "resolved": "https://registry.npmjs.org/ms/-/ms-2.0.0.tgz", "integrity": "sha512-Tpp60P6IUJDTuOq/5Z8cdskzJujfwqfOTkrwIwj7IRISpnkJnT6SyJ4PCPnGMoFjC9ddhal5KVIYtAt97ix05A=="}, "node_modules/express/node_modules/qs": {"version": "6.11.0", "resolved": "https://registry.npmjs.org/qs/-/qs-6.11.0.tgz", "integrity": "sha512-MvjoMCJwEarSbUYk5O+nmoSzSutSsTwF85zcHPQ9OrlFoZOYIjaqBAJIqIXjptyD5vThxGq52Xu/MaJzRkIk4Q==", "dependencies": {"side-channel": "^1.0.4"}, "engines": {"node": ">=0.6"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/extend": {"version": "3.0.2", "license": "MIT"}, "node_modules/extend-shallow": {"version": "3.0.2", "dev": true, "license": "MIT", "dependencies": {"assign-symbols": "^1.0.0", "is-extendable": "^1.0.1"}, "engines": {"node": ">=0.10.0"}}, "node_modules/extglob": {"version": "2.0.4", "dev": true, "license": "MIT", "dependencies": {"array-unique": "^0.3.2", "define-property": "^1.0.0", "expand-brackets": "^2.1.4", "extend-shallow": "^2.0.1", "fragment-cache": "^0.2.1", "regex-not": "^1.0.0", "snapdragon": "^0.8.1", "to-regex": "^3.0.1"}, "engines": {"node": ">=0.10.0"}}, "node_modules/extglob/node_modules/define-property": {"version": "1.0.0", "dev": true, "license": "MIT", "dependencies": {"is-descriptor": "^1.0.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/extglob/node_modules/extend-shallow": {"version": "2.0.1", "dev": true, "license": "MIT", "dependencies": {"is-extendable": "^0.1.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/extglob/node_modules/is-extendable": {"version": "0.1.1", "dev": true, "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/extsprintf": {"version": "1.3.0", "resolved": "https://registry.npmjs.org/extsprintf/-/extsprintf-1.3.0.tgz", "integrity": "sha512-11Ndz7Nv+mvAC1j0ktTa7fAb0vLyGGX+rMHNBYQviQDGU0Hw7lhctJANqbPhu9nV9/izT/IntTgZ7Im/9LJs9g==", "engines": ["node >=0.6.0"]}, "node_modules/fast-copy": {"version": "3.0.1", "license": "MIT"}, "node_modules/fast-deep-equal": {"version": "3.1.3", "license": "MIT"}, "node_modules/fast-diff": {"version": "1.3.0", "dev": true, "license": "Apache-2.0"}, "node_modules/fast-equals": {"version": "3.0.3", "license": "MIT"}, "node_modules/fast-glob": {"version": "3.3.1", "dev": true, "license": "MIT", "dependencies": {"@nodelib/fs.stat": "^2.0.2", "@nodelib/fs.walk": "^1.2.3", "glob-parent": "^5.1.2", "merge2": "^1.3.0", "micromatch": "^4.0.4"}, "engines": {"node": ">=8.6.0"}}, "node_modules/fast-glob/node_modules/glob-parent": {"version": "5.1.2", "dev": true, "license": "ISC", "dependencies": {"is-glob": "^4.0.1"}, "engines": {"node": ">= 6"}}, "node_modules/fast-json-stable-stringify": {"version": "2.1.0", "license": "MIT"}, "node_modules/fast-levenshtein": {"version": "2.0.6", "dev": true, "license": "MIT"}, "node_modules/fast-redact": {"version": "3.3.0", "license": "MIT", "engines": {"node": ">=6"}}, "node_modules/fast-safe-stringify": {"version": "2.1.1", "license": "MIT"}, "node_modules/fast-xml-parser": {"version": "4.2.5", "funding": [{"type": "paypal", "url": "https://paypal.me/naturalintelligence"}, {"type": "github", "url": "https://github.com/sponsors/NaturalIntelligence"}], "license": "MIT", "dependencies": {"strnum": "^1.0.5"}, "bin": {"fxparser": "src/cli/cli.js"}}, "node_modules/fastq": {"version": "1.15.0", "license": "ISC", "dependencies": {"reusify": "^1.0.4"}}, "node_modules/file-entry-cache": {"version": "6.0.1", "dev": true, "license": "MIT", "dependencies": {"flat-cache": "^3.0.4"}, "engines": {"node": "^10.12.0 || >=12.0.0"}}, "node_modules/file-type": {"version": "16.5.4", "license": "MIT", "dependencies": {"readable-web-to-node-stream": "^3.0.0", "strtok3": "^6.2.4", "token-types": "^4.1.1"}, "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/sindresorhus/file-type?sponsor=1"}}, "node_modules/fill-range": {"version": "7.0.1", "dev": true, "license": "MIT", "dependencies": {"to-regex-range": "^5.0.1"}, "engines": {"node": ">=8"}}, "node_modules/finalhandler": {"version": "1.2.0", "resolved": "https://registry.npmjs.org/finalhandler/-/finalhandler-1.2.0.tgz", "integrity": "sha512-5uXcUVftlQMFnWC9qu/svkWv3GTd2PfUhK/3PLkYNAe7FbqJMt3515HaxE6eRL74GdsriiwujiawdaB1BpEISg==", "dependencies": {"debug": "2.6.9", "encodeurl": "~1.0.2", "escape-html": "~1.0.3", "on-finished": "2.4.1", "parseurl": "~1.3.3", "statuses": "2.0.1", "unpipe": "~1.0.0"}, "engines": {"node": ">= 0.8"}}, "node_modules/finalhandler/node_modules/debug": {"version": "2.6.9", "resolved": "https://registry.npmjs.org/debug/-/debug-2.6.9.tgz", "integrity": "sha512-bC7ElrdJaJnPbAP+1EotYvqZsb3ecl5wi6Bfi6BJTUcNowp6cvspg0jXznRTKDjm/E7AdgFBVeAPVMNcKGsHMA==", "dependencies": {"ms": "2.0.0"}}, "node_modules/finalhandler/node_modules/ms": {"version": "2.0.0", "resolved": "https://registry.npmjs.org/ms/-/ms-2.0.0.tgz", "integrity": "sha512-Tpp60P6IUJDTuOq/5Z8cdskzJujfwqfOTkrwIwj7IRISpnkJnT6SyJ4PCPnGMoFjC9ddhal5KVIYtAt97ix05A=="}, "node_modules/find-cache-dir": {"version": "3.3.2", "dev": true, "license": "MIT", "dependencies": {"commondir": "^1.0.1", "make-dir": "^3.0.2", "pkg-dir": "^4.1.0"}, "engines": {"node": ">=8"}, "funding": {"url": "https://github.com/avajs/find-cache-dir?sponsor=1"}}, "node_modules/find-up": {"version": "5.0.0", "dev": true, "license": "MIT", "dependencies": {"locate-path": "^6.0.0", "path-exists": "^4.0.0"}, "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/flat-cache": {"version": "3.1.0", "dev": true, "license": "MIT", "dependencies": {"flatted": "^3.2.7", "keyv": "^4.5.3", "rimraf": "^3.0.2"}, "engines": {"node": ">=12.0.0"}}, "node_modules/flatstr": {"version": "1.0.12", "license": "MIT"}, "node_modules/flatted": {"version": "3.2.7", "dev": true, "license": "ISC"}, "node_modules/flattie": {"version": "1.1.0", "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/follow-redirects": {"version": "1.15.3", "resolved": "https://registry.npmjs.org/follow-redirects/-/follow-redirects-1.15.3.tgz", "integrity": "sha512-1VzOtuEM8pC9SFU1E+8KfTjZyMztRsgEfwQl44z8A25uy13jSzTj6dyK2Df52iV0vgHCfBwLhDWevLn95w5v6Q==", "funding": [{"type": "individual", "url": "https://github.com/sponsors/Ruben<PERSON>"}], "engines": {"node": ">=4.0"}, "peerDependenciesMeta": {"debug": {"optional": true}}}, "node_modules/for-in": {"version": "1.0.2", "dev": true, "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/forever-agent": {"version": "0.6.1", "resolved": "https://registry.npmjs.org/forever-agent/-/forever-agent-0.6.1.tgz", "integrity": "sha512-j0K<PERSON><PERSON>hm6zeac4lz3oJ3o65qvgQCcPubiyotZrXqEaG4hNagNYO8qdlUrX5vwqv9ohqeT/Z3j6+yW067yWWdUw==", "engines": {"node": "*"}}, "node_modules/form-data": {"version": "4.0.0", "license": "MIT", "dependencies": {"asynckit": "^0.4.0", "combined-stream": "^1.0.8", "mime-types": "^2.1.12"}, "engines": {"node": ">= 6"}}, "node_modules/formidable": {"version": "2.1.2", "dev": true, "license": "MIT", "dependencies": {"dezalgo": "^1.0.4", "hexoid": "^1.0.0", "once": "^1.4.0", "qs": "^6.11.0"}, "funding": {"url": "https://ko-fi.com/tunnckoCore/commissions"}}, "node_modules/forwarded": {"version": "0.2.0", "license": "MIT", "engines": {"node": ">= 0.6"}}, "node_modules/fragment-cache": {"version": "0.2.1", "dev": true, "license": "MIT", "dependencies": {"map-cache": "^0.2.2"}, "engines": {"node": ">=0.10.0"}}, "node_modules/fresh": {"version": "0.5.2", "license": "MIT", "engines": {"node": ">= 0.6"}}, "node_modules/fs-extra": {"version": "10.1.0", "license": "MIT", "dependencies": {"graceful-fs": "^4.2.0", "jsonfile": "^6.0.1", "universalify": "^2.0.0"}, "engines": {"node": ">=12"}}, "node_modules/fs-monkey": {"version": "1.0.4", "license": "Unlicense"}, "node_modules/fs-readdir-recursive": {"version": "1.1.0", "license": "MIT"}, "node_modules/fs.realpath": {"version": "1.0.0", "license": "ISC"}, "node_modules/function-bind": {"version": "1.1.1", "license": "MIT"}, "node_modules/generate-function": {"version": "2.3.1", "license": "MIT", "dependencies": {"is-property": "^1.0.2"}}, "node_modules/get-func-name": {"version": "2.0.0", "dev": true, "license": "MIT", "engines": {"node": "*"}}, "node_modules/get-intrinsic": {"version": "1.2.1", "license": "MIT", "dependencies": {"function-bind": "^1.1.1", "has": "^1.0.3", "has-proto": "^1.0.1", "has-symbols": "^1.0.3"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/get-package-type": {"version": "0.1.0", "license": "MIT", "engines": {"node": ">=8.0.0"}}, "node_modules/get-port": {"version": "5.1.1", "dev": true, "license": "MIT", "engines": {"node": ">=8"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/get-source": {"version": "2.0.12", "dev": true, "license": "Unlicense", "dependencies": {"data-uri-to-buffer": "^2.0.0", "source-map": "^0.6.1"}}, "node_modules/get-stream": {"version": "6.0.1", "license": "MIT", "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/get-value": {"version": "2.0.6", "dev": true, "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/getopts": {"version": "2.3.0", "license": "MIT"}, "node_modules/getpass": {"version": "0.1.7", "resolved": "https://registry.npmjs.org/getpass/-/getpass-0.1.7.tgz", "integrity": "sha512-0fzj9JxOLfJ+XGLhR8ze3unN0KZCgZwiSSDz168VERjK8Wl8kVSdcu2kspd4s4wtAa1y/qrVRiAA0WclVsu0ng==", "dependencies": {"assert-plus": "^1.0.0"}}, "node_modules/glob": {"version": "7.2.3", "dev": true, "license": "ISC", "dependencies": {"fs.realpath": "^1.0.0", "inflight": "^1.0.4", "inherits": "2", "minimatch": "^3.1.1", "once": "^1.3.0", "path-is-absolute": "^1.0.0"}, "engines": {"node": "*"}, "funding": {"url": "https://github.com/sponsors/isaacs"}}, "node_modules/glob-parent": {"version": "6.0.2", "dev": true, "license": "ISC", "dependencies": {"is-glob": "^4.0.3"}, "engines": {"node": ">=10.13.0"}}, "node_modules/glob-to-regexp": {"version": "0.3.0", "dev": true, "license": "BSD"}, "node_modules/globals": {"version": "13.21.0", "dev": true, "license": "MIT", "dependencies": {"type-fest": "^0.20.2"}, "engines": {"node": ">=8"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/globby": {"version": "9.2.0", "dev": true, "license": "MIT", "dependencies": {"@types/glob": "^7.1.1", "array-union": "^1.0.2", "dir-glob": "^2.2.2", "fast-glob": "^2.2.6", "glob": "^7.1.3", "ignore": "^4.0.3", "pify": "^4.0.1", "slash": "^2.0.0"}, "engines": {"node": ">=6"}}, "node_modules/globby/node_modules/@nodelib/fs.stat": {"version": "1.1.3", "dev": true, "license": "MIT", "engines": {"node": ">= 6"}}, "node_modules/globby/node_modules/braces": {"version": "2.3.2", "dev": true, "license": "MIT", "dependencies": {"arr-flatten": "^1.1.0", "array-unique": "^0.3.2", "extend-shallow": "^2.0.1", "fill-range": "^4.0.0", "isobject": "^3.0.1", "repeat-element": "^1.1.2", "snapdragon": "^0.8.1", "snapdragon-node": "^2.0.1", "split-string": "^3.0.2", "to-regex": "^3.0.1"}, "engines": {"node": ">=0.10.0"}}, "node_modules/globby/node_modules/braces/node_modules/extend-shallow": {"version": "2.0.1", "dev": true, "license": "MIT", "dependencies": {"is-extendable": "^0.1.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/globby/node_modules/fast-glob": {"version": "2.2.7", "dev": true, "license": "MIT", "dependencies": {"@mrmlnc/readdir-enhanced": "^2.2.1", "@nodelib/fs.stat": "^1.1.2", "glob-parent": "^3.1.0", "is-glob": "^4.0.0", "merge2": "^1.2.3", "micromatch": "^3.1.10"}, "engines": {"node": ">=4.0.0"}}, "node_modules/globby/node_modules/fill-range": {"version": "4.0.0", "dev": true, "license": "MIT", "dependencies": {"extend-shallow": "^2.0.1", "is-number": "^3.0.0", "repeat-string": "^1.6.1", "to-regex-range": "^2.1.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/globby/node_modules/fill-range/node_modules/extend-shallow": {"version": "2.0.1", "dev": true, "license": "MIT", "dependencies": {"is-extendable": "^0.1.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/globby/node_modules/glob-parent": {"version": "3.1.0", "dev": true, "license": "ISC", "dependencies": {"is-glob": "^3.1.0", "path-dirname": "^1.0.0"}}, "node_modules/globby/node_modules/glob-parent/node_modules/is-glob": {"version": "3.1.0", "dev": true, "license": "MIT", "dependencies": {"is-extglob": "^2.1.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/globby/node_modules/ignore": {"version": "4.0.6", "dev": true, "license": "MIT", "engines": {"node": ">= 4"}}, "node_modules/globby/node_modules/is-extendable": {"version": "0.1.1", "dev": true, "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/globby/node_modules/is-number": {"version": "3.0.0", "dev": true, "license": "MIT", "dependencies": {"kind-of": "^3.0.2"}, "engines": {"node": ">=0.10.0"}}, "node_modules/globby/node_modules/is-number/node_modules/kind-of": {"version": "3.2.2", "dev": true, "license": "MIT", "dependencies": {"is-buffer": "^1.1.5"}, "engines": {"node": ">=0.10.0"}}, "node_modules/globby/node_modules/micromatch": {"version": "3.1.10", "dev": true, "license": "MIT", "dependencies": {"arr-diff": "^4.0.0", "array-unique": "^0.3.2", "braces": "^2.3.1", "define-property": "^2.0.2", "extend-shallow": "^3.0.2", "extglob": "^2.0.4", "fragment-cache": "^0.2.1", "kind-of": "^6.0.2", "nanomatch": "^1.2.9", "object.pick": "^1.3.0", "regex-not": "^1.0.0", "snapdragon": "^0.8.1", "to-regex": "^3.0.2"}, "engines": {"node": ">=0.10.0"}}, "node_modules/globby/node_modules/slash": {"version": "2.0.0", "dev": true, "license": "MIT", "engines": {"node": ">=6"}}, "node_modules/globby/node_modules/to-regex-range": {"version": "2.1.1", "dev": true, "license": "MIT", "dependencies": {"is-number": "^3.0.0", "repeat-string": "^1.6.1"}, "engines": {"node": ">=0.10.0"}}, "node_modules/got": {"version": "11.8.6", "license": "MIT", "dependencies": {"@sindresorhus/is": "^4.0.0", "@szmarczak/http-timer": "^4.0.5", "@types/cacheable-request": "^6.0.1", "@types/responselike": "^1.0.0", "cacheable-lookup": "^5.0.3", "cacheable-request": "^7.0.2", "decompress-response": "^6.0.0", "http2-wrapper": "^1.0.0-beta.5.2", "lowercase-keys": "^2.0.0", "p-cancelable": "^2.0.0", "responselike": "^2.0.0"}, "engines": {"node": ">=10.19.0"}, "funding": {"url": "https://github.com/sindresorhus/got?sponsor=1"}}, "node_modules/graceful-fs": {"version": "4.2.11", "license": "ISC"}, "node_modules/graphemer": {"version": "1.4.0", "dev": true, "license": "MIT"}, "node_modules/har-schema": {"version": "2.0.0", "resolved": "https://registry.npmjs.org/har-schema/-/har-schema-2.0.0.tgz", "integrity": "sha512-Oqluz6zhGX8cyRaTQlFMPw80bSJVG2x/cFb8ZPhUILGgHka9SsokCCOQgpveePerqidZOrT14ipqfJb7ILcW5Q==", "engines": {"node": ">=4"}}, "node_modules/har-validator": {"version": "5.1.5", "resolved": "https://registry.npmjs.org/har-validator/-/har-validator-5.1.5.tgz", "integrity": "sha512-nmT2T0lljbxdQZfspsno9hgrG3Uir6Ks5afism62poxqBM6sDnMEuPmzTq8XN0OEwqKLLdh1jQI3qyE66Nzb3w==", "deprecated": "this library is no longer supported", "dependencies": {"ajv": "^6.12.3", "har-schema": "^2.0.0"}, "engines": {"node": ">=6"}}, "node_modules/has": {"version": "1.0.3", "license": "MIT", "dependencies": {"function-bind": "^1.1.1"}, "engines": {"node": ">= 0.4.0"}}, "node_modules/has-ansi": {"version": "2.0.0", "dev": true, "license": "MIT", "dependencies": {"ansi-regex": "^2.0.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/has-ansi/node_modules/ansi-regex": {"version": "2.1.1", "dev": true, "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/has-flag": {"version": "4.0.0", "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/has-glob": {"version": "1.0.0", "dev": true, "license": "MIT", "dependencies": {"is-glob": "^3.0.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/has-glob/node_modules/is-glob": {"version": "3.1.0", "dev": true, "license": "MIT", "dependencies": {"is-extglob": "^2.1.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/has-own-prop": {"version": "2.0.0", "dev": true, "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/has-proto": {"version": "1.0.1", "license": "MIT", "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/has-symbols": {"version": "1.0.3", "license": "MIT", "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/has-value": {"version": "1.0.0", "dev": true, "license": "MIT", "dependencies": {"get-value": "^2.0.6", "has-values": "^1.0.0", "isobject": "^3.0.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/has-values": {"version": "1.0.0", "dev": true, "license": "MIT", "dependencies": {"is-number": "^3.0.0", "kind-of": "^4.0.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/has-values/node_modules/is-number": {"version": "3.0.0", "dev": true, "license": "MIT", "dependencies": {"kind-of": "^3.0.2"}, "engines": {"node": ">=0.10.0"}}, "node_modules/has-values/node_modules/is-number/node_modules/kind-of": {"version": "3.2.2", "dev": true, "license": "MIT", "dependencies": {"is-buffer": "^1.1.5"}, "engines": {"node": ">=0.10.0"}}, "node_modules/has-values/node_modules/kind-of": {"version": "4.0.0", "dev": true, "license": "MIT", "dependencies": {"is-buffer": "^1.1.5"}, "engines": {"node": ">=0.10.0"}}, "node_modules/has-yarn": {"version": "2.1.0", "dev": true, "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/haye": {"version": "3.0.0", "license": "MIT"}, "node_modules/he": {"version": "1.2.0", "license": "MIT", "bin": {"he": "bin/he"}}, "node_modules/header-case": {"version": "2.0.4", "license": "MIT", "dependencies": {"capital-case": "^1.0.4", "tslib": "^2.0.3"}}, "node_modules/help-me": {"version": "4.2.0", "license": "MIT", "dependencies": {"glob": "^8.0.0", "readable-stream": "^3.6.0"}}, "node_modules/help-me/node_modules/brace-expansion": {"version": "2.0.1", "license": "MIT", "dependencies": {"balanced-match": "^1.0.0"}}, "node_modules/help-me/node_modules/glob": {"version": "8.1.0", "license": "ISC", "dependencies": {"fs.realpath": "^1.0.0", "inflight": "^1.0.4", "inherits": "2", "minimatch": "^5.0.1", "once": "^1.3.0"}, "engines": {"node": ">=12"}, "funding": {"url": "https://github.com/sponsors/isaacs"}}, "node_modules/help-me/node_modules/minimatch": {"version": "5.1.6", "license": "ISC", "dependencies": {"brace-expansion": "^2.0.1"}, "engines": {"node": ">=10"}}, "node_modules/help-me/node_modules/readable-stream": {"version": "3.6.2", "license": "MIT", "dependencies": {"inherits": "^2.0.3", "string_decoder": "^1.1.1", "util-deprecate": "^1.0.1"}, "engines": {"node": ">= 6"}}, "node_modules/hexoid": {"version": "1.0.0", "dev": true, "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/http-cache-semantics": {"version": "4.1.1", "license": "BSD-2-<PERSON><PERSON>"}, "node_modules/http-errors": {"version": "2.0.0", "license": "MIT", "dependencies": {"depd": "2.0.0", "inherits": "2.0.4", "setprototypeof": "1.2.0", "statuses": "2.0.1", "toidentifier": "1.0.1"}, "engines": {"node": ">= 0.8"}}, "node_modules/http-signature": {"version": "1.2.0", "resolved": "https://registry.npmjs.org/http-signature/-/http-signature-1.2.0.tgz", "integrity": "sha512-CAbnr6Rz4CYQkLYUtSNXxQPUH2gK8f3iWexVlsnMeD+GjlsQ0Xsy1cOX+mN3dtxYomRy21CiOzU8Uhw6OwncEQ==", "dependencies": {"assert-plus": "^1.0.0", "jsprim": "^1.2.2", "sshpk": "^1.7.0"}, "engines": {"node": ">=0.8", "npm": ">=1.3.7"}}, "node_modules/http2-wrapper": {"version": "1.0.3", "license": "MIT", "dependencies": {"quick-lru": "^5.1.1", "resolve-alpn": "^1.0.0"}, "engines": {"node": ">=10.19.0"}}, "node_modules/https-proxy-agent": {"version": "5.0.1", "resolved": "https://registry.npmjs.org/https-proxy-agent/-/https-proxy-agent-5.0.1.tgz", "integrity": "sha512-dFcAjpTQFgoLMzC2VwU+C/CbS7uRL0lWmxDITmqm7C+7F0Odmj6s9l6alZc6AELXhrnggM2CeWSXHGOdX2YtwA==", "dependencies": {"agent-base": "6", "debug": "4"}, "engines": {"node": ">= 6"}}, "node_modules/human-signals": {"version": "2.1.0", "dev": true, "license": "Apache-2.0", "engines": {"node": ">=10.17.0"}}, "node_modules/ical-generator": {"version": "4.1.0", "license": "MIT", "dependencies": {"uuid-random": "^1.3.2"}, "engines": {"node": "^14.8.0 || >=16.0.0"}, "peerDependencies": {"@touch4it/ical-timezones": ">=1.6.0", "@types/luxon": ">= 1.26.0", "@types/mocha": ">= 8.2.1", "@types/node": ">= 15.0.0", "dayjs": ">= 1.10.0", "luxon": ">= 1.26.0", "moment": ">= 2.29.0", "moment-timezone": ">= 0.5.33", "rrule": ">= 2.6.8"}, "peerDependenciesMeta": {"@touch4it/ical-timezones": {"optional": true}, "@types/luxon": {"optional": true}, "@types/mocha": {"optional": true}, "@types/node": {"optional": true}, "dayjs": {"optional": true}, "luxon": {"optional": true}, "moment": {"optional": true}, "moment-timezone": {"optional": true}, "rrule": {"optional": true}}}, "node_modules/iconv-lite": {"version": "0.6.3", "license": "MIT", "dependencies": {"safer-buffer": ">= 2.1.2 < 3.0.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/ieee754": {"version": "1.2.1", "funding": [{"type": "github", "url": "https://github.com/sponsors/feross"}, {"type": "patreon", "url": "https://www.patreon.com/feross"}, {"type": "consulting", "url": "https://feross.org/support"}], "license": "BSD-3-<PERSON><PERSON>"}, "node_modules/igniculus": {"version": "1.5.0", "license": "MIT", "engines": {"node": ">=4.0.0"}}, "node_modules/ignore": {"version": "5.2.4", "dev": true, "license": "MIT", "engines": {"node": ">= 4"}}, "node_modules/import-fresh": {"version": "3.3.0", "dev": true, "license": "MIT", "dependencies": {"parent-module": "^1.0.0", "resolve-from": "^4.0.0"}, "engines": {"node": ">=6"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/import-fresh/node_modules/resolve-from": {"version": "4.0.0", "dev": true, "license": "MIT", "engines": {"node": ">=4"}}, "node_modules/imurmurhash": {"version": "0.1.4", "dev": true, "license": "MIT", "engines": {"node": ">=0.8.19"}}, "node_modules/inclusion": {"version": "1.0.1", "dev": true, "license": "ISC", "dependencies": {"parent-module": "^2.0.0"}}, "node_modules/inclusion/node_modules/parent-module": {"version": "2.0.0", "dev": true, "license": "MIT", "dependencies": {"callsites": "^3.1.0"}, "engines": {"node": ">=8"}}, "node_modules/indent-string": {"version": "4.0.0", "dev": true, "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/inflation": {"version": "2.0.0", "license": "MIT", "engines": {"node": ">= 0.8.0"}}, "node_modules/inflight": {"version": "1.0.6", "license": "ISC", "dependencies": {"once": "^1.3.0", "wrappy": "1"}}, "node_modules/inherits": {"version": "2.0.4", "license": "ISC"}, "node_modules/ini": {"version": "1.3.8", "dev": true, "license": "ISC"}, "node_modules/interpret": {"version": "2.2.0", "license": "MIT", "engines": {"node": ">= 0.10"}}, "node_modules/intl-messageformat": {"version": "10.5.2", "license": "BSD-3-<PERSON><PERSON>", "dependencies": {"@formatjs/ecma402-abstract": "1.17.2", "@formatjs/fast-memoize": "2.2.0", "@formatjs/icu-messageformat-parser": "2.6.2", "tslib": "^2.4.0"}}, "node_modules/ipaddr.js": {"version": "1.9.1", "license": "MIT", "engines": {"node": ">= 0.10"}}, "node_modules/is-accessor-descriptor": {"version": "1.0.0", "dev": true, "license": "MIT", "dependencies": {"kind-of": "^6.0.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/is-binary-path": {"version": "2.1.0", "dev": true, "license": "MIT", "dependencies": {"binary-extensions": "^2.0.0"}, "engines": {"node": ">=8"}}, "node_modules/is-buffer": {"version": "1.1.6", "dev": true, "license": "MIT"}, "node_modules/is-core-module": {"version": "2.13.0", "license": "MIT", "dependencies": {"has": "^1.0.3"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/is-data-descriptor": {"version": "1.0.0", "dev": true, "license": "MIT", "dependencies": {"kind-of": "^6.0.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/is-descriptor": {"version": "1.0.2", "dev": true, "license": "MIT", "dependencies": {"is-accessor-descriptor": "^1.0.0", "is-data-descriptor": "^1.0.0", "kind-of": "^6.0.2"}, "engines": {"node": ">=0.10.0"}}, "node_modules/is-docker": {"version": "2.2.1", "dev": true, "license": "MIT", "bin": {"is-docker": "cli.js"}, "engines": {"node": ">=8"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/is-extendable": {"version": "1.0.1", "dev": true, "license": "MIT", "dependencies": {"is-plain-object": "^2.0.4"}, "engines": {"node": ">=0.10.0"}}, "node_modules/is-extglob": {"version": "2.1.1", "dev": true, "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/is-fullwidth-code-point": {"version": "3.0.0", "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/is-glob": {"version": "4.0.3", "dev": true, "license": "MIT", "dependencies": {"is-extglob": "^2.1.1"}, "engines": {"node": ">=0.10.0"}}, "node_modules/is-invalid-path": {"version": "0.1.0", "dev": true, "license": "MIT", "dependencies": {"is-glob": "^2.0.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/is-invalid-path/node_modules/is-extglob": {"version": "1.0.0", "dev": true, "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/is-invalid-path/node_modules/is-glob": {"version": "2.0.1", "dev": true, "license": "MIT", "dependencies": {"is-extglob": "^1.0.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/is-number": {"version": "7.0.0", "dev": true, "license": "MIT", "engines": {"node": ">=0.12.0"}}, "node_modules/is-path-inside": {"version": "3.0.3", "dev": true, "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/is-plain-object": {"version": "2.0.4", "dev": true, "license": "MIT", "dependencies": {"isobject": "^3.0.1"}, "engines": {"node": ">=0.10.0"}}, "node_modules/is-property": {"version": "1.0.2", "license": "MIT"}, "node_modules/is-stream": {"version": "2.0.1", "dev": true, "license": "MIT", "engines": {"node": ">=8"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/is-typedarray": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/is-typedarray/-/is-typedarray-1.0.0.tgz", "integrity": "sha512-cyA56iCMHAh5CdzjJIa4aohJyeO1YbwLi3Jc35MmRU6poroFjIGZzUzupGiRPOjgHg9TLu43xbpwXk523fMxKA=="}, "node_modules/is-valid-path": {"version": "0.1.1", "dev": true, "license": "MIT", "dependencies": {"is-invalid-path": "^0.1.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/is-windows": {"version": "1.0.2", "dev": true, "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/is-wsl": {"version": "2.2.0", "dev": true, "license": "MIT", "dependencies": {"is-docker": "^2.0.0"}, "engines": {"node": ">=8"}}, "node_modules/isarray": {"version": "1.0.0", "dev": true, "license": "MIT"}, "node_modules/isexe": {"version": "2.0.0", "dev": true, "license": "ISC"}, "node_modules/isobject": {"version": "3.0.1", "dev": true, "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/isstream": {"version": "0.1.2", "resolved": "https://registry.npmjs.org/isstream/-/isstream-0.1.2.tgz", "integrity": "sha512-Yljz7ffyPbrLpLngrMtZ7NduUgVvi6wG9RJ9IUcyCd59YQ911PBJphODUcbOVbqYfxe1wuYf/LJ8PauMRwsM/g=="}, "node_modules/jest-diff": {"version": "29.7.0", "dev": true, "license": "MIT", "dependencies": {"chalk": "^4.0.0", "diff-sequences": "^29.6.3", "jest-get-type": "^29.6.3", "pretty-format": "^29.7.0"}, "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}}, "node_modules/jest-diff/node_modules/chalk": {"version": "4.1.2", "dev": true, "license": "MIT", "dependencies": {"ansi-styles": "^4.1.0", "supports-color": "^7.1.0"}, "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/chalk/chalk?sponsor=1"}}, "node_modules/jest-get-type": {"version": "29.6.3", "dev": true, "license": "MIT", "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}}, "node_modules/jest-matcher-utils": {"version": "25.5.0", "dev": true, "license": "MIT", "dependencies": {"chalk": "^3.0.0", "jest-diff": "^25.5.0", "jest-get-type": "^25.2.6", "pretty-format": "^25.5.0"}, "engines": {"node": ">= 8.3"}}, "node_modules/jest-matcher-utils/node_modules/diff-sequences": {"version": "25.2.6", "dev": true, "license": "MIT", "engines": {"node": ">= 8.3"}}, "node_modules/jest-matcher-utils/node_modules/jest-diff": {"version": "25.5.0", "dev": true, "license": "MIT", "dependencies": {"chalk": "^3.0.0", "diff-sequences": "^25.2.6", "jest-get-type": "^25.2.6", "pretty-format": "^25.5.0"}, "engines": {"node": ">= 8.3"}}, "node_modules/jest-matcher-utils/node_modules/jest-get-type": {"version": "25.2.6", "dev": true, "license": "MIT", "engines": {"node": ">= 8.3"}}, "node_modules/jest-matcher-utils/node_modules/pretty-format": {"version": "25.5.0", "dev": true, "license": "MIT", "dependencies": {"@jest/types": "^25.5.0", "ansi-regex": "^5.0.0", "ansi-styles": "^4.0.0", "react-is": "^16.12.0"}, "engines": {"node": ">= 8.3"}}, "node_modules/jest-matcher-utils/node_modules/react-is": {"version": "16.13.1", "dev": true, "license": "MIT"}, "node_modules/jest-worker": {"version": "27.5.1", "license": "MIT", "dependencies": {"@types/node": "*", "merge-stream": "^2.0.0", "supports-color": "^8.0.0"}, "engines": {"node": ">= 10.13.0"}}, "node_modules/jest-worker/node_modules/supports-color": {"version": "8.1.1", "license": "MIT", "dependencies": {"has-flag": "^4.0.0"}, "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/chalk/supports-color?sponsor=1"}}, "node_modules/joycon": {"version": "3.1.1", "license": "MIT", "engines": {"node": ">=10"}}, "node_modules/js-stringify": {"version": "1.0.2", "license": "MIT"}, "node_modules/js-tokens": {"version": "3.0.2", "dev": true, "license": "MIT"}, "node_modules/js-yaml": {"version": "3.14.1", "dev": true, "license": "MIT", "dependencies": {"argparse": "^1.0.7", "esprima": "^4.0.0"}, "bin": {"js-yaml": "bin/js-yaml.js"}}, "node_modules/jsbn": {"version": "0.1.1", "resolved": "https://registry.npmjs.org/jsbn/-/jsbn-0.1.1.tgz", "integrity": "sha512-UVU9dibq2JcFWxQPA6KCqj5O42VOmAY3zQUfEKxU0KpTGXwNoCjkX1e13eHNvw/xPynt6pU0rZ1htjWTNTSXsg=="}, "node_modules/json-buffer": {"version": "3.0.1", "license": "MIT"}, "node_modules/json-schema": {"version": "0.4.0", "resolved": "https://registry.npmjs.org/json-schema/-/json-schema-0.4.0.tgz", "integrity": "sha512-es94M3nTIfsEPisRafak+HDLfHXnKBhV3vU5eqPcS3flIWqcxJWgXHXiey3YrpaNsanY5ei1VoYEbOzijuq9BA=="}, "node_modules/json-schema-deref-sync": {"version": "0.14.0", "dev": true, "license": "MIT", "dependencies": {"clone": "^2.1.2", "dag-map": "~1.0.0", "is-valid-path": "^0.1.1", "lodash": "^4.17.13", "md5": "~2.2.0", "memory-cache": "~0.2.0", "traverse": "~0.6.6", "valid-url": "~1.0.9"}, "engines": {"node": ">=6.0.0"}}, "node_modules/json-schema-traverse": {"version": "0.4.1", "license": "MIT"}, "node_modules/json-stable-stringify-without-jsonify": {"version": "1.0.1", "dev": true, "license": "MIT"}, "node_modules/json-stringify-safe": {"version": "5.0.1", "resolved": "https://registry.npmjs.org/json-stringify-safe/-/json-stringify-safe-5.0.1.tgz", "integrity": "sha512-ZClg6AaYvamvYEE82d3Iyd3vSSIjQ+odgjaTzRuO3s7toCdFKczob2i0zCh7JE8kWn17yvAWhUVxvqGwUalsRA=="}, "node_modules/jsonfile": {"version": "6.1.0", "license": "MIT", "dependencies": {"universalify": "^2.0.0"}, "optionalDependencies": {"graceful-fs": "^4.1.6"}}, "node_modules/jsonwebtoken": {"version": "9.0.2", "resolved": "https://registry.npmjs.org/jsonwebtoken/-/jsonwebtoken-9.0.2.tgz", "integrity": "sha512-PRp66vJ865SSqOlgqS8hujT5U4AOgMfhrwYIuIhfKaoSCZcirrmASQr8CX7cUg+RMih+hgznrjp99o+W4pJLHQ==", "dependencies": {"jws": "^3.2.2", "lodash.includes": "^4.3.0", "lodash.isboolean": "^3.0.3", "lodash.isinteger": "^4.0.4", "lodash.isnumber": "^3.0.3", "lodash.isplainobject": "^4.0.6", "lodash.isstring": "^4.0.1", "lodash.once": "^4.0.0", "ms": "^2.1.1", "semver": "^7.5.4"}, "engines": {"node": ">=12", "npm": ">=6"}}, "node_modules/jsprim": {"version": "1.4.2", "resolved": "https://registry.npmjs.org/jsprim/-/jsprim-1.4.2.tgz", "integrity": "sha512-P2bSOMAc/ciLz6DzgjVlGJP9+BrJWu5UDGK70C2iweC5QBIeFf0ZXRvGjEj2uYgrY2MkAAhsSWHDWlFtEroZWw==", "dependencies": {"assert-plus": "1.0.0", "extsprintf": "1.3.0", "json-schema": "0.4.0", "verror": "1.10.0"}, "engines": {"node": ">=0.6.0"}}, "node_modules/junk": {"version": "3.1.0", "dev": true, "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/jwa": {"version": "1.4.1", "resolved": "https://registry.npmjs.org/jwa/-/jwa-1.4.1.tgz", "integrity": "sha512-qiLX/xhEEFKUAJ6FiBMbes3w9ATzyk5W7Hvzpa/SLYdxNtng+gcurvrI7TbACjIXlsJyr05/S1oUhZrc63evQA==", "dependencies": {"buffer-equal-constant-time": "1.0.1", "ecdsa-sig-formatter": "1.0.11", "safe-buffer": "^5.0.1"}}, "node_modules/jws": {"version": "3.2.2", "resolved": "https://registry.npmjs.org/jws/-/jws-3.2.2.tgz", "integrity": "sha512-YHlZCB6lMTllWDtSPHz/ZXTsi8S00usEV6v1tjq8tOUZzw7DpSDWVXjXDre6ed1w/pd495ODpHZYSdkRTsa0HA==", "dependencies": {"jwa": "^1.4.1", "safe-buffer": "^5.0.1"}}, "node_modules/keyv": {"version": "4.5.3", "license": "MIT", "dependencies": {"json-buffer": "3.0.1"}}, "node_modules/kind-of": {"version": "6.0.3", "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/kleur": {"version": "4.1.5", "license": "MIT", "engines": {"node": ">=6"}}, "node_modules/knex": {"version": "2.5.1", "license": "MIT", "dependencies": {"colorette": "2.0.19", "commander": "^10.0.0", "debug": "4.3.4", "escalade": "^3.1.1", "esm": "^3.2.25", "get-package-type": "^0.1.0", "getopts": "2.3.0", "interpret": "^2.2.0", "lodash": "^4.17.21", "pg-connection-string": "2.6.1", "rechoir": "^0.8.0", "resolve-from": "^5.0.0", "tarn": "^3.0.2", "tildify": "2.0.0"}, "bin": {"knex": "bin/cli.js"}, "engines": {"node": ">=12"}, "peerDependenciesMeta": {"better-sqlite3": {"optional": true}, "mysql": {"optional": true}, "mysql2": {"optional": true}, "pg": {"optional": true}, "pg-native": {"optional": true}, "sqlite3": {"optional": true}, "tedious": {"optional": true}}}, "node_modules/knex-dynamic-connection": {"version": "3.1.0", "license": "MIT", "dependencies": {"debug": "^4.3.4", "knex": "^2.5.0"}, "engines": {"node": ">=14.0.0"}}, "node_modules/leven": {"version": "3.1.0", "license": "MIT", "engines": {"node": ">=6"}}, "node_modules/levn": {"version": "0.4.1", "dev": true, "license": "MIT", "dependencies": {"prelude-ls": "^1.2.1", "type-check": "~0.4.0"}, "engines": {"node": ">= 0.8.0"}}, "node_modules/listify": {"version": "1.0.3", "dev": true, "license": "MIT", "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/locate-path": {"version": "6.0.0", "dev": true, "license": "MIT", "dependencies": {"p-locate": "^5.0.0"}, "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/lodash": {"version": "4.17.21", "license": "MIT"}, "node_modules/lodash.flatten": {"version": "4.4.0", "dev": true, "license": "MIT"}, "node_modules/lodash.get": {"version": "4.4.2", "license": "MIT"}, "node_modules/lodash.includes": {"version": "4.3.0", "resolved": "https://registry.npmjs.org/lodash.includes/-/lodash.includes-4.3.0.tgz", "integrity": "sha512-W3Bx6mdkRTGtlJISOvVD/lbqjTlPPUDTMnlXZFnVwi9NKJ6tiAk6LVdlhZMm17VZisqhKcgzpO5Wz91PCt5b0w=="}, "node_modules/lodash.isboolean": {"version": "3.0.3", "resolved": "https://registry.npmjs.org/lodash.isboolean/-/lodash.isboolean-3.0.3.tgz", "integrity": "sha512-Bz5mupy2SVbPHURB98VAcw+aHh4vRV5IPNhILUCsOzRmsTmSQ17jIuqopAentWoehktxGd9e/hbIXq980/1QJg=="}, "node_modules/lodash.isequal": {"version": "4.5.0", "license": "MIT"}, "node_modules/lodash.isinteger": {"version": "4.0.4", "resolved": "https://registry.npmjs.org/lodash.isinteger/-/lodash.isinteger-4.0.4.tgz", "integrity": "sha512-DBwtEWN2caHQ9/imiNeEA5ys1JoRtRfY3d7V9wkqtbycnAmTvRRmbHKDV4a0EYc678/dia0jrte4tjYwVBaZUA=="}, "node_modules/lodash.isnumber": {"version": "3.0.3", "resolved": "https://registry.npmjs.org/lodash.isnumber/-/lodash.isnumber-3.0.3.tgz", "integrity": "sha512-QYqzpfwO3/CWf3XP+Z+tkQsfaLL/EnUlXWVkIk5FUPc4sBdTehEqZONuyRt2P67PXAk+NXmTBcc97zw9t1FQrw=="}, "node_modules/lodash.isplainobject": {"version": "4.0.6", "resolved": "https://registry.npmjs.org/lodash.isplainobject/-/lodash.isplainobject-4.0.6.tgz", "integrity": "sha512-oSXzaWypCMHkPC3NvBEaPHf0KsA5mvPrOPgQWDsbg8n7orZ290M0BmC/jgRZ4vcJ6DTAhjrsSYgdsW/F+MFOBA=="}, "node_modules/lodash.isstring": {"version": "4.0.1", "resolved": "https://registry.npmjs.org/lodash.isstring/-/lodash.isstring-4.0.1.tgz", "integrity": "sha512-0wJxfxH1wgO3GrbuP+dTTk7op+6L41QCXbGINEmD+ny/G/eCqGzxyCsh7159S+mgDDcoarnBw6PC1PS5+wUGgw=="}, "node_modules/lodash.merge": {"version": "4.6.2", "dev": true, "license": "MIT"}, "node_modules/lodash.mergewith": {"version": "4.6.2", "resolved": "https://registry.npmjs.org/lodash.mergewith/-/lodash.mergewith-4.6.2.tgz", "integrity": "sha512-GK3g5RPZWTRSeLSpgP8Xhra+pnjBC56q9FZYe1d5RN3TJ35dbkGy3YqBSMbyCrlbi+CM9Z3Jk5yTL7RCsqboyQ=="}, "node_modules/lodash.once": {"version": "4.1.1", "resolved": "https://registry.npmjs.org/lodash.once/-/lodash.once-4.1.1.tgz", "integrity": "sha512-Sb487aTOCr9drQVL8pIxOzVhafOjZN9UU54hiN8PU3uAiSV7lx1yYNpbNmex2PK6dSJoNTSJUUswT651yww3Mg=="}, "node_modules/lodash.set": {"version": "4.3.2", "dev": true, "license": "MIT"}, "node_modules/log-update": {"version": "4.0.0", "license": "MIT", "dependencies": {"ansi-escapes": "^4.3.0", "cli-cursor": "^3.1.0", "slice-ansi": "^4.0.0", "wrap-ansi": "^6.2.0"}, "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/long": {"version": "5.2.3", "license": "Apache-2.0"}, "node_modules/loupe": {"version": "2.3.6", "dev": true, "license": "MIT", "dependencies": {"get-func-name": "^2.0.0"}}, "node_modules/lower-case": {"version": "2.0.2", "license": "MIT", "dependencies": {"tslib": "^2.0.3"}}, "node_modules/lowercase-keys": {"version": "2.0.0", "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/lru-cache": {"version": "4.1.5", "dev": true, "license": "ISC", "dependencies": {"pseudomap": "^1.0.2", "yallist": "^2.1.2"}}, "node_modules/luxon": {"version": "3.4.3", "license": "MIT", "engines": {"node": ">=12"}}, "node_modules/macroable": {"version": "7.0.2", "license": "MIT"}, "node_modules/make-dir": {"version": "3.1.0", "dev": true, "license": "MIT", "dependencies": {"semver": "^6.0.0"}, "engines": {"node": ">=8"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/make-dir/node_modules/semver": {"version": "6.3.1", "dev": true, "license": "ISC", "bin": {"semver": "bin/semver.js"}}, "node_modules/map-age-cleaner": {"version": "0.1.3", "dev": true, "license": "MIT", "dependencies": {"p-defer": "^1.0.0"}, "engines": {"node": ">=6"}}, "node_modules/map-cache": {"version": "0.2.2", "dev": true, "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/map-visit": {"version": "1.0.0", "dev": true, "license": "MIT", "dependencies": {"object-visit": "^1.0.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/marked": {"version": "4.3.0", "resolved": "https://registry.npmjs.org/marked/-/marked-4.3.0.tgz", "integrity": "sha512-PRsaiG84bK+AMvxziE/lCFss8juXjNaWzVbN5tXAm4XjeaS9NAHhop+PjQxz2A9h8Q4M/xGmzP8vqNwy6JeK0A==", "bin": {"marked": "bin/marked.js"}, "engines": {"node": ">= 12"}}, "node_modules/marked-terminal": {"version": "5.2.0", "resolved": "https://registry.npmjs.org/marked-terminal/-/marked-terminal-5.2.0.tgz", "integrity": "sha512-Piv6yNwAQXGFjZSaiNljyNFw7jKDdGrw70FSbtxEyldLsyeuV5ZHm/1wW++kWbrOF1VPnUgYOhB2oLL0ZpnekA==", "dev": true, "dependencies": {"ansi-escapes": "^6.2.0", "cardinal": "^2.1.1", "chalk": "^5.2.0", "cli-table3": "^0.6.3", "node-emoji": "^1.11.0", "supports-hyperlinks": "^2.3.0"}, "engines": {"node": ">=14.13.1 || >=16.0.0"}, "peerDependencies": {"marked": "^1.0.0 || ^2.0.0 || ^3.0.0 || ^4.0.0 || ^5.0.0"}}, "node_modules/marked-terminal/node_modules/ansi-escapes": {"version": "6.2.0", "resolved": "https://registry.npmjs.org/ansi-escapes/-/ansi-escapes-6.2.0.tgz", "integrity": "sha512-kzRaCqXnpzWs+3z5ABPQiVke+iq0KXkHo8xiWV4RPTi5Yli0l97BEQuhXV1s7+aSU/fu1kUuxgS4MsQ0fRuygw==", "dev": true, "dependencies": {"type-fest": "^3.0.0"}, "engines": {"node": ">=14.16"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/marked-terminal/node_modules/chalk": {"version": "5.3.0", "resolved": "https://registry.npmjs.org/chalk/-/chalk-5.3.0.tgz", "integrity": "sha512-dLitG79d+GV1Nb/VYcCDFivJeK1hiukt9QjRNVOsUtTy1rR1YJsmpGGTZ3qJos+uw7WmWF4wUwBd9jxjocFC2w==", "dev": true, "engines": {"node": "^12.17.0 || ^14.13 || >=16.0.0"}, "funding": {"url": "https://github.com/chalk/chalk?sponsor=1"}}, "node_modules/marked-terminal/node_modules/type-fest": {"version": "3.13.1", "resolved": "https://registry.npmjs.org/type-fest/-/type-fest-3.13.1.tgz", "integrity": "sha512-tLq3bSNx+xSpwvAJnzrK0Ep5CLNWjvFTOp71URMaAEWBfRb9nnJiBoUe0tF8bI4ZFO3omgBR6NvnbzVUT3Ly4g==", "dev": true, "engines": {"node": ">=14.16"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/md5": {"version": "2.2.1", "dev": true, "license": "BSD-3-<PERSON><PERSON>", "dependencies": {"charenc": "~0.0.1", "crypt": "~0.0.1", "is-buffer": "~1.1.1"}}, "node_modules/media-typer": {"version": "1.1.0", "license": "MIT", "engines": {"node": ">= 0.8"}}, "node_modules/mem": {"version": "8.1.1", "dev": true, "license": "MIT", "dependencies": {"map-age-cleaner": "^0.1.3", "mimic-fn": "^3.1.0"}, "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/sindresorhus/mem?sponsor=1"}}, "node_modules/memfs": {"version": "3.5.3", "license": "Unlicense", "dependencies": {"fs-monkey": "^1.0.4"}, "engines": {"node": ">= 4.0.0"}}, "node_modules/memory-cache": {"version": "0.2.0", "dev": true, "license": "BSD-2-<PERSON><PERSON>"}, "node_modules/merge-descriptors": {"version": "1.0.1", "resolved": "https://registry.npmjs.org/merge-descriptors/-/merge-descriptors-1.0.1.tgz", "integrity": "sha512-cCi6g3/Zr1iqQi6ySbseM1Xvooa98N0w31jzUYrXPX2xqObmFGHJ0tQ5u74H3mVh7wLouTseZyYIq39g8cNp1w=="}, "node_modules/merge-stream": {"version": "2.0.0", "license": "MIT"}, "node_modules/merge2": {"version": "1.4.1", "dev": true, "license": "MIT", "engines": {"node": ">= 8"}}, "node_modules/methods": {"version": "1.1.2", "license": "MIT", "engines": {"node": ">= 0.6"}}, "node_modules/micro-memoize": {"version": "4.1.2", "license": "MIT"}, "node_modules/micromatch": {"version": "4.0.5", "dev": true, "license": "MIT", "dependencies": {"braces": "^3.0.2", "picomatch": "^2.3.1"}, "engines": {"node": ">=8.6"}}, "node_modules/mime": {"version": "1.6.0", "license": "MIT", "bin": {"mime": "cli.js"}, "engines": {"node": ">=4"}}, "node_modules/mime-db": {"version": "1.52.0", "license": "MIT", "engines": {"node": ">= 0.6"}}, "node_modules/mime-kind": {"version": "3.0.0", "license": "MIT", "dependencies": {"file-type": "^12.1.0", "mime-types": "^2.1.24"}, "engines": {"node": ">=8.3.0"}}, "node_modules/mime-kind/node_modules/file-type": {"version": "12.4.2", "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/mime-types": {"version": "2.1.35", "license": "MIT", "dependencies": {"mime-db": "1.52.0"}, "engines": {"node": ">= 0.6"}}, "node_modules/mimic-fn": {"version": "3.1.0", "dev": true, "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/mimic-response": {"version": "1.0.1", "license": "MIT", "engines": {"node": ">=4"}}, "node_modules/minimatch": {"version": "3.1.2", "license": "ISC", "dependencies": {"brace-expansion": "^1.1.7"}, "engines": {"node": "*"}}, "node_modules/minimist": {"version": "1.2.8", "license": "MIT", "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/mixin-deep": {"version": "1.3.2", "dev": true, "license": "MIT", "dependencies": {"for-in": "^1.0.2", "is-extendable": "^1.0.1"}, "engines": {"node": ">=0.10.0"}}, "node_modules/moize": {"version": "6.1.6", "license": "MIT", "dependencies": {"fast-equals": "^3.0.1", "micro-memoize": "^4.1.2"}}, "node_modules/mrm-core": {"version": "7.1.13", "dev": true, "license": "MIT", "dependencies": {"babel-code-frame": "^6.26.0", "comment-json": "^2.2.0", "detect-indent": "^6.0.0", "editorconfig": "^0.15.3", "find-up": "^4.1.0", "fs-extra": "^8.1.0", "kleur": "^3.0.3", "listify": "^1.0.0", "lodash": "^4.17.15", "minimist": "^1.2.0", "prop-ini": "^0.0.2", "rc": "^1.2.8", "readme-badger": "^0.3.0", "semver": "^6.3.0", "smpltmpl": "^1.0.2", "split-lines": "^2.0.0", "strip-bom": "^4.0.0", "validate-npm-package-name": "^3.0.0", "webpack-merge": "^4.2.2", "yaml": "^2.0.0-1"}, "engines": {"node": ">=10.13"}}, "node_modules/mrm-core/node_modules/find-up": {"version": "4.1.0", "dev": true, "license": "MIT", "dependencies": {"locate-path": "^5.0.0", "path-exists": "^4.0.0"}, "engines": {"node": ">=8"}}, "node_modules/mrm-core/node_modules/fs-extra": {"version": "8.1.0", "dev": true, "license": "MIT", "dependencies": {"graceful-fs": "^4.2.0", "jsonfile": "^4.0.0", "universalify": "^0.1.0"}, "engines": {"node": ">=6 <7 || >=8"}}, "node_modules/mrm-core/node_modules/jsonfile": {"version": "4.0.0", "dev": true, "license": "MIT", "optionalDependencies": {"graceful-fs": "^4.1.6"}}, "node_modules/mrm-core/node_modules/kleur": {"version": "3.0.3", "dev": true, "license": "MIT", "engines": {"node": ">=6"}}, "node_modules/mrm-core/node_modules/locate-path": {"version": "5.0.0", "dev": true, "license": "MIT", "dependencies": {"p-locate": "^4.1.0"}, "engines": {"node": ">=8"}}, "node_modules/mrm-core/node_modules/p-limit": {"version": "2.3.0", "dev": true, "license": "MIT", "dependencies": {"p-try": "^2.0.0"}, "engines": {"node": ">=6"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/mrm-core/node_modules/p-locate": {"version": "4.1.0", "dev": true, "license": "MIT", "dependencies": {"p-limit": "^2.2.0"}, "engines": {"node": ">=8"}}, "node_modules/mrm-core/node_modules/semver": {"version": "6.3.1", "dev": true, "license": "ISC", "bin": {"semver": "bin/semver.js"}}, "node_modules/mrm-core/node_modules/universalify": {"version": "0.1.2", "dev": true, "license": "MIT", "engines": {"node": ">= 4.0.0"}}, "node_modules/ms": {"version": "2.1.3", "license": "MIT"}, "node_modules/multi-part": {"version": "3.0.0", "license": "MIT", "dependencies": {"mime-kind": "^3.0.0", "multi-part-lite": "^1.0.0"}, "engines": {"node": ">=8.3.0"}}, "node_modules/multi-part-lite": {"version": "1.0.0", "license": "MIT", "engines": {"node": ">=8.3.0"}}, "node_modules/mustache": {"version": "4.2.0", "license": "MIT", "bin": {"mustache": "bin/mustache"}}, "node_modules/mysql2": {"version": "3.6.1", "license": "MIT", "dependencies": {"denque": "^2.1.0", "generate-function": "^2.3.1", "iconv-lite": "^0.6.3", "long": "^5.2.1", "lru-cache": "^8.0.0", "named-placeholders": "^1.1.3", "seq-queue": "^0.0.5", "sqlstring": "^2.3.2"}, "engines": {"node": ">= 8.0"}}, "node_modules/mysql2/node_modules/lru-cache": {"version": "8.0.5", "license": "ISC", "engines": {"node": ">=16.14"}}, "node_modules/named-placeholders": {"version": "1.1.3", "license": "MIT", "dependencies": {"lru-cache": "^7.14.1"}, "engines": {"node": ">=12.0.0"}}, "node_modules/named-placeholders/node_modules/lru-cache": {"version": "7.18.3", "license": "ISC", "engines": {"node": ">=12"}}, "node_modules/nanomatch": {"version": "1.2.13", "dev": true, "license": "MIT", "dependencies": {"arr-diff": "^4.0.0", "array-unique": "^0.3.2", "define-property": "^2.0.2", "extend-shallow": "^3.0.2", "fragment-cache": "^0.2.1", "is-windows": "^1.0.2", "kind-of": "^6.0.2", "object.pick": "^1.3.0", "regex-not": "^1.0.0", "snapdragon": "^0.8.1", "to-regex": "^3.0.1"}, "engines": {"node": ">=0.10.0"}}, "node_modules/natural-compare": {"version": "1.4.0", "dev": true, "license": "MIT"}, "node_modules/natural-compare-lite": {"version": "1.4.0", "dev": true, "license": "MIT"}, "node_modules/negotiator": {"version": "0.6.3", "license": "MIT", "engines": {"node": ">= 0.6"}}, "node_modules/nested-error-stacks": {"version": "2.1.1", "dev": true, "license": "MIT"}, "node_modules/no-case": {"version": "3.0.4", "license": "MIT", "dependencies": {"lower-case": "^2.0.2", "tslib": "^2.0.3"}}, "node_modules/node-emoji": {"version": "1.11.0", "resolved": "https://registry.npmjs.org/node-emoji/-/node-emoji-1.11.0.tgz", "integrity": "sha512-wo2DpQkQp7Sjm2A0cq+sN7EHKO6Sl0ctXeBdFZrL9T9+UywORbufTcTZxom8YqpLQt/FqNMUkOpkZrJVYSKD3A==", "dev": true, "dependencies": {"lodash": "^4.17.21"}}, "node_modules/node-fetch": {"version": "2.7.0", "resolved": "https://registry.npmjs.org/node-fetch/-/node-fetch-2.7.0.tgz", "integrity": "sha512-c4FRfUm/dbcWZ7U+1Wq0AwCyFL+3nt2bEw05wfxSz+DWpWsitgmSgYmy2dQdWyKC1694ELPqMs/YzUSNozLt8A==", "dependencies": {"whatwg-url": "^5.0.0"}, "engines": {"node": "4.x || >=6.0.0"}, "peerDependencies": {"encoding": "^0.1.0"}, "peerDependenciesMeta": {"encoding": {"optional": true}}}, "node_modules/node-repl-await": {"version": "0.1.2", "license": "MIT", "dependencies": {"acorn": "^8.0.5", "acorn-class-fields": "^1.0.0", "acorn-private-methods": "^1.0.0", "acorn-static-class-features": "^1.0.0", "acorn-walk": "^8.0.2"}}, "node_modules/nodemailer": {"version": "6.9.5", "license": "MIT-0", "engines": {"node": ">=6.0.0"}}, "node_modules/normalize-path": {"version": "3.0.0", "dev": true, "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/normalize-url": {"version": "6.1.0", "license": "MIT", "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/npm-run-path": {"version": "4.0.1", "dev": true, "license": "MIT", "dependencies": {"path-key": "^3.0.0"}, "engines": {"node": ">=8"}}, "node_modules/oauth-sign": {"version": "0.9.0", "resolved": "https://registry.npmjs.org/oauth-sign/-/oauth-sign-0.9.0.tgz", "integrity": "sha512-fexhUFFPTGV8ybAtSIGbV6gOkSv8UtRbDBnAyLQw4QPKkgNlsH2ByPGtMUqdWkos6YCRmAqViwgZrJc/mRDzZQ==", "engines": {"node": "*"}}, "node_modules/object-copy": {"version": "0.1.0", "dev": true, "license": "MIT", "dependencies": {"copy-descriptor": "^0.1.0", "define-property": "^0.2.5", "kind-of": "^3.0.3"}, "engines": {"node": ">=0.10.0"}}, "node_modules/object-copy/node_modules/define-property": {"version": "0.2.5", "dev": true, "license": "MIT", "dependencies": {"is-descriptor": "^0.1.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/object-copy/node_modules/is-accessor-descriptor": {"version": "0.1.6", "dev": true, "license": "MIT", "dependencies": {"kind-of": "^3.0.2"}, "engines": {"node": ">=0.10.0"}}, "node_modules/object-copy/node_modules/is-data-descriptor": {"version": "0.1.4", "dev": true, "license": "MIT", "dependencies": {"kind-of": "^3.0.2"}, "engines": {"node": ">=0.10.0"}}, "node_modules/object-copy/node_modules/is-descriptor": {"version": "0.1.6", "dev": true, "license": "MIT", "dependencies": {"is-accessor-descriptor": "^0.1.6", "is-data-descriptor": "^0.1.4", "kind-of": "^5.0.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/object-copy/node_modules/is-descriptor/node_modules/kind-of": {"version": "5.1.0", "dev": true, "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/object-copy/node_modules/kind-of": {"version": "3.2.2", "dev": true, "license": "MIT", "dependencies": {"is-buffer": "^1.1.5"}, "engines": {"node": ">=0.10.0"}}, "node_modules/object-inspect": {"version": "1.12.3", "license": "MIT", "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/object-visit": {"version": "1.0.1", "dev": true, "license": "MIT", "dependencies": {"isobject": "^3.0.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/object.pick": {"version": "1.3.0", "dev": true, "license": "MIT", "dependencies": {"isobject": "^3.0.1"}, "engines": {"node": ">=0.10.0"}}, "node_modules/on-exit-leak-free": {"version": "2.1.0", "license": "MIT"}, "node_modules/on-finished": {"version": "2.4.1", "license": "MIT", "dependencies": {"ee-first": "1.1.1"}, "engines": {"node": ">= 0.8"}}, "node_modules/once": {"version": "1.4.0", "license": "ISC", "dependencies": {"wrappy": "1"}}, "node_modules/onetime": {"version": "5.1.2", "license": "MIT", "dependencies": {"mimic-fn": "^2.1.0"}, "engines": {"node": ">=6"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/onetime/node_modules/mimic-fn": {"version": "2.1.0", "license": "MIT", "engines": {"node": ">=6"}}, "node_modules/open": {"version": "8.4.2", "dev": true, "license": "MIT", "dependencies": {"define-lazy-prop": "^2.0.0", "is-docker": "^2.1.1", "is-wsl": "^2.2.0"}, "engines": {"node": ">=12"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/openapi-schema-validator": {"version": "3.0.3", "dev": true, "license": "MIT", "dependencies": {"ajv": "^6.5.2", "lodash.merge": "^4.6.1", "openapi-types": "1.3.4", "swagger-schema-official": "2.0.0-bab6bed"}}, "node_modules/openapi-types": {"version": "1.3.4", "dev": true, "license": "MIT"}, "node_modules/optionator": {"version": "0.9.3", "dev": true, "license": "MIT", "dependencies": {"@aashutoshrathi/word-wrap": "^1.2.3", "deep-is": "^0.1.3", "fast-levenshtein": "^2.0.6", "levn": "^0.4.1", "prelude-ls": "^1.2.1", "type-check": "^0.4.0"}, "engines": {"node": ">= 0.8.0"}}, "node_modules/p-all": {"version": "2.1.0", "dev": true, "license": "MIT", "dependencies": {"p-map": "^2.0.0"}, "engines": {"node": ">=6"}}, "node_modules/p-all/node_modules/p-map": {"version": "2.1.0", "dev": true, "license": "MIT", "engines": {"node": ">=6"}}, "node_modules/p-cancelable": {"version": "2.1.1", "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/p-defer": {"version": "1.0.0", "dev": true, "license": "MIT", "engines": {"node": ">=4"}}, "node_modules/p-event": {"version": "4.2.0", "dev": true, "license": "MIT", "dependencies": {"p-timeout": "^3.1.0"}, "engines": {"node": ">=8"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/p-filter": {"version": "2.1.0", "dev": true, "license": "MIT", "dependencies": {"p-map": "^2.0.0"}, "engines": {"node": ">=8"}}, "node_modules/p-filter/node_modules/p-map": {"version": "2.1.0", "dev": true, "license": "MIT", "engines": {"node": ">=6"}}, "node_modules/p-finally": {"version": "1.0.0", "dev": true, "license": "MIT", "engines": {"node": ">=4"}}, "node_modules/p-limit": {"version": "3.1.0", "dev": true, "license": "MIT", "dependencies": {"yocto-queue": "^0.1.0"}, "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/p-locate": {"version": "5.0.0", "dev": true, "license": "MIT", "dependencies": {"p-limit": "^3.0.2"}, "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/p-map": {"version": "3.0.0", "dev": true, "license": "MIT", "dependencies": {"aggregate-error": "^3.0.0"}, "engines": {"node": ">=8"}}, "node_modules/p-timeout": {"version": "3.2.0", "dev": true, "license": "MIT", "dependencies": {"p-finally": "^1.0.0"}, "engines": {"node": ">=8"}}, "node_modules/p-try": {"version": "2.2.0", "dev": true, "license": "MIT", "engines": {"node": ">=6"}}, "node_modules/param-case": {"version": "3.0.4", "license": "MIT", "dependencies": {"dot-case": "^3.0.4", "tslib": "^2.0.3"}}, "node_modules/parent-module": {"version": "1.0.1", "dev": true, "license": "MIT", "dependencies": {"callsites": "^3.0.0"}, "engines": {"node": ">=6"}}, "node_modules/parse-imports": {"version": "0.0.5", "license": "Apache 2.0", "dependencies": {"es-module-lexer": "0.3.26", "slashes": "2.0.2"}, "engines": {"node": ">= 10"}}, "node_modules/parseurl": {"version": "1.3.3", "license": "MIT", "engines": {"node": ">= 0.8"}}, "node_modules/pascal-case": {"version": "3.1.2", "license": "MIT", "dependencies": {"no-case": "^3.0.4", "tslib": "^2.0.3"}}, "node_modules/pascalcase": {"version": "0.1.1", "dev": true, "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/path-case": {"version": "3.0.4", "license": "MIT", "dependencies": {"dot-case": "^3.0.4", "tslib": "^2.0.3"}}, "node_modules/path-dirname": {"version": "1.0.2", "dev": true, "license": "MIT"}, "node_modules/path-exists": {"version": "4.0.0", "dev": true, "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/path-is-absolute": {"version": "1.0.1", "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/path-key": {"version": "3.1.1", "dev": true, "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/path-parse": {"version": "1.0.7", "license": "MIT"}, "node_modules/path-to-regexp": {"version": "0.1.7", "resolved": "https://registry.npmjs.org/path-to-regexp/-/path-to-regexp-0.1.7.tgz", "integrity": "sha512-5DFkuoqlv1uYQKxy8omFBeJPQcdoE07Kv2sferDCrAq1ohOU+MSDswDIbnx3YAM60qIOnYa53wBhXW0EbMonrQ=="}, "node_modules/path-type": {"version": "3.0.0", "dev": true, "license": "MIT", "dependencies": {"pify": "^3.0.0"}, "engines": {"node": ">=4"}}, "node_modules/path-type/node_modules/pify": {"version": "3.0.0", "dev": true, "license": "MIT", "engines": {"node": ">=4"}}, "node_modules/pathval": {"version": "1.1.1", "dev": true, "license": "MIT", "engines": {"node": "*"}}, "node_modules/peek-readable": {"version": "4.1.0", "license": "MIT", "engines": {"node": ">=8"}, "funding": {"type": "github", "url": "https://github.com/sponsors/Borewit"}}, "node_modules/performance-now": {"version": "2.1.0", "resolved": "https://registry.npmjs.org/performance-now/-/performance-now-2.1.0.tgz", "integrity": "sha512-7EAHlyLHI56VEIdK57uwHdHKIaAGbnXPiw0yWbarQZOKaKpvUIgW0jWRVLiatnM+XXlSwsanIBH/hzGMJulMow=="}, "node_modules/pg-connection-string": {"version": "2.6.1", "license": "MIT"}, "node_modules/picomatch": {"version": "2.3.1", "dev": true, "license": "MIT", "engines": {"node": ">=8.6"}, "funding": {"url": "https://github.com/sponsors/jonschlinkert"}}, "node_modules/pify": {"version": "4.0.1", "dev": true, "license": "MIT", "engines": {"node": ">=6"}}, "node_modules/pino": {"version": "6.14.0", "license": "MIT", "dependencies": {"fast-redact": "^3.0.0", "fast-safe-stringify": "^2.0.8", "flatstr": "^1.0.12", "pino-std-serializers": "^3.1.0", "process-warning": "^1.0.0", "quick-format-unescaped": "^4.0.3", "sonic-boom": "^1.0.2"}, "bin": {"pino": "bin.js"}}, "node_modules/pino-abstract-transport": {"version": "1.1.0", "license": "MIT", "dependencies": {"readable-stream": "^4.0.0", "split2": "^4.0.0"}}, "node_modules/pino-pretty": {"version": "10.2.0", "license": "MIT", "dependencies": {"colorette": "^2.0.7", "dateformat": "^4.6.3", "fast-copy": "^3.0.0", "fast-safe-stringify": "^2.1.1", "help-me": "^4.0.1", "joycon": "^3.1.1", "minimist": "^1.2.6", "on-exit-leak-free": "^2.1.0", "pino-abstract-transport": "^1.0.0", "pump": "^3.0.0", "readable-stream": "^4.0.0", "secure-json-parse": "^2.4.0", "sonic-boom": "^3.0.0", "strip-json-comments": "^3.1.1"}, "bin": {"pino-pretty": "bin.js"}}, "node_modules/pino-pretty/node_modules/sonic-boom": {"version": "3.3.0", "license": "MIT", "dependencies": {"atomic-sleep": "^1.0.0"}}, "node_modules/pino-std-serializers": {"version": "6.2.2", "license": "MIT"}, "node_modules/pino/node_modules/pino-std-serializers": {"version": "3.2.0", "license": "MIT"}, "node_modules/pino/node_modules/sonic-boom": {"version": "1.4.1", "license": "MIT", "dependencies": {"atomic-sleep": "^1.0.0", "flatstr": "^1.0.12"}}, "node_modules/pirates": {"version": "4.0.6", "dev": true, "license": "MIT", "engines": {"node": ">= 6"}}, "node_modules/pkg-dir": {"version": "4.2.0", "dev": true, "license": "MIT", "dependencies": {"find-up": "^4.0.0"}, "engines": {"node": ">=8"}}, "node_modules/pkg-dir/node_modules/find-up": {"version": "4.1.0", "dev": true, "license": "MIT", "dependencies": {"locate-path": "^5.0.0", "path-exists": "^4.0.0"}, "engines": {"node": ">=8"}}, "node_modules/pkg-dir/node_modules/locate-path": {"version": "5.0.0", "dev": true, "license": "MIT", "dependencies": {"p-locate": "^4.1.0"}, "engines": {"node": ">=8"}}, "node_modules/pkg-dir/node_modules/p-limit": {"version": "2.3.0", "dev": true, "license": "MIT", "dependencies": {"p-try": "^2.0.0"}, "engines": {"node": ">=6"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/pkg-dir/node_modules/p-locate": {"version": "4.1.0", "dev": true, "license": "MIT", "dependencies": {"p-limit": "^2.2.0"}, "engines": {"node": ">=8"}}, "node_modules/pluralize": {"version": "8.0.0", "license": "MIT", "engines": {"node": ">=4"}}, "node_modules/posix-character-classes": {"version": "0.1.1", "dev": true, "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/prelude-ls": {"version": "1.2.1", "dev": true, "license": "MIT", "engines": {"node": ">= 0.8.0"}}, "node_modules/prettier": {"version": "2.8.8", "dev": true, "license": "MIT", "bin": {"prettier": "bin-prettier.js"}, "engines": {"node": ">=10.13.0"}, "funding": {"url": "https://github.com/prettier/prettier?sponsor=1"}}, "node_modules/prettier-linter-helpers": {"version": "1.0.0", "dev": true, "license": "MIT", "dependencies": {"fast-diff": "^1.1.2"}, "engines": {"node": ">=6.0.0"}}, "node_modules/pretty-format": {"version": "29.7.0", "dev": true, "license": "MIT", "dependencies": {"@jest/schemas": "^29.6.3", "ansi-styles": "^5.0.0", "react-is": "^18.0.0"}, "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}}, "node_modules/pretty-format/node_modules/ansi-styles": {"version": "5.2.0", "dev": true, "license": "MIT", "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/chalk/ansi-styles?sponsor=1"}}, "node_modules/pretty-hrtime": {"version": "1.0.3", "license": "MIT", "engines": {"node": ">= 0.8"}}, "node_modules/printable-characters": {"version": "1.0.42", "dev": true, "license": "Unlicense"}, "node_modules/process": {"version": "0.11.10", "license": "MIT", "engines": {"node": ">= 0.6.0"}}, "node_modules/process-warning": {"version": "1.0.0", "license": "MIT"}, "node_modules/prop-ini": {"version": "0.0.2", "dev": true, "license": "MIT", "dependencies": {"extend": "^3.0.0"}}, "node_modules/proxy-addr": {"version": "2.0.7", "license": "MIT", "dependencies": {"forwarded": "0.2.0", "ipaddr.js": "1.9.1"}, "engines": {"node": ">= 0.10"}}, "node_modules/pseudomap": {"version": "1.0.2", "dev": true, "license": "ISC"}, "node_modules/psl": {"version": "1.9.0", "resolved": "https://registry.npmjs.org/psl/-/psl-1.9.0.tgz", "integrity": "sha512-E/ZsdU4HLs/68gYzgGTkMicWTLPdAftJLfJFlLUAAKZGkStNU72sZjT66SnMDVOfOWY/YAoiD7Jxa9iHvngcag=="}, "node_modules/pump": {"version": "3.0.0", "license": "MIT", "dependencies": {"end-of-stream": "^1.1.0", "once": "^1.3.1"}}, "node_modules/punycode": {"version": "2.3.0", "license": "MIT", "engines": {"node": ">=6"}}, "node_modules/qs": {"version": "6.11.2", "license": "BSD-3-<PERSON><PERSON>", "dependencies": {"side-channel": "^1.0.4"}, "engines": {"node": ">=0.6"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/querystringify": {"version": "2.2.0", "resolved": "https://registry.npmjs.org/querystringify/-/querystringify-2.2.0.tgz", "integrity": "sha512-FIqgj2EUvTa7R50u0rGsyTftzjYmv/a3hO345bZNrqabNqjtgiDMgmo4mkUjd+nzU5oF3dClKqFIPUKybUyqoQ=="}, "node_modules/queue-microtask": {"version": "1.2.3", "dev": true, "funding": [{"type": "github", "url": "https://github.com/sponsors/feross"}, {"type": "patreon", "url": "https://www.patreon.com/feross"}, {"type": "consulting", "url": "https://feross.org/support"}], "license": "MIT"}, "node_modules/quick-format-unescaped": {"version": "4.0.4", "license": "MIT"}, "node_modules/quick-lru": {"version": "5.1.1", "license": "MIT", "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/random-bytes": {"version": "1.0.0", "license": "MIT", "engines": {"node": ">= 0.8"}}, "node_modules/range-parser": {"version": "1.2.1", "license": "MIT", "engines": {"node": ">= 0.6"}}, "node_modules/raw-body": {"version": "2.5.2", "license": "MIT", "dependencies": {"bytes": "3.1.2", "http-errors": "2.0.0", "iconv-lite": "0.4.24", "unpipe": "1.0.0"}, "engines": {"node": ">= 0.8"}}, "node_modules/raw-body/node_modules/iconv-lite": {"version": "0.4.24", "license": "MIT", "dependencies": {"safer-buffer": ">= 2.1.2 < 3"}, "engines": {"node": ">=0.10.0"}}, "node_modules/rc": {"version": "1.2.8", "dev": true, "license": "(BSD-2-<PERSON><PERSON> OR MIT OR Apache-2.0)", "dependencies": {"deep-extend": "^0.6.0", "ini": "~1.3.0", "minimist": "^1.2.0", "strip-json-comments": "~2.0.1"}, "bin": {"rc": "cli.js"}}, "node_modules/rc/node_modules/strip-json-comments": {"version": "2.0.1", "dev": true, "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/react-is": {"version": "18.2.0", "dev": true, "license": "MIT"}, "node_modules/readable-stream": {"version": "4.4.2", "license": "MIT", "dependencies": {"abort-controller": "^3.0.0", "buffer": "^6.0.3", "events": "^3.3.0", "process": "^0.11.10", "string_decoder": "^1.3.0"}, "engines": {"node": "^12.22.0 || ^14.17.0 || >=16.0.0"}}, "node_modules/readable-stream/node_modules/buffer": {"version": "6.0.3", "funding": [{"type": "github", "url": "https://github.com/sponsors/feross"}, {"type": "patreon", "url": "https://www.patreon.com/feross"}, {"type": "consulting", "url": "https://feross.org/support"}], "license": "MIT", "dependencies": {"base64-js": "^1.3.1", "ieee754": "^1.2.1"}}, "node_modules/readable-web-to-node-stream": {"version": "3.0.2", "license": "MIT", "dependencies": {"readable-stream": "^3.6.0"}, "engines": {"node": ">=8"}, "funding": {"type": "github", "url": "https://github.com/sponsors/Borewit"}}, "node_modules/readable-web-to-node-stream/node_modules/readable-stream": {"version": "3.6.2", "license": "MIT", "dependencies": {"inherits": "^2.0.3", "string_decoder": "^1.1.1", "util-deprecate": "^1.0.1"}, "engines": {"node": ">= 6"}}, "node_modules/readdirp": {"version": "3.6.0", "dev": true, "license": "MIT", "dependencies": {"picomatch": "^2.2.1"}, "engines": {"node": ">=8.10.0"}}, "node_modules/readme-badger": {"version": "0.3.0", "dev": true, "license": "MIT", "dependencies": {"balanced-match": "^1.0.0"}}, "node_modules/rechoir": {"version": "0.8.0", "license": "MIT", "dependencies": {"resolve": "^1.20.0"}, "engines": {"node": ">= 10.13.0"}}, "node_modules/redeyed": {"version": "2.1.1", "resolved": "https://registry.npmjs.org/redeyed/-/redeyed-2.1.1.tgz", "integrity": "sha512-FNpGGo1DycYAdnrKFxCMmKYgo/mILAqtRYbkdQD8Ep/Hk2PQ5+aEAEx+IU713RTDmuBaH0c8P5ZozurNu5ObRQ==", "dev": true, "dependencies": {"esprima": "~4.0.0"}}, "node_modules/reflect-metadata": {"version": "0.1.13", "license": "Apache-2.0"}, "node_modules/regenerator-runtime": {"version": "0.14.0", "license": "MIT"}, "node_modules/regex-not": {"version": "1.0.2", "dev": true, "license": "MIT", "dependencies": {"extend-shallow": "^3.0.2", "safe-regex": "^1.1.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/repeat-element": {"version": "1.1.4", "dev": true, "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/repeat-string": {"version": "1.6.1", "dev": true, "license": "MIT", "engines": {"node": ">=0.10"}}, "node_modules/request": {"version": "2.88.2", "resolved": "https://registry.npmjs.org/request/-/request-2.88.2.tgz", "integrity": "sha512-MsvtOrfG9ZcrOwAW+Qi+F6HbD0CWXEh9ou77uOb7FM2WPhwT7smM833PzanhJLsgXjN89Ir6V2PczXNnMpwKhw==", "deprecated": "request has been deprecated, see https://github.com/request/request/issues/3142", "dependencies": {"aws-sign2": "~0.7.0", "aws4": "^1.8.0", "caseless": "~0.12.0", "combined-stream": "~1.0.6", "extend": "~3.0.2", "forever-agent": "~0.6.1", "form-data": "~2.3.2", "har-validator": "~5.1.3", "http-signature": "~1.2.0", "is-typedarray": "~1.0.0", "isstream": "~0.1.2", "json-stringify-safe": "~5.0.1", "mime-types": "~2.1.19", "oauth-sign": "~0.9.0", "performance-now": "^2.1.0", "qs": "~6.5.2", "safe-buffer": "^5.1.2", "tough-cookie": "~2.5.0", "tunnel-agent": "^0.6.0", "uuid": "^3.3.2"}, "engines": {"node": ">= 6"}}, "node_modules/request-promise": {"version": "4.2.6", "resolved": "https://registry.npmjs.org/request-promise/-/request-promise-4.2.6.tgz", "integrity": "sha512-HCHI3DJJUakkOr8fNoCc73E5nU5bqITjOYFMDrKHYOXWXrgD/SBaC7LjwuPymUprRyuF06UK7hd/lMHkmUXglQ==", "deprecated": "request-promise has been deprecated because it extends the now deprecated request package, see https://github.com/request/request/issues/3142", "dependencies": {"bluebird": "^3.5.0", "request-promise-core": "1.1.4", "stealthy-require": "^1.1.1", "tough-cookie": "^2.3.3"}, "engines": {"node": ">=0.10.0"}, "peerDependencies": {"request": "^2.34"}}, "node_modules/request-promise-core": {"version": "1.1.4", "resolved": "https://registry.npmjs.org/request-promise-core/-/request-promise-core-1.1.4.tgz", "integrity": "sha512-TTbAfBBRdWD7aNNOoVOBH4pN/KigV6LyapYNNlAPA8JwbovRti1E88m3sYAwsLi5ryhPKsE9APwnjFTgdUjTpw==", "dependencies": {"lodash": "^4.17.19"}, "engines": {"node": ">=0.10.0"}, "peerDependencies": {"request": "^2.34"}}, "node_modules/request/node_modules/form-data": {"version": "2.3.3", "resolved": "https://registry.npmjs.org/form-data/-/form-data-2.3.3.tgz", "integrity": "sha512-1lLKB2Mu3aGP1Q/2eCOx0fNbRMe7XdwktwOruhfqqd0rIJWwN4Dh+E3hrPSlDCXnSR7UtZ1N38rVXm+6+MEhJQ==", "dependencies": {"asynckit": "^0.4.0", "combined-stream": "^1.0.6", "mime-types": "^2.1.12"}, "engines": {"node": ">= 0.12"}}, "node_modules/request/node_modules/qs": {"version": "6.5.3", "resolved": "https://registry.npmjs.org/qs/-/qs-6.5.3.tgz", "integrity": "sha512-qxXIEh4pCGfHICj1mAJQ2/2XVZkjCDTcEgfoSQxc/fYivUZxTkk7L3bDBJSoNrEzXI17oUO5Dp07ktqE5KzczA==", "engines": {"node": ">=0.6"}}, "node_modules/request/node_modules/uuid": {"version": "3.4.0", "resolved": "https://registry.npmjs.org/uuid/-/uuid-3.4.0.tgz", "integrity": "sha512-HjSDRw6gZE5JMggctHBcjVak08+KEVhSIiDzFnT9S9aegmp85S/bReBVTb4QTFaRNptJ9kuYaNhnbNEOkbKb/A==", "deprecated": "Please upgrade  to version 7 or higher.  Older versions may use Math.random() in certain circumstances, which is known to be problematic.  See https://v8.dev/blog/math-random for details.", "bin": {"uuid": "bin/uuid"}}, "node_modules/require-all": {"version": "3.0.0", "license": "MIT", "engines": {"node": ">= 0.8"}}, "node_modules/requires-port": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/requires-port/-/requires-port-1.0.0.tgz", "integrity": "sha512-KigOCHcocU3XODJxsu8i/j8T9tzT4adHiecwORRQ0ZZFcp7ahwXuRU1m+yuO90C5ZUyGeGfocHDI14M3L3yDAQ=="}, "node_modules/resolve": {"version": "1.22.4", "license": "MIT", "dependencies": {"is-core-module": "^2.13.0", "path-parse": "^1.0.7", "supports-preserve-symlinks-flag": "^1.0.0"}, "bin": {"resolve": "bin/resolve"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/resolve-alpn": {"version": "1.2.1", "license": "MIT"}, "node_modules/resolve-from": {"version": "5.0.0", "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/resolve-url": {"version": "0.2.1", "dev": true, "license": "MIT"}, "node_modules/responselike": {"version": "2.0.1", "license": "MIT", "dependencies": {"lowercase-keys": "^2.0.0"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/restore-cursor": {"version": "3.1.0", "license": "MIT", "dependencies": {"onetime": "^5.1.0", "signal-exit": "^3.0.2"}, "engines": {"node": ">=8"}}, "node_modules/ret": {"version": "0.1.15", "dev": true, "license": "MIT", "engines": {"node": ">=0.12"}}, "node_modules/retry": {"version": "0.13.1", "dev": true, "license": "MIT", "engines": {"node": ">= 4"}}, "node_modules/reusify": {"version": "1.0.4", "license": "MIT", "engines": {"iojs": ">=1.0.0", "node": ">=0.10.0"}}, "node_modules/rev-hash": {"version": "3.0.0", "dev": true, "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/rimraf": {"version": "3.0.2", "dev": true, "license": "ISC", "dependencies": {"glob": "^7.1.3"}, "bin": {"rimraf": "bin.js"}, "funding": {"url": "https://github.com/sponsors/isaacs"}}, "node_modules/run-parallel": {"version": "1.2.0", "dev": true, "funding": [{"type": "github", "url": "https://github.com/sponsors/feross"}, {"type": "patreon", "url": "https://www.patreon.com/feross"}, {"type": "consulting", "url": "https://feross.org/support"}], "license": "MIT", "dependencies": {"queue-microtask": "^1.2.2"}}, "node_modules/safe-buffer": {"version": "5.2.1", "funding": [{"type": "github", "url": "https://github.com/sponsors/feross"}, {"type": "patreon", "url": "https://www.patreon.com/feross"}, {"type": "consulting", "url": "https://feross.org/support"}], "license": "MIT"}, "node_modules/safe-regex": {"version": "1.1.0", "dev": true, "license": "MIT", "dependencies": {"ret": "~0.1.10"}}, "node_modules/safer-buffer": {"version": "2.1.2", "license": "MIT"}, "node_modules/scmp": {"version": "2.1.0", "resolved": "https://registry.npmjs.org/scmp/-/scmp-2.1.0.tgz", "integrity": "sha512-o/mRQGk9Rcer/jEEw/yw4mwo3EU/NvYvp577/Btqrym9Qy5/MdWGBqipbALgd2lrdWTJ5/gqDusxfnQBxOxT2Q=="}, "node_modules/secure-json-parse": {"version": "2.7.0", "license": "BSD-3-<PERSON><PERSON>"}, "node_modules/semver": {"version": "7.5.4", "license": "ISC", "dependencies": {"lru-cache": "^6.0.0"}, "bin": {"semver": "bin/semver.js"}, "engines": {"node": ">=10"}}, "node_modules/semver/node_modules/lru-cache": {"version": "6.0.0", "license": "ISC", "dependencies": {"yallist": "^4.0.0"}, "engines": {"node": ">=10"}}, "node_modules/semver/node_modules/yallist": {"version": "4.0.0", "license": "ISC"}, "node_modules/send": {"version": "0.18.0", "license": "MIT", "dependencies": {"debug": "2.6.9", "depd": "2.0.0", "destroy": "1.2.0", "encodeurl": "~1.0.2", "escape-html": "~1.0.3", "etag": "~1.8.1", "fresh": "0.5.2", "http-errors": "2.0.0", "mime": "1.6.0", "ms": "2.1.3", "on-finished": "2.4.1", "range-parser": "~1.2.1", "statuses": "2.0.1"}, "engines": {"node": ">= 0.8.0"}}, "node_modules/send/node_modules/debug": {"version": "2.6.9", "license": "MIT", "dependencies": {"ms": "2.0.0"}}, "node_modules/send/node_modules/debug/node_modules/ms": {"version": "2.0.0", "license": "MIT"}, "node_modules/sentence-case": {"version": "3.0.4", "license": "MIT", "dependencies": {"no-case": "^3.0.4", "tslib": "^2.0.3", "upper-case-first": "^2.0.2"}}, "node_modules/seq-queue": {"version": "0.0.5"}, "node_modules/serve-static": {"version": "1.15.0", "license": "MIT", "dependencies": {"encodeurl": "~1.0.2", "escape-html": "~1.0.3", "parseurl": "~1.3.3", "send": "0.18.0"}, "engines": {"node": ">= 0.8.0"}}, "node_modules/set-cookie-parser": {"version": "2.6.0", "dev": true, "license": "MIT"}, "node_modules/set-value": {"version": "2.0.1", "dev": true, "license": "MIT", "dependencies": {"extend-shallow": "^2.0.1", "is-extendable": "^0.1.1", "is-plain-object": "^2.0.3", "split-string": "^3.0.1"}, "engines": {"node": ">=0.10.0"}}, "node_modules/set-value/node_modules/extend-shallow": {"version": "2.0.1", "dev": true, "license": "MIT", "dependencies": {"is-extendable": "^0.1.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/set-value/node_modules/is-extendable": {"version": "0.1.1", "dev": true, "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/setprototypeof": {"version": "1.2.0", "license": "ISC"}, "node_modules/shallow-clone": {"version": "3.0.1", "dev": true, "license": "MIT", "dependencies": {"kind-of": "^6.0.2"}, "engines": {"node": ">=8"}}, "node_modules/shebang-command": {"version": "2.0.0", "dev": true, "license": "MIT", "dependencies": {"shebang-regex": "^3.0.0"}, "engines": {"node": ">=8"}}, "node_modules/shebang-regex": {"version": "3.0.0", "dev": true, "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/side-channel": {"version": "1.0.4", "license": "MIT", "dependencies": {"call-bind": "^1.0.0", "get-intrinsic": "^1.0.2", "object-inspect": "^1.9.0"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/sigmund": {"version": "1.0.1", "dev": true, "license": "ISC"}, "node_modules/signal-exit": {"version": "3.0.7", "license": "ISC"}, "node_modules/slash": {"version": "3.0.0", "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/slashes": {"version": "2.0.2", "license": "ISC"}, "node_modules/slice-ansi": {"version": "4.0.0", "license": "MIT", "dependencies": {"ansi-styles": "^4.0.0", "astral-regex": "^2.0.0", "is-fullwidth-code-point": "^3.0.0"}, "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/chalk/slice-ansi?sponsor=1"}}, "node_modules/slugify": {"version": "1.6.6", "license": "MIT", "engines": {"node": ">=8.0.0"}}, "node_modules/smpltmpl": {"version": "1.0.2", "dev": true, "license": "MIT", "dependencies": {"babel-code-frame": "^6.26.0"}, "engines": {"node": ">=4"}}, "node_modules/snake-case": {"version": "3.0.4", "license": "MIT", "dependencies": {"dot-case": "^3.0.4", "tslib": "^2.0.3"}}, "node_modules/snapdragon": {"version": "0.8.2", "dev": true, "license": "MIT", "dependencies": {"base": "^0.11.1", "debug": "^2.2.0", "define-property": "^0.2.5", "extend-shallow": "^2.0.1", "map-cache": "^0.2.2", "source-map": "^0.5.6", "source-map-resolve": "^0.5.0", "use": "^3.1.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/snapdragon-node": {"version": "2.1.1", "dev": true, "license": "MIT", "dependencies": {"define-property": "^1.0.0", "isobject": "^3.0.0", "snapdragon-util": "^3.0.1"}, "engines": {"node": ">=0.10.0"}}, "node_modules/snapdragon-node/node_modules/define-property": {"version": "1.0.0", "dev": true, "license": "MIT", "dependencies": {"is-descriptor": "^1.0.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/snapdragon-util": {"version": "3.0.1", "dev": true, "license": "MIT", "dependencies": {"kind-of": "^3.2.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/snapdragon-util/node_modules/kind-of": {"version": "3.2.2", "dev": true, "license": "MIT", "dependencies": {"is-buffer": "^1.1.5"}, "engines": {"node": ">=0.10.0"}}, "node_modules/snapdragon/node_modules/debug": {"version": "2.6.9", "dev": true, "license": "MIT", "dependencies": {"ms": "2.0.0"}}, "node_modules/snapdragon/node_modules/define-property": {"version": "0.2.5", "dev": true, "license": "MIT", "dependencies": {"is-descriptor": "^0.1.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/snapdragon/node_modules/extend-shallow": {"version": "2.0.1", "dev": true, "license": "MIT", "dependencies": {"is-extendable": "^0.1.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/snapdragon/node_modules/is-accessor-descriptor": {"version": "0.1.6", "dev": true, "license": "MIT", "dependencies": {"kind-of": "^3.0.2"}, "engines": {"node": ">=0.10.0"}}, "node_modules/snapdragon/node_modules/is-accessor-descriptor/node_modules/kind-of": {"version": "3.2.2", "dev": true, "license": "MIT", "dependencies": {"is-buffer": "^1.1.5"}, "engines": {"node": ">=0.10.0"}}, "node_modules/snapdragon/node_modules/is-data-descriptor": {"version": "0.1.4", "dev": true, "license": "MIT", "dependencies": {"kind-of": "^3.0.2"}, "engines": {"node": ">=0.10.0"}}, "node_modules/snapdragon/node_modules/is-data-descriptor/node_modules/kind-of": {"version": "3.2.2", "dev": true, "license": "MIT", "dependencies": {"is-buffer": "^1.1.5"}, "engines": {"node": ">=0.10.0"}}, "node_modules/snapdragon/node_modules/is-descriptor": {"version": "0.1.6", "dev": true, "license": "MIT", "dependencies": {"is-accessor-descriptor": "^0.1.6", "is-data-descriptor": "^0.1.4", "kind-of": "^5.0.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/snapdragon/node_modules/is-extendable": {"version": "0.1.1", "dev": true, "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/snapdragon/node_modules/kind-of": {"version": "5.1.0", "dev": true, "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/snapdragon/node_modules/ms": {"version": "2.0.0", "dev": true, "license": "MIT"}, "node_modules/snapdragon/node_modules/source-map": {"version": "0.5.7", "dev": true, "license": "BSD-3-<PERSON><PERSON>", "engines": {"node": ">=0.10.0"}}, "node_modules/sonic-boom": {"version": "2.8.0", "license": "MIT", "dependencies": {"atomic-sleep": "^1.0.0"}}, "node_modules/source-map": {"version": "0.6.1", "license": "BSD-3-<PERSON><PERSON>", "engines": {"node": ">=0.10.0"}}, "node_modules/source-map-resolve": {"version": "0.5.3", "dev": true, "license": "MIT", "dependencies": {"atob": "^2.1.2", "decode-uri-component": "^0.2.0", "resolve-url": "^0.2.1", "source-map-url": "^0.4.0", "urix": "^0.1.0"}}, "node_modules/source-map-support": {"version": "0.5.21", "license": "MIT", "dependencies": {"buffer-from": "^1.0.0", "source-map": "^0.6.0"}}, "node_modules/source-map-url": {"version": "0.4.1", "dev": true, "license": "MIT"}, "node_modules/split-lines": {"version": "2.1.0", "dev": true, "license": "MIT", "engines": {"node": ">=6"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/split-string": {"version": "3.1.0", "dev": true, "license": "MIT", "dependencies": {"extend-shallow": "^3.0.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/split2": {"version": "4.2.0", "license": "ISC", "engines": {"node": ">= 10.x"}}, "node_modules/sprintf-js": {"version": "1.0.3", "dev": true, "license": "BSD-3-<PERSON><PERSON>"}, "node_modules/sqlstring": {"version": "2.3.3", "license": "MIT", "engines": {"node": ">= 0.6"}}, "node_modules/sshpk": {"version": "1.17.0", "resolved": "https://registry.npmjs.org/sshpk/-/sshpk-1.17.0.tgz", "integrity": "sha512-/9HIEs1ZXGhSPE8X6Ccm7Nam1z8KcoCqPdI7ecm1N33EzAetWahvQWVqLZtaZQ+IDKX4IyA2o0gBzqIMkAagHQ==", "dependencies": {"asn1": "~0.2.3", "assert-plus": "^1.0.0", "bcrypt-pbkdf": "^1.0.0", "dashdash": "^1.12.0", "ecc-jsbn": "~0.1.1", "getpass": "^0.1.1", "jsbn": "~0.1.0", "safer-buffer": "^2.0.2", "tweetnacl": "~0.14.0"}, "bin": {"sshpk-conv": "bin/sshpk-conv", "sshpk-sign": "bin/sshpk-sign", "sshpk-verify": "bin/sshpk-verify"}, "engines": {"node": ">=0.10.0"}}, "node_modules/stacktracey": {"version": "2.1.8", "dev": true, "license": "Unlicense", "dependencies": {"as-table": "^1.0.36", "get-source": "^2.0.12"}}, "node_modules/static-extend": {"version": "0.1.2", "dev": true, "license": "MIT", "dependencies": {"define-property": "^0.2.5", "object-copy": "^0.1.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/static-extend/node_modules/define-property": {"version": "0.2.5", "dev": true, "license": "MIT", "dependencies": {"is-descriptor": "^0.1.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/static-extend/node_modules/is-accessor-descriptor": {"version": "0.1.6", "dev": true, "license": "MIT", "dependencies": {"kind-of": "^3.0.2"}, "engines": {"node": ">=0.10.0"}}, "node_modules/static-extend/node_modules/is-accessor-descriptor/node_modules/kind-of": {"version": "3.2.2", "dev": true, "license": "MIT", "dependencies": {"is-buffer": "^1.1.5"}, "engines": {"node": ">=0.10.0"}}, "node_modules/static-extend/node_modules/is-data-descriptor": {"version": "0.1.4", "dev": true, "license": "MIT", "dependencies": {"kind-of": "^3.0.2"}, "engines": {"node": ">=0.10.0"}}, "node_modules/static-extend/node_modules/is-data-descriptor/node_modules/kind-of": {"version": "3.2.2", "dev": true, "license": "MIT", "dependencies": {"is-buffer": "^1.1.5"}, "engines": {"node": ">=0.10.0"}}, "node_modules/static-extend/node_modules/is-descriptor": {"version": "0.1.6", "dev": true, "license": "MIT", "dependencies": {"is-accessor-descriptor": "^0.1.6", "is-data-descriptor": "^0.1.4", "kind-of": "^5.0.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/static-extend/node_modules/kind-of": {"version": "5.1.0", "dev": true, "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/statuses": {"version": "2.0.1", "license": "MIT", "engines": {"node": ">= 0.8"}}, "node_modules/stealthy-require": {"version": "1.1.1", "resolved": "https://registry.npmjs.org/stealthy-require/-/stealthy-require-1.1.1.tgz", "integrity": "sha512-ZnWpYnYugiOVEY5GkcuJK1io5V8QmNYChG62gSit9pQVGErXtrKuPC55ITaVSukmMta5qpMU7vqLt2Lnni4f/g==", "engines": {"node": ">=0.10.0"}}, "node_modules/stream-browserify": {"version": "3.0.0", "license": "MIT", "dependencies": {"inherits": "~2.0.4", "readable-stream": "^3.5.0"}}, "node_modules/stream-browserify/node_modules/readable-stream": {"version": "3.6.2", "license": "MIT", "dependencies": {"inherits": "^2.0.3", "string_decoder": "^1.1.1", "util-deprecate": "^1.0.1"}, "engines": {"node": ">= 6"}}, "node_modules/string_decoder": {"version": "1.3.0", "license": "MIT", "dependencies": {"safe-buffer": "~5.2.0"}}, "node_modules/string-width": {"version": "4.2.3", "license": "MIT", "dependencies": {"emoji-regex": "^8.0.0", "is-fullwidth-code-point": "^3.0.0", "strip-ansi": "^6.0.1"}, "engines": {"node": ">=8"}}, "node_modules/stringify-attributes": {"version": "2.0.0", "license": "MIT", "dependencies": {"escape-goat": "^2.0.0"}, "engines": {"node": ">=8"}}, "node_modules/strip-ansi": {"version": "6.0.1", "license": "MIT", "dependencies": {"ansi-regex": "^5.0.1"}, "engines": {"node": ">=8"}}, "node_modules/strip-bom": {"version": "4.0.0", "dev": true, "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/strip-final-newline": {"version": "2.0.0", "dev": true, "license": "MIT", "engines": {"node": ">=6"}}, "node_modules/strip-json-comments": {"version": "3.1.1", "license": "MIT", "engines": {"node": ">=8"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/strnum": {"version": "1.0.5", "license": "MIT"}, "node_modules/strtok3": {"version": "6.3.0", "license": "MIT", "dependencies": {"@tokenizer/token": "^0.3.0", "peek-readable": "^4.1.0"}, "engines": {"node": ">=10"}, "funding": {"type": "github", "url": "https://github.com/sponsors/Borewit"}}, "node_modules/superagent": {"version": "8.1.2", "dev": true, "license": "MIT", "dependencies": {"component-emitter": "^1.3.0", "cookiejar": "^2.1.4", "debug": "^4.3.4", "fast-safe-stringify": "^2.1.1", "form-data": "^4.0.0", "formidable": "^2.1.2", "methods": "^1.1.2", "mime": "2.6.0", "qs": "^6.11.0", "semver": "^7.3.8"}, "engines": {"node": ">=6.4.0 <13 || >=14"}}, "node_modules/superagent/node_modules/mime": {"version": "2.6.0", "dev": true, "license": "MIT", "bin": {"mime": "cli.js"}, "engines": {"node": ">=4.0.0"}}, "node_modules/supports-color": {"version": "7.2.0", "dev": true, "license": "MIT", "dependencies": {"has-flag": "^4.0.0"}, "engines": {"node": ">=8"}}, "node_modules/supports-hyperlinks": {"version": "2.3.0", "resolved": "https://registry.npmjs.org/supports-hyperlinks/-/supports-hyperlinks-2.3.0.tgz", "integrity": "sha512-RpsAZlpWcDwOPQA22aCH4J0t7L8JmAvsCxfOSEwm7cQs3LshN36QaTkwd70DnBOXDWGssw2eUoc8CaRWT0XunA==", "dev": true, "dependencies": {"has-flag": "^4.0.0", "supports-color": "^7.0.0"}, "engines": {"node": ">=8"}}, "node_modules/supports-preserve-symlinks-flag": {"version": "1.0.0", "license": "MIT", "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/swagger-jsdoc": {"version": "6.2.8", "resolved": "https://registry.npmjs.org/swagger-jsdoc/-/swagger-jsdoc-6.2.8.tgz", "integrity": "sha512-VPvil1+JRpmJ55CgAtn8DIcpBs0bL5L3q5bVQvF4tAW/k/9JYSj7dCpaYCAv5rufe0vcCbBRQXGvzpkWjvLklQ==", "dependencies": {"commander": "6.2.0", "doctrine": "3.0.0", "glob": "7.1.6", "lodash.mergewith": "^4.6.2", "swagger-parser": "^10.0.3", "yaml": "2.0.0-1"}, "bin": {"swagger-jsdoc": "bin/swagger-jsdoc.js"}, "engines": {"node": ">=12.0.0"}}, "node_modules/swagger-jsdoc/node_modules/commander": {"version": "6.2.0", "resolved": "https://registry.npmjs.org/commander/-/commander-6.2.0.tgz", "integrity": "sha512-zP4jEKbe8SHzKJYQmq8Y9gYjtO/POJLgIdKgV7B9qNmABVFVc+ctqSX6iXh4mCpJfRBOabiZ2YKPg8ciDw6C+Q==", "engines": {"node": ">= 6"}}, "node_modules/swagger-jsdoc/node_modules/glob": {"version": "7.1.6", "resolved": "https://registry.npmjs.org/glob/-/glob-7.1.6.tgz", "integrity": "sha512-LwaxwyZ72Lk7vZINtNNrywX0ZuLyStrdDtabefZKAY5ZGJhVtgdznluResxNmPitE0SAO+O26sWTHeKSI2wMBA==", "dependencies": {"fs.realpath": "^1.0.0", "inflight": "^1.0.4", "inherits": "2", "minimatch": "^3.0.4", "once": "^1.3.0", "path-is-absolute": "^1.0.0"}, "engines": {"node": "*"}, "funding": {"url": "https://github.com/sponsors/isaacs"}}, "node_modules/swagger-jsdoc/node_modules/yaml": {"version": "2.0.0-1", "resolved": "https://registry.npmjs.org/yaml/-/yaml-2.0.0-1.tgz", "integrity": "sha512-W7h5dEhywMKenDJh2iX/LABkbFnBxasD27oyXWDS/feDsxiw0dD5ncXdYXgkvAsXIY2MpW/ZKkr9IU30DBdMNQ==", "engines": {"node": ">= 6"}}, "node_modules/swagger-parser": {"version": "10.0.3", "license": "MIT", "dependencies": {"@apidevtools/swagger-parser": "10.0.3"}, "engines": {"node": ">=10"}}, "node_modules/swagger-parser/node_modules/@apidevtools/swagger-parser": {"version": "10.0.3", "license": "MIT", "dependencies": {"@apidevtools/json-schema-ref-parser": "^9.0.6", "@apidevtools/openapi-schemas": "^2.0.4", "@apidevtools/swagger-methods": "^3.0.2", "@jsdevtools/ono": "^7.1.3", "call-me-maybe": "^1.0.1", "z-schema": "^5.0.1"}, "peerDependencies": {"openapi-types": ">=7"}}, "node_modules/swagger-parser/node_modules/openapi-types": {"version": "12.1.3", "license": "MIT", "peer": true}, "node_modules/swagger-schema-official": {"version": "2.0.0-bab6bed", "dev": true, "license": "ISC"}, "node_modules/swagger-ui-dist": {"version": "4.19.1", "resolved": "https://registry.npmjs.org/swagger-ui-dist/-/swagger-ui-dist-4.19.1.tgz", "integrity": "sha512-n/gFn+R7G/BXWwl5UZLw6F1YgWOlf3zkwGlsPhTMhNtAAolBGKg0JS5b2RKt5NI6/hSopVaSrki2wTIMUDDy2w=="}, "node_modules/tarn": {"version": "3.0.2", "license": "MIT", "engines": {"node": ">=8.0.0"}}, "node_modules/term-size": {"version": "2.2.1", "license": "MIT", "engines": {"node": ">=8"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/text-table": {"version": "0.2.0", "dev": true, "license": "MIT"}, "node_modules/tildify": {"version": "2.0.0", "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/time-span": {"version": "4.0.0", "dev": true, "license": "MIT", "dependencies": {"convert-hrtime": "^3.0.0"}, "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/tmp-cache": {"version": "1.1.0", "license": "MIT", "engines": {"node": ">=6"}}, "node_modules/to-object-path": {"version": "0.3.0", "dev": true, "license": "MIT", "dependencies": {"kind-of": "^3.0.2"}, "engines": {"node": ">=0.10.0"}}, "node_modules/to-object-path/node_modules/kind-of": {"version": "3.2.2", "dev": true, "license": "MIT", "dependencies": {"is-buffer": "^1.1.5"}, "engines": {"node": ">=0.10.0"}}, "node_modules/to-regex": {"version": "3.0.2", "dev": true, "license": "MIT", "dependencies": {"define-property": "^2.0.2", "extend-shallow": "^3.0.2", "regex-not": "^1.0.2", "safe-regex": "^1.1.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/to-regex-range": {"version": "5.0.1", "dev": true, "license": "MIT", "dependencies": {"is-number": "^7.0.0"}, "engines": {"node": ">=8.0"}}, "node_modules/toidentifier": {"version": "1.0.1", "license": "MIT", "engines": {"node": ">=0.6"}}, "node_modules/token-types": {"version": "4.2.1", "license": "MIT", "dependencies": {"@tokenizer/token": "^0.3.0", "ieee754": "^1.2.1"}, "engines": {"node": ">=10"}, "funding": {"type": "github", "url": "https://github.com/sponsors/Borewit"}}, "node_modules/tough-cookie": {"version": "2.5.0", "resolved": "https://registry.npmjs.org/tough-cookie/-/tough-cookie-2.5.0.tgz", "integrity": "sha512-nlLsUzgm1kfLXSXfRZMc1KLAugd4hqJHDTvc2hDIwS3mZAfMEuMbc03SujMF+GEcpaX/qboeycw6iO8JwVv2+g==", "dependencies": {"psl": "^1.1.28", "punycode": "^2.1.1"}, "engines": {"node": ">=0.8"}}, "node_modules/tr46": {"version": "0.0.3", "resolved": "https://registry.npmjs.org/tr46/-/tr46-0.0.3.tgz", "integrity": "sha512-N3WMsuqV66lT30CrXNbEjx4GEwlow3v6rr4mCcv6prnfwhS01rkgyFdjPNBYd9br7LpXV1+Emh01fHnq2Gdgrw=="}, "node_modules/traverse": {"version": "0.6.7", "dev": true, "license": "MIT", "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/truncatise": {"version": "0.0.8", "license": "MIT"}, "node_modules/tslib": {"version": "2.6.2", "license": "0BSD"}, "node_modules/tsutils": {"version": "3.21.0", "dev": true, "license": "MIT", "dependencies": {"tslib": "^1.8.1"}, "engines": {"node": ">= 6"}, "peerDependencies": {"typescript": ">=2.8.0 || >= 3.2.0-dev || >= 3.3.0-dev || >= 3.4.0-dev || >= 3.5.0-dev || >= 3.6.0-dev || >= 3.6.0-beta || >= 3.7.0-dev || >= 3.7.0-beta"}}, "node_modules/tsutils/node_modules/tslib": {"version": "1.14.1", "dev": true, "license": "0BSD"}, "node_modules/tunnel-agent": {"version": "0.6.0", "resolved": "https://registry.npmjs.org/tunnel-agent/-/tunnel-agent-0.6.0.tgz", "integrity": "sha512-McnNiV1l8RYeY8tBgEpuodCC1mLUdbSN+CYBL7kJsJNInOP8UjDDEwdk6Mw60vdLLrr5NHKZhMAOSrR2NZuQ+w==", "dependencies": {"safe-buffer": "^5.0.1"}, "engines": {"node": "*"}}, "node_modules/tweetnacl": {"version": "0.14.5", "resolved": "https://registry.npmjs.org/tweetnacl/-/tweetnacl-0.14.5.tgz", "integrity": "sha512-KXXFFdAbFXY4geFIwoyNK+f5Z1b7swfXABfL7HXCmoIWMKU3dmS26672A4EeQtDzLKy7SXmfBu51JolvEKwtGA=="}, "node_modules/twilio": {"version": "4.18.0", "resolved": "https://registry.npmjs.org/twilio/-/twilio-4.18.0.tgz", "integrity": "sha512-f8etm0l0G2zexwM6wdpjUjLe1iPLLsr0sWTMkkkLUGQ2GAaQcCclXQa6t4gNCDcvgH5wa3vOuTL9p0Ny9cdChQ==", "dependencies": {"axios": "^0.26.1", "dayjs": "^1.11.9", "https-proxy-agent": "^5.0.0", "jsonwebtoken": "^9.0.0", "qs": "^6.9.4", "scmp": "^2.1.0", "url-parse": "^1.5.9", "xmlbuilder": "^13.0.2"}, "engines": {"node": ">=14.0"}}, "node_modules/type-check": {"version": "0.4.0", "dev": true, "license": "MIT", "dependencies": {"prelude-ls": "^1.2.1"}, "engines": {"node": ">= 0.8.0"}}, "node_modules/type-detect": {"version": "4.0.8", "dev": true, "license": "MIT", "engines": {"node": ">=4"}}, "node_modules/type-fest": {"version": "0.20.2", "dev": true, "license": "(MIT OR CC0-1.0)", "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/type-is": {"version": "1.6.18", "license": "MIT", "dependencies": {"media-typer": "0.3.0", "mime-types": "~2.1.24"}, "engines": {"node": ">= 0.6"}}, "node_modules/type-is/node_modules/media-typer": {"version": "0.3.0", "license": "MIT", "engines": {"node": ">= 0.6"}}, "node_modules/typescript": {"version": "4.6.4", "dev": true, "license": "Apache-2.0", "bin": {"tsc": "bin/tsc", "tsserver": "bin/tsserver"}, "engines": {"node": ">=4.2.0"}}, "node_modules/uid-safe": {"version": "2.1.5", "license": "MIT", "dependencies": {"random-bytes": "~1.0.0"}, "engines": {"node": ">= 0.8"}}, "node_modules/union-value": {"version": "1.0.1", "dev": true, "license": "MIT", "dependencies": {"arr-union": "^3.1.0", "get-value": "^2.0.6", "is-extendable": "^0.1.1", "set-value": "^2.0.1"}, "engines": {"node": ">=0.10.0"}}, "node_modules/union-value/node_modules/is-extendable": {"version": "0.1.1", "dev": true, "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/universalify": {"version": "2.0.0", "license": "MIT", "engines": {"node": ">= 10.0.0"}}, "node_modules/unpipe": {"version": "1.0.0", "license": "MIT", "engines": {"node": ">= 0.8"}}, "node_modules/unset-value": {"version": "1.0.0", "dev": true, "license": "MIT", "dependencies": {"has-value": "^0.3.1", "isobject": "^3.0.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/unset-value/node_modules/has-value": {"version": "0.3.1", "dev": true, "license": "MIT", "dependencies": {"get-value": "^2.0.3", "has-values": "^0.1.4", "isobject": "^2.0.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/unset-value/node_modules/has-value/node_modules/isobject": {"version": "2.1.0", "dev": true, "license": "MIT", "dependencies": {"isarray": "1.0.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/unset-value/node_modules/has-values": {"version": "0.1.4", "dev": true, "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/upper-case": {"version": "2.0.2", "license": "MIT", "dependencies": {"tslib": "^2.0.3"}}, "node_modules/upper-case-first": {"version": "2.0.2", "license": "MIT", "dependencies": {"tslib": "^2.0.3"}}, "node_modules/uri-js": {"version": "4.4.1", "license": "BSD-2-<PERSON><PERSON>", "dependencies": {"punycode": "^2.1.0"}}, "node_modules/urix": {"version": "0.1.0", "dev": true, "license": "MIT"}, "node_modules/url-parse": {"version": "1.5.10", "resolved": "https://registry.npmjs.org/url-parse/-/url-parse-1.5.10.tgz", "integrity": "sha512-WypcfiRhfeUP9vvF0j6rw0J3hrWrw6iZv3+22h6iRMJ/8z1Tj6XfLP4DsUix5MhMPnXpiHDoKyoZ/bdCkwBCiQ==", "dependencies": {"querystringify": "^2.1.1", "requires-port": "^1.0.0"}}, "node_modules/use": {"version": "3.1.1", "dev": true, "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/util-deprecate": {"version": "1.0.2", "license": "MIT"}, "node_modules/utils-merge": {"version": "1.0.1", "resolved": "https://registry.npmjs.org/utils-merge/-/utils-merge-1.0.1.tgz", "integrity": "sha512-pMZTvIkT1d+TFGvDOqodOclx0QWkkgi6Tdoa8gC8ffGAAqz9pzPTZWAybbsHHoED/ztMtkv/VoYTYyShUn81hA==", "engines": {"node": ">= 0.4.0"}}, "node_modules/uuid": {"version": "9.0.1", "funding": ["https://github.com/sponsors/broofa", "https://github.com/sponsors/ctavan"], "license": "MIT", "bin": {"uuid": "dist/bin/uuid"}}, "node_modules/uuid-random": {"version": "1.3.2", "license": "MIT"}, "node_modules/valid-url": {"version": "1.0.9", "dev": true}, "node_modules/validate-npm-package-name": {"version": "3.0.0", "dev": true, "license": "ISC", "dependencies": {"builtins": "^1.0.3"}}, "node_modules/validator": {"version": "13.11.0", "license": "MIT", "engines": {"node": ">= 0.10"}}, "node_modules/vary": {"version": "1.1.2", "license": "MIT", "engines": {"node": ">= 0.8"}}, "node_modules/verror": {"version": "1.10.0", "resolved": "https://registry.npmjs.org/verror/-/verror-1.10.0.tgz", "integrity": "sha512-ZZKSmDAEFOijERBLkmYfJ+vmk3w+7hOLYDNkRCuRuMJGEmqYNCNLyBBFwWKVMhfwaEF3WOd0Zlw86U/WC/+nYw==", "engines": ["node >=0.6.0"], "dependencies": {"assert-plus": "^1.0.0", "core-util-is": "1.0.2", "extsprintf": "^1.2.0"}}, "node_modules/verror/node_modules/core-util-is": {"version": "1.0.2", "resolved": "https://registry.npmjs.org/core-util-is/-/core-util-is-1.0.2.tgz", "integrity": "sha512-3lqz5YjWTYnW6dlDa5TLaTCcShfar1e40rmcJVwCBJC6mWlFuj0eCHIElmG1g5kyuJ/GD+8Wn4FFCcz4gJPfaQ=="}, "node_modules/wcwidth": {"version": "1.0.1", "dev": true, "license": "MIT", "dependencies": {"defaults": "^1.0.3"}}, "node_modules/webidl-conversions": {"version": "3.0.1", "resolved": "https://registry.npmjs.org/webidl-conversions/-/webidl-conversions-3.0.1.tgz", "integrity": "sha512-2JAn3z8AR6rjK8Sm8orRC0h/bcl/DqL7tRPdGZ4I1CjdF+EaMLmYxBHyXuKL849eucPFhvBoxMsflfOb8kxaeQ=="}, "node_modules/webpack-merge": {"version": "4.2.2", "dev": true, "license": "MIT", "dependencies": {"lodash": "^4.17.15"}}, "node_modules/whatwg-url": {"version": "5.0.0", "resolved": "https://registry.npmjs.org/whatwg-url/-/whatwg-url-5.0.0.tgz", "integrity": "sha512-saE57nupxk6v3HY35+jzBwYa0rKSy0XR8JSxZPwgLr7ys0IBzhGviA1/TUGJLmSVqs8pb9AnvICXEuOHLprYTw==", "dependencies": {"tr46": "~0.0.3", "webidl-conversions": "^3.0.0"}}, "node_modules/which": {"version": "2.0.2", "dev": true, "license": "ISC", "dependencies": {"isexe": "^2.0.0"}, "bin": {"node-which": "bin/node-which"}, "engines": {"node": ">= 8"}}, "node_modules/wordwrap": {"version": "1.0.0", "dev": true, "license": "MIT"}, "node_modules/wrap-ansi": {"version": "6.2.0", "license": "MIT", "dependencies": {"ansi-styles": "^4.0.0", "string-width": "^4.1.0", "strip-ansi": "^6.0.0"}, "engines": {"node": ">=8"}}, "node_modules/wrappy": {"version": "1.0.2", "license": "ISC"}, "node_modules/xmlbuilder": {"version": "13.0.2", "resolved": "https://registry.npmjs.org/xmlbuilder/-/xmlbuilder-13.0.2.tgz", "integrity": "sha512-Eux0i2QdDYKbdbA6AM6xE4m6ZTZr4G4xF9kahI2ukSEMCzwce2eX9WlTI5J3s+NU7hpasFsr8hWIONae7LluAQ==", "engines": {"node": ">=6.0"}}, "node_modules/yallist": {"version": "2.1.2", "dev": true, "license": "ISC"}, "node_modules/yaml": {"version": "2.3.2", "license": "ISC", "engines": {"node": ">= 14"}}, "node_modules/yocto-queue": {"version": "0.1.0", "dev": true, "license": "MIT", "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/youch": {"version": "3.3.1", "dev": true, "license": "MIT", "dependencies": {"cookie": "^0.5.0", "mustache": "^4.2.0", "stacktracey": "^2.1.8"}}, "node_modules/youch-terminal": {"version": "2.2.2", "dev": true, "license": "MIT", "dependencies": {"kleur": "^4.1.5", "string-width": "^4.2.3", "wordwrap": "^1.0.0"}}, "node_modules/z-schema": {"version": "5.0.5", "license": "MIT", "dependencies": {"lodash.get": "^4.4.2", "lodash.isequal": "^4.5.0", "validator": "^13.7.0"}, "bin": {"z-schema": "bin/z-schema"}, "engines": {"node": ">=8.0.0"}, "optionalDependencies": {"commander": "^9.4.1"}}, "node_modules/z-schema/node_modules/commander": {"version": "9.5.0", "license": "MIT", "optional": true, "engines": {"node": "^12.20.0 || >=14"}}}}