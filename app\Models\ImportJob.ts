import { DateTime } from 'luxon'
import User from './User'
import { BaseModel, BelongsTo, belongsTo, column } from '@ioc:Adonis/Lucid/Orm'

export default class ImportJob extends BaseModel {
  @column({ isPrimary: true })
  public id: number

  @column({ serializeAs: null })
  public partnerId: number

  @column({ serializeAs: null })
  public userId: number

  @belongsTo(() => User)
  public partner: BelongsTo<typeof User>

  @belongsTo(() => User)
  public user: BelongsTo<typeof User>

  @column()
  public status: 'pending' | 'processing' | 'completed' | 'failed_with_errors' | 'failed'

  @column()
  public totalRows: number

  @column()
  public processedRows: number

  @column()
  public originalFilename: string

  @column()
  public storedFilePath: string

  @column()
  public errorsReport: any | null

  @column()
  public isRead: boolean

  @column()
  public completedAt: DateTime | null

  @column.dateTime({
    autoCreate: true,
    serialize: (value: DateTime) => {
      if (value) return value.toFormat('yyyy-MM-dd HH:mm:ss').replace(/\s+/g, 'T')
      return
    },
  })
  public createdAt: DateTime

  @column.dateTime({
    autoCreate: true,
    autoUpdate: true,
    serialize: (value: DateTime) => {
      if (value) return value.toFormat('yyyy-MM-dd HH:mm:ss').replace(/\s+/g, 'T')
      return
    },
  })
  public updatedAt: DateTime
}
