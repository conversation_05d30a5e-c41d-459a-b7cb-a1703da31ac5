import { DateTime } from 'luxon'
import { BaseModel, beforeCreate, BelongsTo, belongsTo, column } from '@ioc:Adonis/Lucid/Orm'
import { v4 as uuid } from 'uuid'
import User from './User'
import Schedule from './Schedule'

export default class ScheduleDatesRequest extends BaseModel {
	@column({ isPrimary: true, serializeAs: null })
	public id: number

	@column()
	public secureId: string

	@column({ serializeAs: null })
	public scheduleId: number

	@column({ serializeAs: null })
	public partnerId: number | null

	@column()
	public partnerType: 'helloMed' | 'doctor' | 'clinic' | 'lab'

	@column.dateTime({
		autoCreate: true,
		serialize: (value: DateTime) => {
			if (value) return value.toFormat('yyyy-MM-dd HH:mm:ss').replace(/\s+/g, 'T')
			return
		},
	})
	public date: DateTime

	@column()
	public dateType: 'hour' | 'period'

	@column()
	public value: 'morning' | 'afternoon' | 'night'

	@column()
	public type: 'patient' | 'backoffice'

	@column()
	public status: 'to_check' | 'available' | 'unavailable'

	@column()
	public paymentMethods: string

	@column()
	public queryValue: number

	@column()
	public queryValueSubsidy: number

	@column()
	public queryValuePatient: number

	@belongsTo(() => User, {
		localKey: 'id',
		foreignKey: 'partnerId',
	})
	public partner: BelongsTo<typeof User>

	@belongsTo(() => Schedule)
	public schedule: BelongsTo<typeof Schedule>

	@column.dateTime({
		autoCreate: true,
		serialize: (value: DateTime) => {
			if (value) return value.toFormat('yyyy-MM-dd HH:mm:ss').replace(/\s+/g, 'T')
			return
		},
	})
	public createdAt: DateTime

	@column.dateTime({
		autoCreate: true,
		autoUpdate: true,
		serialize: (value: DateTime) => {
			if (value) return value.toFormat('yyyy-MM-dd HH:mm:ss').replace(/\s+/g, 'T')
			return
		},
	})
	public updatedAt: DateTime

	@beforeCreate()
	public static async createUUID(model: ScheduleDatesRequest) {
		model.secureId = uuid()
	}
}
