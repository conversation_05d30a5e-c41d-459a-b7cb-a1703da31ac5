import type { HttpContextContract } from '@ioc:Adonis/Core/HttpContext'
import { ModelQueryBuilderContract } from '@ioc:Adonis/Lucid/Orm';

import User from 'App/Models/User'

type GetPartinerProps = {
	typeConsult: string;
	attendantType?: string
	search: string;
	specialtyOrExamSecureId: string;
	page: number;
	limit: number;
	builderWhere: (builder: ModelQueryBuilderContract<typeof User, User>) => void;
}

const getPartiner = async ({ typeConsult, search, specialtyOrExamSecureId, limit, page, builderWhere, attendantType }: GetPartinerProps) => {
	return await User.query()
		.select('id', 'avatarId', 'secure_id', 'email', 'type')
		.whereHas('roles', (builder) => {
			builder.whereIn('slug', ['accredited'])
			if (attendantType) {
				builder.andWhere('type', attendantType)
			}
		})
		.andWhere((builder) => {
			if (typeConsult === 'in_person' || typeConsult === 'video_call') {
				builder.whereHas('userInfo', (builderUserInfo) => {
					builderUserInfo.where('type_of_care', typeConsult)
					builderUserInfo.orWhere('type_of_care', 'both')
				})
			}
		})
		.andWhere((builder) => {
			if (search) {
				builder.whereILike('email', `%${search}%`)
				builder.orWhereHas('userInfo', builder => {
					builder.whereILike('name', `%${search}%`)
				})
			}
		})
		.andWhere(builder => {
			if (specialtyOrExamSecureId) {
				if (typeConsult === 'in_person' || typeConsult === 'video_call') {
					builder.whereHas('specialties', builderSpecialty => {
						builderSpecialty.where('secure_id', specialtyOrExamSecureId)
					})
				} else {
					builder.whereHas('exams', builderExam => {
						builderExam.where('secure_id', specialtyOrExamSecureId)
					})
				}
			}
		})
		.andWhere('show_accredited_in_app', true)
		.andWhereNot('type', 'patient')
		.andWhere(builderWhere)
		.preload('avatar')
		.preload('userInfo')
		.paginate(page, limit)
}

export default class AttendantController {
	public async index({ response, request }: HttpContextContract) {
		const {
			page = 1,
			limit = 15,
			search,
			typeConsult,
			specialtyOrExamSecureId,
			local,
			attendantType
		} = request.only(['page', 'limit', 'search', 'typeConsult', 'specialtyOrExamSecureId', 'local', 'attendantType'])

		const partnersNeighborhood = await getPartiner({
			specialtyOrExamSecureId,
			typeConsult,
			attendantType,
			search,
			limit,
			page,
			builderWhere: (builder: ModelQueryBuilderContract<typeof User, User>) => {
				builder.whereHas('userInfo', builder => {
					builder.where('neighborhood', local.neighborhood)
					builder.andWhere('city', local.city)
					builder.andWhere('state', local.state)
				})
			}
		})

		if (partnersNeighborhood.toJSON().meta.current_page < partnersNeighborhood.toJSON().meta.last_page) {
			return response.ok(partnersNeighborhood)
		}

		const pageCity = page - partnersNeighborhood.toJSON().meta.last_page + 1

		const partnersCity = await getPartiner({
			specialtyOrExamSecureId,
			typeConsult,
			attendantType,
			search,
			limit,
			page: pageCity,
			builderWhere: (builder: ModelQueryBuilderContract<typeof User, User>) => {
				builder.whereHas('userInfo', builder => {
					builder.where('city', local.city)
					builder.andWhere('state', local.state)
					builder.andWhereNot('neighborhood', local.neighborhood)
				})
			}
		})

		if (partnersCity.toJSON().meta.current_page < partnersCity.toJSON().meta.last_page) {
			const dataReturn = [...partnersNeighborhood.toJSON().data.map(i => i.toJSON()), ...partnersCity.toJSON().data.map(i => i.toJSON())]

			return response.ok({
				data: dataReturn,
				meta: {
					total: dataReturn.length,
					per_page: limit,
					current_page: partnersCity.toJSON().meta.current_page + partnersNeighborhood.toJSON().meta.current_page,
					last_page: partnersCity.toJSON().meta.last_page + partnersNeighborhood.toJSON().meta.last_page,
				}
			})
		}

		const pageState = page - partnersCity.toJSON().meta.last_page - partnersNeighborhood.toJSON().meta.last_page + 2

		const partnersState = await getPartiner({
			specialtyOrExamSecureId,
			typeConsult,
			attendantType,
			search,
			limit,
			page: pageState,
			builderWhere: (builder: ModelQueryBuilderContract<typeof User, User>) => {
				builder.whereHas('userInfo', builder => {
					builder.where('state', local.state)
					builder.andWhereNot('neighborhood', local.neighborhood)
					builder.andWhereNot('city', local.city)
				})
			}
		})

		const dataReturn = [...partnersNeighborhood.toJSON().data.map(i => i.toJSON()), ...partnersCity.toJSON().data.map(i => i.toJSON()), ...partnersState.toJSON().data.map(i => i.toJSON())]

		return response.ok({
			data: dataReturn,
			meta: {
				total: dataReturn.length,
				per_page: limit,
				current_page: partnersCity.toJSON().meta.current_page + partnersNeighborhood.toJSON().meta.current_page + partnersState.toJSON().meta.current_page,
				last_page: partnersCity.toJSON().meta.last_page + partnersState.toJSON().meta.last_page + partnersNeighborhood.toJSON().meta.last_page,
			}
		})
	}
}
