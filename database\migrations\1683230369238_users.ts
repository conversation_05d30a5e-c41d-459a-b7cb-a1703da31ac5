import BaseSchema from '@ioc:Adonis/Lucid/Schema'

export default class extends BaseSchema {
	protected tableName = 'users'

	public async up() {
		this.schema.createTable(this.tableName, (table) => {
			table.increments('id').primary()
			table.string('secure_id')
			table
				.integer('parent_id')
				.unsigned()
				.references('id')
				.inTable('users')
				.onUpdate('CASCADE')
				.onDelete('RESTRICT')
			table.string('email', 255).notNullable()
			table.string('password', 180).notNullable()
			table.string('remember_me_token').nullable()
			table
				.enu('type', ['patient', 'dependent', 'doctor', 'clinic', 'lab', 'hospital', 'admin'])
				.defaultTo('patient')
			table.boolean('deleted').defaultTo(false).notNullable()

			table.dateTime('created_at').notNullable()
			table.dateTime('updated_at').notNullable()

			table.unique(['email', 'parent_id'])
		})
	}

	public async down() {
		this.schema.dropTable(this.tableName)
	}
}
