import Route from '@ioc:Adonis/Core/Route'
import { isRoles } from 'App/Contants/Roles'

Route.group(() => {
  Route.resource('holidays', 'V1/Admin/Holiday/MainController').middleware({
		index: ['auth', `${isRoles(['MASTER', 'ADMIN'])}`],
		store: ['auth', `${isRoles(['MASTER', 'ADMIN'])}`],
		show: ['auth', `${isRoles(['MASTER', 'ADMIN'])}`],
		update: ['auth', `${isRoles(['MASTER', 'ADMIN'])}`],
		destroy: ['auth', `${isRoles(['MASTER', 'ADMIN'])}`],
	})

  Route.get(
    'holidays-by-current-date/:currentDate',
    'V1/Admin/Holiday/MainController.searchByDate'
  ).middleware([
		'auth',
		`${isRoles(['MASTER', 'ADMIN'])}`,
	])
})
