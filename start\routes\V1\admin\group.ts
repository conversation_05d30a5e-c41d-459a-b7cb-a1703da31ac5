import Route from '@ioc:Adonis/Core/Route'
import { isPermissions } from 'App/Contants/Permissions'
import { isRoles } from 'App/Contants/Roles'

Route.group(() => {

	Route.resource('groups', 'V1/Admin/Group/MainController').middleware({
		index: ['auth', `${isRoles(['MASTER', 'ADMIN'])}`, isPermissions(['groups_view'])],
		store: ['auth', `${isRoles(['MASTER', 'ADMIN'])}`, isPermissions(['groups_create'])],
		update: ['auth', `${isRoles(['MASTER', 'ADMIN'])}`, isPermissions(['groups_edit'])],
		destroy: ['auth', `${isRoles(['MASTER', 'ADMIN'])}`, isPermissions(['groups_delete'])],
	})

	Route.resource('groups/:secure_id/users', 'V1/Admin/Group/GroupUsersController').middleware({
		index: ['auth', `${isRoles(['MASTER', 'ADMIN'])}`, isPermissions(['groups_view'])],
		store: ['auth', `${isRoles(['MASTER', 'ADMIN'])}`, isPermissions(['groups_create'])],
		destroy: ['auth', `${isRoles(['MASTER', 'ADMIN'])}`, isPermissions(['groups_delete'])],
	})

	Route.resource('groups/:secure_id/accrediteds', 'V1/Admin/Group/GroupAccreditedsController').middleware({
		index: ['auth', `${isRoles(['MASTER', 'ADMIN'])}`, isPermissions(['groups_view'])],
		store: ['auth', `${isRoles(['MASTER', 'ADMIN'])}`, isPermissions(['groups_create'])],
		destroy: ['auth', `${isRoles(['MASTER', 'ADMIN'])}`, isPermissions(['groups_delete'])],
	})
})
