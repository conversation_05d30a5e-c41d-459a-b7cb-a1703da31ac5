import BaseSchema from '@ioc:Adonis/Lucid/Schema'

export default class extends BaseSchema {
  protected tableName = 'schedule_dates_requests'

  public async up () {
    this.schema.alterTable(this.tableName, (table) => {
      table.integer('query_value_subsidy').after('query_value')
      table.integer('query_value_patient').after('query_value_subsidy')
    })
  }

  public async down () {
    this.schema.alterTable(this.tableName ,(table) => {
      table.dropColumn('query_value_subsidy')
      table.dropColumn('query_value_patient')
    })
  }
}
