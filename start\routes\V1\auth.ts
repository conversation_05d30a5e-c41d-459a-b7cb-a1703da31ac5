import Route from '@ioc:Adonis/Core/Route'
import { allRoles } from 'App/Contants/Roles'

Route.group(() => {
	Route.post('sessions', 'V1/Auth/AuthController.store')
	Route.delete('sessions', 'V1/Auth/AuthController.destroy').middleware(['auth', `${allRoles()}`])

	Route.resource('me', 'V1/Auth/MeController').middleware({
		index: ['auth', `${allRoles()}`],
	})

	Route.put('profile', 'V1/Auth/ProfileController.update').middleware(['auth', `${allRoles()}`])
	Route.get('profile', 'V1/Auth/ProfileController.show').middleware(['auth', `${allRoles()}`])

	Route.post('recovery_password', 'V1/Auth/ForgotPasswordController.store')
	Route.put('reset_password', 'V1/Auth/ForgotPasswordController.update')

	Route.put('change-password', 'V1/Auth/ChangePasswordController.update')
		.middleware(['auth', `${allRoles()}`])
})
