import { BaseCommand, flags } from '@adonisjs/core/build/standalone'
import Database from '@ioc:Adonis/Lucid/Database'
import TemplateNotifications from 'App/Models/TemplateNotifications'

export default class CreateTemplateNotification extends BaseCommand {

	public static commandName = 'create:templatenotifications'

	public static title = 'Popular notificações'

	public static description = 'Popula a tabela de templatenotification com as notificações a serem atualizadas'

	@flags.boolean()
	public confirmation: boolean = true

	public static settings = {
		loadApp: true,
		stayAlive: false,
	}

	public async run() {
		const templates = [
			"consultSolicitation",
			"examSolicitation",
			"consultConfirmation",
			"examConfirmation",
			"consultOptions",
			"examOptions",
			"consult3h",
			"consult24h",
			"exam3h",
			"exam24h",
			"examCancelForPatient",
			"consultCancelForPatient",
			"examCancelForPatient",
			"consultCancelForPatientSendBackoffice",
			"examCancelForPatientSendBackoffice",
		] as const

		const types = ['push', 'email', 'sms'] as const

		const subjectMap = {
			'push': 'Subtítulo',
			'email': 'Assunto',
			'sms': null
		}

		try {
			await Database.transaction(async (trx) => {
				const templatesExists = await TemplateNotifications.query()

				for await (const template of templates) {
					for await (const type of types) {
						if (!((template === 'consultCancelForPatientSendBackoffice' || template === 'examCancelForPatientSendBackoffice') && (type !== 'email'))) {
							if (!templatesExists.find(item => item.type === type && item.name === template)) {
								const subject = subjectMap[type];
								const templateNotification = new TemplateNotifications()
								templateNotification.merge({
									name: template,
									type: type,
									subject: subject ? `${subject} para ${template} ${type}` : undefined,
									content: `Conteúdo para ${template} ${type}`,
								})
								await templateNotification.useTransaction(trx).save()
							}
						}
					}
				}
			})

			this.logger.log(`${this.colors.green('Templates de notificações criados!')} ${this.ui.icons.tick}`)
			await Database.manager.closeAll()
		} catch (error) {
			this.logger.logError(error)
			this.logger.log(`${this.colors.red('Ocorreu um erro ao criar os templates!')} ${this.ui.icons.cross}`)
			await Database.manager.closeAll()
		}
	}
}
