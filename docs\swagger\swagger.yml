openapi: 3.0.3
paths:
  /v1/sessions:
    post:
      tags:
        - Auth
      summary: Iniciar uma Nova Sessão
      description: Esta operação permite que você crie uma nova sessão de usuário, fornecendo seu email e senha. Após a autenticação bem-sucedida, você terá acesso às funcionalidades da sua conta.
      operationId: createSession
      requestBody:
        description: Email e senha para uma nova sessão
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                email:
                  type: string
                  example: '<EMAIL>'
                password:
                  type: string
                  example: 'teste'
      responses:
        '200':
          description: Success
        '500':
          description: Internal Server Error
    delete:
      tags:
        - Auth
      summary: Encerrar Sessão - ROLES 'ALL'
      description: Esta operação permite encerrar a sessão do usuário. Para executá-la, é necessário autenticação usando um Bearer Token, garantindo a segurança e a privacidade da sua conta.
      operationId: deleteSession
      responses:
        '200':
          description: Success
        '401':
          description: Unauthorized access
      security:
        - bearerAuth: []

  /v1/recovery_password:
    post:
      tags:
        - Auth
      summary: Recuperação de Senha
      description: Esta operação permite enviar um email ao usuário com instruções para recuperar sua senha. Caso tenha esquecido sua senha, siga as instruções no email para redefinir sua senha e recuperar o acesso à sua conta.
      operationId: recoveryPassword
      requestBody:
        description: Email do usuário para recuperação de senha.
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                email:
                  type: string
                  example: '<EMAIL>'
                redirectUrl:
                  type: string
                  example: 'https://hellomed.com.br/forgotpassword'
      responses:
        '200':
          description: Success
        '500':
          description: Internal Server Error

  /v1/reset_password:
    put:
      tags:
        - Auth
      summary: Redefinir Senha
      description: Esta operação permite que um usuário redefina sua senha. Se você esqueceu sua senha atual, utilize esta funcionalidade para criar uma nova senha e recuperar o acesso à sua conta.
      operationId: resetPassword
      requestBody:
        description: Nova senha do usuário.
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                password:
                  type: string
                  example: 'password'
                token:
                  type: string
                  example: 'token'
      responses:
        '200':
          description: Success
        '500':
          description: Internal Server Error
        '404':
          description: Not Found

  /v1/me:
    get:
      tags:
        - Auth
      summary: Consultar Detalhes do Usuário Autenticado - ROLES 'ALL'
      description: Esta operação permite obter informações detalhadas sobre o usuário que está atualmente autenticado no sistema. Ela oferece acesso a dados como perfil, histórico de atividades e preferências do usuário.
      operationId: getMe
      responses:
        '200':
          description: Success
        '401':
          description: Unauthorized access
      security:
        - bearerAuth: []

  /v1/profile:
    get:
      tags:
        - Profile
      summary: Consultar Detalhes de Cadastro do Usuário Autenticado - ROLES 'ALL'
      description: Esta operação permite acessar os dados de cadastro completos do usuário que está atualmente autenticado no sistema. Você terá acesso a informações como nome, endereço, informações de contato e detalhes de perfil.
      operationId: getProfile
      responses:
        '200':
          description: Success
        '401':
          description: Unauthorized access
      security:
        - bearerAuth: []
    put:
      tags:
        - Profile
      summary: Atualizar Informações de Cadastro do Usuário Autenticado - ROLES 'ALL'
      description: Esta operação permite que o usuário autenticado atualize e modifique suas informações de cadastro, como nome, endereço, informações de contato e detalhes de perfil. Mantenha seus dados atualizados para garantir uma experiência personalizada e precisa.
      operationId: putProfile
      requestBody:
        description: Alteração de dados do usuário.
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                email:
                  type: string
                name:
                  type: string
                avatar:
                  type: string
                legal_document_number:
                  type: string
                gender:
                  type: string
                  enum: ['masculine', 'feminine']
                ddd_phone:
                  type: integer
                phone:
                  type: integer
                ddd_cell:
                  type: integer
                cell:
                  type: integer
                zip_code:
                  type: string
                street:
                  type: string
                number:
                  type: string
                complement:
                  type: string
                neighborhood:
                  type: string
                city:
                  type: string
                state:
                  type: string
                type_of_care:
                  type: string
                  enum: ['in_person', 'video_call', 'both']
                payment_methods:
                  type: string
                advice_register:
                  type: string
      responses:
        '200':
          description: Success
        '401':
          description: Unauthorized access
        '404':
          description: Not Found
      security:
        - bearerAuth: []

  /v1/uploads:
    get:
      tags:
        - Uploads
      summary: Consultar Arquivos Enviados - ROLES 'ALL'.
      description: Esta operação permite obter uma lista de todos os arquivos previamente enviados para o sistema. Para acessar esses arquivos, é necessário estar autenticado no sistema, garantindo a segurança e a privacidade dos dados.
      operationId: getUploads
      parameters:
        - name: search
          in: query
          description: Parâmetro de pesquisa
          required: false
          schema:
            type: string
        - name: type
          in: query
          description: Parâmetro de pesquisa
          required: false
          schema:
            type: string
        - name: page
          in: query
          description: Número da página
          required: false
          schema:
            type: integer
        - name: limit
          in: query
          description: Limite de itens por página
          required: false
          schema:
            type: integer
      responses:
        '200':
          description: Success
        '401':
          description: Unauthorized access
      security:
        - bearerAuth: []
    post:
      tags:
        - Uploads
      summary: Enviar Arquivos para o Bucket - ROLES 'ALL'
      description: Esta operação permite que usuários autenticados enviem arquivos para o bucket do sistema. Garanta a segurança e a integridade de seus documentos ao utilizar esta funcionalidade.
      operationId: postUploads
      requestBody:
        description: Precisa passar o file
        content:
          multipart/form-data:
            schema:
              type: object
              properties:
                file:
                  type: string
                  format: binary
                name:
                  type: string
                  description: nome do arquivo
      responses:
        '200':
          description: Success
        '500':
          description: Unauthorized access
      security:
        - bearerAuth: []

  /v1/uploads/{id}: 
    get:
      tags:
        - Uploads
      summary: Consultar Arquivo Específico - ROLES 'ALL'
      description: Esta operação permite obter um arquivo específico com base em um parâmetro. Para acessar o arquivo desejado, é necessário estar autenticado no sistema, garantindo a segurança e a privacidade dos dados.
      operationId: getUpload
      parameters:
        - name: id
          in: path
          description: Id do arquivo para obter
          required: true
          schema:
            type: string
      responses:
        '200':
          description: Success
        '401':
          description: Unauthorized access
        '404':
          description: Not Found
      security:
        - bearerAuth: []

  /v1/public/sign-up:
    post:
      tags:
        - Public
      summary: Cadastro de Novo Usuário
      description: Esta rota permite que um novo usuário se cadastre no sistema. Ao utilizar esta funcionalidade, os usuários podem criar uma conta e começar a aproveitar os serviços oferecidos.
      operationId: signUpPublic
      requestBody:
        description: Dados para o usuário se inscrever
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                email:
                  type: string
                  example: '<EMAIL>'
                password:
                  type: string
                  example: 'teste'
                name:
                  type: string
                  example: 'joão'
                ddd_cell:
                  type: integer
                  example: 87
                cell:
                  type: integer
                  example: 988020647
      responses:
        '200':
          description: Success
        '500':
          description: Internal Server Error

  /v1/public/specialties:
    get:
      tags:
        - Public
      summary: Consultar Todas as Especialidades do Usuário
      description: Esta operação permite obter uma lista de todas as especialidades disponíveis para o usuário após autenticação. Explore e selecione as especialidades que melhor atendam às suas necessidades de serviço.
      operationId: getSpecialties
      parameters:
        - name: search
          in: query
          description: Parâmetro de pesquisa
          required: false
          schema:
            type: string
        - name: page
          in: query
          description: Número da página
          required: false
          schema:
            type: integer
        - name: limit
          in: query
          description: Limite de itens por página
          required: false
          schema:
            type: integer
      responses:
        '200':
          description: Success
        '500':
          description: Internal Server Error

  /v1/public/attendants:
    get:
      tags:
        - Public
      summary: Consultar Lista de Atendentes
      description: Esta operação permite obter uma lista de todos os atendentes disponíveis. Explore os perfis dos atendentes e encontre o profissional que melhor atenda às suas necessidades de atendimento.
      operationId: getAttendants
      parameters:
        - name: search
          in: query
          description: Parâmetro de pesquisa
          required: false
          schema:
            type: string
        - name: typeConsult
          in: query
          description: Pesquisa pelo tipo de consulta
          required: false
          schema:
            type: string
        - name: page
          in: query
          description: Número da página
          required: false
          schema:
            type: integer
        - name: limit
          in: query
          description: Limite de itens por página
          required: false
          schema:
            type: integer
      responses:
        '200':
          description: Success
        '500':
          description: Internal Server Error

  /v1/public/exams:
    get:
      tags:
        - Public
      summary: Consultar Lista de Exames Disponíveis
      description: Esta operação permite obter uma lista completa de todos os exames disponíveis. Explore as opções de exames disponíveis para atender às suas necessidades de saúde e diagnóstico.
      operationId: getExams
      parameters:
        - name: search
          in: query
          description: Parâmetro de pesquisa
          required: false
          schema:
            type: string
        - name: page
          in: query
          description: Número da página
          required: false
          schema:
            type: integer
        - name: limit
          in: query
          description: Limite de itens por página
          required: false
          schema:
            type: integer
      responses:
        '200':
          description: Success
        '500':
          description: Internal Server Error

  /v1/public/states:
    get:
      tags:
        - Public
      summary: Consultar Estados ou Aplicar Filtro
      description: Esta operação permite obter uma lista de todos os estados disponíveis ou aplicar um filtro para encontrar estados específicos de acordo com suas necessidades. Personalize sua consulta para obter informações relevantes.
      operationId: getStates
      parameters:
        - in: query
          name: search
          schema:
            type: string
          description: Nome do estado para buscar
      responses:
        '200':
          description: Success
        '500':
          description: Internal Server Error

  /v1/public/cities:
    get:
      tags:
        - Public
      summary: Consultar Cidades ou Aplicar Filtro
      description: Esta operação permite obter uma lista de todas as cidades disponíveis ou aplicar um filtro para encontrar cidades específicas de acordo com suas necessidades. Personalize sua consulta para obter informações relevantes sobre as cidades desejadas.
      operationId: getCities
      parameters:
        - in: query
          name: search
          schema:
            type: string
          description: Nome da cidade para buscar
        - in: query
          name: uf
          schema:
            type: string
          description: Unidade federativa (estado) para buscar
      responses:
        '200':
          description: Success
        '500':
          description: Internal Server Error

  /v1/patient/appointments:
    get:
      tags:
        - Patient Appointments 
      summary: Consultar Agendamentos com Médicos do Paciente - ROLES 'ALL'
      description: Esta operação permite que pacientes autenticados obtenham uma lista completa de todos os agendamentos feitos com médicos. Através desta consulta, você pode gerenciar seus compromissos médicos de maneira eficiente e conveniente.
      operationId: getAppointments
      parameters:
        - name: status
          in: query
          description: Parâmetro de pesquisa
          required: false
          schema:
            type: string
        - name: page
          in: query
          description: Número da página
          required: false
          schema:
            type: integer
        - name: limit
          in: query
          description: Limite de itens por página
          required: false
          schema:
            type: integer
      responses:
        '200':
          description: Success
        '401':
          description: Unauthorized access
      security:
        - bearerAuth: []
    post:
      tags:
        - Patient Appointments
      summary: Confirmar Agendamento - ROLES 'ALL'
      description: Esta operação permite que você confirme um agendamento específico, garantindo a reserva do seu compromisso. Para confirmar, é necessário autenticação e fornecer o SecureId associado ao agendamento.
      operationId: postAppointments
      requestBody:
        required: true
        content:
          application/json:
            schema:
              properties:
                scheduleDatesSecureId:
                  type: string
      responses:
        '200':
          description: Success
        '401':
          description: Unauthorized access
        '404':
          description: Not Found
      security:
        - bearerAuth: []

  /v1/patient/dependents:
    get:
      tags:
        - Patient Dependents
      summary: Consultar Dependentes dos Pacientes - ROLES 'ALL'
      description: Esta operação permite que pacientes autenticados obtenham uma lista completa de todos os seus dependentes registrados. Através desta consulta, você pode gerenciar de forma eficiente as informações dos dependentes associados à sua conta.
      operationId: getDependents
      parameters:
        - name: search
          in: query
          description: Parâmetro de pesquisa
          required: false
          schema:
            type: string
        - name: page
          in: query
          description: Número da página
          required: false
          schema:
            type: integer
        - name: limit
          in: query
          description: Limite de itens por página
          required: false
          schema:
            type: integer
      responses:
        '200':
          description: Success
        '401':
          description: Unauthorized access
      security:
        - bearerAuth: []
    post:
      tags:
        - Patient Dependents
      summary: Adicionar Dependente ao Usuário Autenticado - ROLES 'ALL'
      description: Esta operação permite que um usuário autenticado adicione um novo dependente à sua conta. Ao cadastrar dependentes, você pode gerenciar os perfis de familiares ou indivíduos associados à sua conta de forma conveniente.
      operationId: postDependents
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                avatarSecureId:
                  default: string | undefined
                name:
                  type: string
                birthDate:
                  default: DateTime
                legal_document_number:
                  default: string | undefined
      responses:
        '200':
          description: Success
        '401':
          description: Unauthorized access
      security:
        - bearerAuth: []

  /v1/patient/dependents/{id}:
    get:
      tags:
        - Patient Dependents
      summary: Consultar Dependente de Conta de Paciente - ROLES 'ALL'
      description: Esta operação permite que pacientes autenticados consultem um dependente associado à sua conta. Certifique-se de autenticar-se antes de realizar essa ação para manter o controle de seus perfis de dependentes.
      operationId: getDependent
      parameters:
        - name: id
          in: path
          description: Id do dependente para consultar
          required: true
          schema:
            type: string
      responses:
        '200':
          description: Success
        '401':
          description: Unauthorized access
        '404':
          description: Not Found
      security:
        - bearerAuth: []
    put:
      tags:
        - Patient Dependents
      summary: Editar Dependente do Usuário Autenticado - ROLES 'ALL'
      description: Esta operação permite que um usuário autenticado faça alterações nas informações de um dependente já cadastrado em sua conta. Mantenha os dados atualizados para melhor gerenciamento dos perfis associados à sua conta.
      operationId: putDependents
      parameters:
        - name: id
          in: path
          description: Id do dependente a ser alterado os dados
          required: true
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                dependenteSecureId:
                  default: string | requerid
                avatarSecureId:
                  default: string | undefined
                name:
                  type: string
                birthDate:
                  default: DateTime
                legal_document_number:
                  default: string | undefined
      responses:
        '200':
          description: Success
        '401':
          description: Unauthorized access
        '404':
          description: Not Found
      security:
        - bearerAuth: []
    delete:
      tags:
        - Patient Dependents
      summary: Remover Dependente de Conta de Paciente - ROLES 'ALL'
      description: Esta operação permite que pacientes autenticados removam um dependente associado à sua conta. Certifique-se de autenticar-se antes de realizar essa ação para manter o controle de seus perfis de dependentes.
      operationId: deleteDependents
      parameters:
        - name: id
          in: path
          description: Id do dependente para remover
          required: true
          schema:
            type: string
      responses:
        '200':
          description: Success
        '401':
          description: Unauthorized access
        '404':
          description: Not Found
      security:
        - bearerAuth: []

  /v1/patient/notifications:
    get:
      tags:
        - Patient Notifications
      summary: Consultar Todas as Notificações de Pacientes - ROLES 'ALL'
      description: Esta operação permite que pacientes autenticados obtenham uma lista completa de todas as notificações associadas às suas contas. Através desta consulta, você pode acompanhar informações e atualizações importantes.
      operationId: getNotifications
      parameters:
        - name: search
          in: query
          description: Parâmetro de pesquisa
          required: false
          schema:
            type: string
            default: read, not_read
        - name: status
          in: query
          description: Parâmetro de pesquisa
          required: false
          schema:
            type: string
        - name: page
          in: query
          description: Número da página
          required: false
          schema:
            type: integer
        - name: limit
          in: query
          description: Limite de itens por página
          required: false
          schema:
            type: integer
      responses:
        '200':
          description: Success
        '401':
          description: Unauthorized access
      security:
        - bearerAuth: []

  /v1/patient/notifications/{id}:
    get:
      tags:
        - Patient Notifications
      summary: Consultar Notificação Específica do Paciente - ROLES 'ALL'
      description: Esta operação permite que pacientes autenticados obtenham detalhes sobre uma notificação específica associada à sua conta. Utilize esta consulta para obter informações detalhadas sobre eventos ou atualizações importantes.
      operationId: getNotification
      parameters:
        - name: id
          in: path
          description: Id da notificação a ser obtida
          required: true
          schema:
            type: string
      responses:
        '200':
          description: Success
        '401':
          description: Unauthorized access
        '404':
          description: Not Found
      security:
        - bearerAuth: []
    put:
      tags:
        - Patient Notifications
      summary: Visualizar Notificação do Paciente - ROLES 'ALL'
      description: Esta operação permite que pacientes autenticados visualizem uma notificação específica associada à sua conta. Utilize esta funcionalidade para ler e compreender as informações contidas na notificação.
      operationId: putNotifications
      parameters:
        - name: id
          in: path
          description: Id da notificação a ser visualizada
          required: true
          schema:
            type: string
      responses:
        '200':
          description: Success
        '401':
          description: Unauthorized access
        '404':
          description: Not Found
      security:
        - bearerAuth: []

  /v1/patient/schedule:
    get:
      tags:
        - Patient Schedule
      summary: Consultar Todos os Agendamentos de Pacientes - ROLES 'ALL'
      description: Esta operação permite que pacientes autenticados obtenham uma lista completa de todos os agendamentos associados às suas contas. Utilize esta consulta para gerenciar e acompanhar seus compromissos de maneira conveniente.
      operationId: getSchedules
      parameters:
        - name: status
          in: query
          description: Parâmetro de pesquisa
          required: false
          schema:
            type: string
        - name: statusScheduleDates
          in: query
          description: Parâmetro de pesquisa
          required: false
          schema:
            type: string
        - name: page
          in: query
          description: Número da página
          required: false
          schema:
            type: integer
        - name: limit
          in: query
          description: Limite de itens por página
          required: false
          schema:
            type: integer
      responses:
        '200':
          description: Success
        '401':
          description: Unauthorized access
      security:
        - bearerAuth: []
    post:
      tags:
        - Patient Schedule
      summary: Agendar Compromisso para o Paciente - ROLES 'ALL'
      description: Esta operação permite que pacientes autenticados cadastrem um novo agendamento em sua conta. Utilize esta funcionalidade para marcar compromissos e gerenciar sua agenda de forma eficaz.
      operationId: postSchedule
      requestBody:
        required: true
        content:
          application/json:
            schema:
              properties:
                typeConsult:
                  type: string
                  default: in_person | video_call | exam
                specialtyOrExamSecureId:
                  type: string
                patientSecureId:
                  type: string
                attendantType:
                  type: string
                partnerSecureId:
                  type: string
                local:
                  type: object
                  properties:
                    neighborhood:
                      type: string
                    city:
                      type: string
                    state:
                      type: string
                dates:
                  type: array
                  items:
                    type: object
                    properties:
                      date:
                        type: string
                      type:
                        type: string
                      value:
                        type: string
      responses:
        '200':
          description: Success
        '401':
          description: Unauthorized access
      security:
        - bearerAuth: []

  /v1/patient/schedule/{id}:
    get:
      tags:
        - Patient Schedule
      summary: Consultar Agendamento Específico - ROLES 'ALL'
      description: Esta operação permite que pacientes autenticados obtenham detalhes sobre um agendamento específico em sua conta. Utilize esta consulta para visualizar informações detalhadas sobre um compromisso agendado.
      operationId: getSchedule
      parameters:
        - name: id
          in: path
          description: Id do agendamento a ser obtido
          required: true
          schema:
            type: string
      responses:
        '200':
          description: Success
        '401':
          description: Unauthorized access
        '404':
          description: Not Found
      security:
        - bearerAuth: []

  /v1/patient/schedule-canceled/{id}:
    put:
      tags:
        - Patient Schedule
      summary: Cancelar Agendamento do Paciente - ROLES 'ALL'
      description: Esta operação permite que pacientes autenticados cancelem um agendamento em sua conta. Utilize esta funcionalidade para gerenciar seus compromissos de forma flexível e conveniente.
      operationId: deleteSchedule
      parameters:
        - name: id
          in: path
          description: Id do agendamento a ser cancelado
          required: true
          schema:
            type: string
      responses:
        '200':
          description: Success
        '401':
          description: Unauthorized access
        '404':
          description: Not Found
      security:
        - bearerAuth: []
        
  /v1/patient/onesignal-key:
    post:
      tags:
        - Patient OneSignal
      summary: Alterar Chave OneSignal de Paciente - ROLES 'PATIENT'
      description: Modifique a chave do OneSignal do paciente de maneira autenticada usando esta rota.
      operationId: postPatientOneSignal
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                oneSignalKey:
                  default: string
      security:
        - bearerAuth: []
      responses:
        '201':
          description: Created
        '401':
          description: Unauthorized access
        
  /v1/admin/users:
    get:
      tags:
        - Admin Users
      summary: Consultar Lista de Usuários Administradores Cadastrados - ROLES 'MASTER', 'ADMIN'
      description: Esta operação permite obter uma lista de todos os usuários administradores registrados no banco de dados. Utilize esta consulta para acessar informações sobre os usuários e seus perfis no sistema.
      operationId: getAdminUsers
      responses:
        '200':
          description: Success
        '401':
          description: Unauthorized access
      security:
        - bearerAuth: []
    post:
      tags:
        - Admin Users
      summary: Registrar Novo Usuário Administrador - ROLES 'MASTER', 'ADMIN'
      description: Esta operação permite que usuários autenticados registrem um administrador no banco de dados. Utilize esta funcionalidade para incluir informações e perfis de administradores no sistema.
      operationId: postAdminUsers
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                name:
                  type: string
                email:
                  type: string
                password:
                  type: string
      security:
        - bearerAuth: []
      responses:
        '200':
          description: Success
        '401':
          description: Unauthorized access

  /v1/admin/users/{id}:
    get:
      tags:
        - Admin Users
      summary: Consultar Paciente por ID - ROLES 'MASTER', 'ADMIN'
      description: Esta operação permite que usuários autenticados obtenham informações detalhadas sobre um administrador específico, com base no ID fornecido como parâmetro. Utilize esta consulta para acessar os detalhes de um administrador em particular.
      operationId: getAdminUser
      parameters:
        - name: id
          in: path
          description: Id do administrador a ser obtido
          required: true
          schema:
            type: string
      responses:
        '200':
          description: Success
        '401':
          description: Unauthorized access
        '404':
          description: Not Found
      security:
        - bearerAuth: []
    put:
      tags:
        - Admin Users
      summary: Atualizar Dados de Usuário Administrador - ROLES 'MASTER', 'ADMIN'
      description: Esta operação permite que usuários autenticados atualizem as informações de um administrador cadastrado no banco de dados. Utilize esta funcionalidade para manter os dados dos pacientes precisos e atualizados.
      operationId: putAdminUsers
      parameters:
        - name: id
          in: path
          description: Id do administrador a ser alterado
          required: true
          schema:
            type: string
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                name:
                  default: string
                email:
                  default: string
                password:
                  default: string | undefined
                legal_document_number:
                  default: string | undefined
                gender:
                  default: masculine | feminine | undefined
                birth_date:
                  default: string | undefined
                ddd_phone:
                  default: number | undefined
                phone:
                  default: number | undefined
                ddd_cell:
                  default: number | undefined
                cell:
                  default: number | undefined
                zip_code:
                  default: string | undefined
                street:
                  default: string | undefined
                number:
                  default: string | undefined
                complement:
                  default: string | undefined
                neighborhood:
                  default: string | undefined
                city:
                  default: string | undefined
                state:
                  default: string | undefined
                avatarSecureId:
                  default: string | undefined
      responses:
        '200':
          description: Success
        '401':
          description: Unauthorized access
        '404':
          description: Not Found
      security:
        - bearerAuth: []
    delete:
      tags:
        - Admin Users
      summary: Remover Paciente por ID - ROLES 'MASTER', 'ADMIN'
      description: Esta operação permite que usuários autenticados removam um administrador específico com base no ID fornecido como parâmetro. Utilize esta funcionalidade com cautela.
      operationId: deleteAdminUsers
      parameters:
        - name: id
          in: path
          description: Id do administrador a ser removida
          required: true
          schema:
            type: string
      responses:
        '200':
          description: Success
        '401':
          description: Unauthorized access
        '404':
          description: Not Found
      security:
        - bearerAuth: []

  /v1/admin/patients:
    get:
      tags:
        - Admin Patients
      summary: Consultar Lista de Pacientes Cadastrados - ROLES 'MASTER', 'ADMIN'
      description: Esta operação permite obter uma lista de todos os pacientes registrados no banco de dados. Utilize esta consulta para acessar informações sobre os pacientes e seus perfis no sistema.
      operationId: getAdminPatients
      parameters:
        - name: search
          in: query
          description: Parâmetro de pesquisa
          required: false
          schema:
            type: string
        - name: page
          in: query
          description: Número da página
          required: false
          schema:
            type: integer
        - name: limit
          in: query
          description: Limite de itens por página
          required: false
          schema:
            type: integer
      responses:
        '200':
          description: Success
        '401':
          description: Unauthorized access
      security:
        - bearerAuth: []
    post:
      tags:
        - Admin Patients
      summary: Registrar Novo Paciente - ROLES 'MASTER', 'ADMIN'
      description: Esta operação permite que usuários autenticados registrem um novo paciente no banco de dados. Utilize esta funcionalidade para incluir informações e perfis de pacientes no sistema.
      operationId: postAdminPatients
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                name:
                  type: string
                email:
                  type: string
                password:
                  type: string
                legal_document_number:
                  type: string
      security:
        - bearerAuth: []
      responses:
        '200':
          description: Success
        '401':
          description: Unauthorized access

  /v1/admin/patients/{id}:
    get:
      tags:
        - Admin Patients
      summary: Consultar Paciente por ID - ROLES 'MASTER', 'ADMIN'
      description: Esta operação permite que usuários autenticados obtenham informações detalhadas sobre um paciente específico, com base no ID fornecido como parâmetro. Utilize esta consulta para acessar os detalhes de um paciente em particular.
      operationId: getAdminPatient
      parameters:
        - name: id
          in: path
          description: Id do paciente a ser obtido
          required: true
          schema:
            type: string
      responses:
        '200':
          description: Success
        '401':
          description: Unauthorized access
        '404':
          description: Not Found
      security:
        - bearerAuth: []
    put:
      tags:
        - Admin Patients
      summary: Atualizar Dados de Paciente - ROLES 'MASTER', 'ADMIN'
      description: Esta operação permite que usuários autenticados atualizem as informações de um paciente cadastrado no banco de dados. Utilize esta funcionalidade para manter os dados dos pacientes precisos e atualizados.
      operationId: putAdminPatients
      parameters:
        - name: id
          in: path
          description: Id do paciente a ser alterado
          required: true
          schema:
            type: string
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                userExists:
                  default: boolean | undefined
                name:
                  default: string
                email:
                  default: string
                password:
                  default: string | undefined
                legal_document_number:
                  default: string | undefined
                gender:
                  default: masculine | feminine | undefined
                birth_date:
                  default: string | undefined
                ddd_phone:
                  default: number | undefined
                phone:
                  default: number | undefined
                ddd_cell:
                  default: number | undefined
                cell:
                  default: number | undefined
                zip_code:
                  default: string | undefined
                street:
                  default: string | undefined
                number:
                  default: string | undefined
                complement:
                  default: string | undefined
                neighborhood:
                  default: string | undefined
                city:
                  default: string | undefined
                state:
                  default: string | undefined
                avatarSecureId:
                  default: string | undefined
      responses:
        '200':
          description: Success
        '401':
          description: Unauthorized access
        '404':
          description: Not Found
      security:
        - bearerAuth: []
    delete:
      tags:
        - Admin Patients
      summary: Remover Paciente por ID - ROLES 'MASTER', 'ADMIN'
      description: Esta operação permite que usuários autenticados removam um paciente específico com base no ID fornecido como parâmetro. Utilize esta funcionalidade com cautela, pois a ação é irreversível e removerá permanentemente o paciente do sistema.
      operationId: deleteAdminPatient
      parameters:
        - name: id
          in: path
          description: Id do paciente a ser removida
          required: true
          schema:
            type: string
      responses:
        '200':
          description: Success
        '401':
          description: Unauthorized access
        '404':
          description: Not Found
      security:
        - bearerAuth: []

  /v1/admin/specialties:
    get:
      tags:
        - Admin Specialties
      summary: Consultar Todas as Especialidades Cadastradas - ROLES 'MASTER', 'ADMIN'
      description: Esta operação permite obter uma lista de todas as especialidades registradas no banco de dados. Utilize esta consulta para acessar informações sobre as especialidades disponíveis no sistema.
      operationId: getAdminSpecialties
      parameters:
        - name: search
          in: query
          description: Parâmetro de pesquisa
          required: false
          schema:
            type: string
        - name: page
          in: query
          description: Número da página
          required: false
          schema:
            type: integer
        - name: limit
          in: query
          description: Limite de itens por página
          required: false
          schema:
            type: integer
      responses:
        '200':
          description: Success
        '401':
          description: Unauthorized access
      security:
        - bearerAuth: []
    post:
      tags:
        - Admin Specialties
      summary: Registrar Nova Especialidade - ROLES 'MASTER', 'ADMIN'
      description: Esta operação permite que usuários autenticados cadastrem uma nova especialidade no banco de dados. Utilize esta funcionalidade para incluir informações sobre uma nova especialidade disponível no sistema.
      operationId: postAdminSpecialties
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                thumbSecureId:
                  type: string
                name:
                  type: string
                tags:
                  nullable: true
                  type: string
      security:
        - bearerAuth: []
      responses:
        '200':
          description: Success
        '401':
          description: Unauthorized access

  /v1/admin/specialties/{id}:
    get:
      tags:
        - Admin Specialties
      summary: Consultar Especialidade por ID - ROLES 'MASTER', 'ADMIN'
      description: Esta operação permite que usuários autenticados obtenham informações detalhadas sobre uma especialidade específica com base no ID fornecido como parâmetro. Utilize esta consulta para acessar detalhes específicos de uma especialidade no sistema.
      operationId: getAdminSpecialtie
      parameters:
        - name: id
          in: path
          description: Id do paciente a ser obtido
          required: true
          schema:
            type: string
      responses:
        '200':
          description: Success
        '401':
          description: Unauthorized access
        '404':
          description: Not Found
      security:
        - bearerAuth: []
    put:
      tags:
        - Admin Specialties
      summary: Atualizar Dados da Especialidade - ROLES 'MASTER', 'ADMIN'
      description: Esta operação permite que usuários autenticados atualizem as informações de uma especialidade cadastrada no banco de dados. Utilize esta funcionalidade para manter os dados das especialidades precisos e atualizados.
      operationId: putAdminSpecialties
      parameters:
        - name: id
          in: path
          description: Id da especialidade a ser alterada
          required: true
          schema:
            type: string
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                userExists:
                  default: boolean | undefined
                thumbSecureId:
                  default: string
                name:
                  default: string | undefined
                tags:
                  default: string | undefined
                active:
                  default: boolean | undefined
      responses:
        '200':
          description: Success
        '401':
          description: Unauthorized access
        '404':
          description: Not Found
      security:
        - bearerAuth: []
    delete:
      tags:
        - Admin Specialties
      summary: Remover Especialidade por ID - ROLES 'MASTER', 'ADMIN'
      description: Esta operação permite que usuários autenticados removam uma especialidade específica com base no ID fornecido como parâmetro. Tenha em mente que essa ação é irreversível e removerá permanentemente a especialidade do sistema.
      operationId: deleteAdminSpecialties
      parameters:
        - name: id
          in: path
          description: Id da especialidade a ser removida
          required: true
          schema:
            type: string
      responses:
        '200':
          description: Success
        '401':
          description: Unauthorized access
        '404':
          description: Not Found
      security:
        - bearerAuth: []

  /v1/admin/permissions:
    get:
      tags:
        - Admin Permissions
      summary: Consultar Todas as Permissões de Usuário - ROLES 'MASTER', 'ADMIN'
      description: Esta operação permite que usuários autenticados obtenham uma lista completa de todas as permissões disponíveis no sistema. Utilize esta consulta para acessar informações sobre as permissões disponíveis para os usuários.
      operationId: getAdminPermissions
      responses:
        '200':
          description: Success
        '401':
          description: Unauthorized access
      security:
        - bearerAuth: []
  
  /v1/admin/list/specialties:
    get:
      tags:
        - Admin Lists
      summary: Consultar Lista de Todas as Especialidades - ROLES 'MASTER', 'ADMIN'
      description: Esta operação permite que usuários autenticados obtenham uma lista completa de todas as especialidades disponíveis no sistema. Utilize esta consulta para acessar informações sobre as especialidades disponíveis.
      operationId: getAdminListSpecialties
      responses:
        '200':
          description: Success
        '401':
          description: Unauthorized access
      security:
        - bearerAuth: []

  /v1/admin/list/patients:
    get:
      tags:
        - Admin Lists
      summary: Consultar Lista de Todos os Pacientes - ROLES 'MASTER', 'ADMIN'
      description: Esta operação permite que usuários autenticados obtenham uma lista completa de todos os pacientes registrados no sistema. Utilize esta consulta para acessar informações sobre os pacientes cadastrados.
      operationId: getAdminListPatients
      responses:
        '200':
          description: Success
        '401':
          description: Unauthorized access
      security:
        - bearerAuth: []
    
  /v1/admin/exams:
    get:
      tags:
        - Admin Exams
      summary: Consultar Lista de Todos os Exames Cadastrados - ROLES 'MASTER', 'ADMIN'
      description: Esta operação permite que usuários autenticados obtenham uma lista completa de todos os exames registrados no banco de dados. Utilize esta consulta para acessar informações sobre os exames disponíveis no sistema.
      operationId: getAdminExams
      parameters:
        - name: search
          in: query
          description: Parâmetro de pesquisa
          required: false
          schema:
            type: string
        - name: page
          in: query
          description: Número da página
          required: false
          schema:
            type: integer
        - name: limit
          in: query
          description: Limite de itens por página
          required: false
          schema:
            type: integer
      responses:
        '200':
          description: Success
        '401':
          description: Unauthorized access
      security:
        - bearerAuth: []
    post:
      tags:
        - Admin Exams
      summary: Registrar Novo Exame - ROLES 'MASTER', 'ADMIN'
      description: Esta operação permite que usuários autenticados cadastrem um novo exame no banco de dados. Utilize esta funcionalidade para incluir informações sobre um novo exame disponível no sistema.
      operationId: postAdminExams
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                thumbSecureId:
                  type: string
                name:
                  type: string
                tags:
                  nullable: true
                  type: string
      security:
        - bearerAuth: []
      responses:
        '200':
          description: Success
        '401':
          description: Unauthorized access

  /v1/admin/exams/{id}:
    get:
      tags:
        - Admin Exams
      summary: Consultar Exame por ID - ROLES 'MASTER', 'ADMIN'
      description: Esta operação permite que usuários autenticados obtenham informações detalhadas sobre um exame específico com base no ID fornecido como parâmetro. Utilize esta consulta para acessar detalhes específicos de um exame no sistema.
      operationId: getAdminExam
      parameters:
        - name: id
          in: path
          description: Id do exame a ser obtido
          required: true
          schema:
            type: string
      responses:
        '200':
          description: Success
        '401':
          description: Unauthorized access
        '404':
          description: Not Found
      security:
        - bearerAuth: []
    put:
      tags:
        - Admin Exams
      summary: Atualizar Dados do Exame - ROLES 'MASTER', 'ADMIN'
      description: Esta operação permite que usuários autenticados atualizem as informações de um exame cadastrado no banco de dados. Utilize esta funcionalidade para manter os dados dos exames precisos e atualizados no sistema.
      operationId: putAdminExams
      parameters:
        - name: id
          in: path
          description: Id do exame a ser alterada
          required: true
          schema:
            type: string
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                userExists:
                  default: boolean | undefined
                thumbSecureId:
                  default: string | undefined
                name:
                  default: string | undefined
                tags:
                  default: string | undefined
                active:
                  default: boolean | undefined
      responses:
        '200':
          description: Success
        '401':
          description: Unauthorized access
        '404':
          description: Not Found
      security:
        - bearerAuth: []
    delete:
      tags:
        - Admin Exams
      summary: Remover Exame por ID - ROLES 'MASTER', 'ADMIN'
      description: Esta operação permite que usuários autenticados removam um exame específico com base no ID fornecido como parâmetro. Lembre-se de que essa ação é irreversível e removerá permanentemente o exame do sistema.
      operationId: deleteAdminExams
      parameters:
        - name: id
          in: path
          description: Id do exame a ser removido
          required: true
          schema:
            type: string
      responses:
        '200':
          description: Success
        '401':
          description: Unauthorized access
        '404':
          description: Not Found
      security:
        - bearerAuth: []

  /v1/admin/partners:
    get:
      tags:
        - Admin Partners
      summary: Consultar Lista de Todos os Parceiros Cadastrados - ROLES 'MASTER', 'ADMIN'
      description: Esta operação permite que usuários autenticados obtenham uma lista completa de todos os parceiros registrados no banco de dados. Utilize esta consulta para acessar informações sobre os parceiros disponíveis no sistema.
      operationId: getAdminPartners
      parameters:
        - name: search
          in: query
          description: Parâmetro de pesquisa
          required: false
          schema:
            type: string
        - name: type
          in: query
          description: Parâmetro de pesquisa
          required: false
          schema:
            type: string
        - name: page
          in: query
          description: Número da página
          required: false
          schema:
            type: integer
        - name: limit
          in: query
          description: Limite de itens por página
          required: false
          schema:
            type: integer
      responses:
        '200':
          description: Success
        '401':
          description: Unauthorized access
      security:
        - bearerAuth: []
    post:
      tags:
        - Admin Partners
      summary: Registrar Novo Parceiro - ROLES 'MASTER', 'ADMIN'
      description: Esta operação permite que usuários autenticados cadastrem um novo parceiro no banco de dados. Utilize esta funcionalidade para incluir informações sobre um novo parceiro disponível no sistema.
      operationId: postAdminPartners
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                adviceRegister:
                  default: string
                paymentMethods:
                  default: string
                queryValue:
                  default: integer
                type:
                  default: clinic | doctor | lab | hospital
                typeOfCare:
                  default: in_person | video_call | both
                avatarSecureId:
                  default: string | undefined
                specialtiesSecureIds:
                  default: string[] | undefined
                userExists:
                  default: boolean | undefined
                email:
                  default: string
                password:
                  default: string | undefined
                name:
                  default: string
      security:
        - bearerAuth: []
      responses:
        '200':
          description: Success
        '401':
          description: Unauthorized access

  /v1/admin/partners/{id}:
    get:
      tags:
        - Admin Partners
      summary: Consultar Parceiro por ID - ROLES 'MASTER', 'ADMIN'
      description: Esta operação permite que usuários autenticados obtenham informações detalhadas sobre um parceiro específico com base no ID fornecido como parâmetro. Utilize esta consulta para acessar detalhes específicos de um parceiro no sistema.
      operationId: getAdminPartner
      parameters:
        - name: id
          in: path
          description: Id do parceiro a ser obtido
          required: true
          schema:
            type: string
      responses:
        '200':
          description: Success
        '401':
          description: Unauthorized access
        '404':
          description: Not Found
      security:
        - bearerAuth: []
    put:
      tags:
        - Admin Partners
      summary: Atualizar Dados do Parceiro - ROLES 'MASTER', 'ADMIN'
      description: Esta operação permite que usuários autenticados atualizem as informações de um parceiro cadastrado no banco de dados. Utilize esta funcionalidade para manter os dados dos parceiros precisos e atualizados no sistema.
      operationId: putAdminPartners
      parameters:
        - name: id
          in: path
          description: Id do parceiro a ser alterada
          required: true
          schema:
            type: string
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                adviceRegister:
                  default: string | undefined
                paymentMethods:
                  default: string | undefined
                type:
                  default: clinic | doctor | lab | hospital | undefined
                typeOfCare:
                  default: in_person | video_call | both | undefined
                avatarSecureId:
                  default: string | undefined
                specialtiesSecureIds:
                  default: string[] | undefined
                userExists:
                  default: boolean | undefined
                email:
                  default: string | undefined
                password:
                  default: string | undefined
                name:
                  default: string | undefined
      responses:
        '200':
          description: Success
        '401':
          description: Unauthorized access
        '404':
          description: Not Found
      security:
        - bearerAuth: []
    delete:
      tags:
        - Admin Partners
      summary: Remover Parceiro por ID - ROLES 'MASTER', 'ADMIN'
      description: Esta operação permite que usuários autenticados removam um parceiro específico com base no ID fornecido como parâmetro. Lembre-se de que essa ação é irreversível e removerá permanentemente o parceiro do sistema.
      operationId: deleteAdminPartners
      parameters:
        - name: id
          in: path
          description: Id do parceiro a ser removido
          required: true
          schema:
            type: string
      responses:
        '200':
          description: Success
        '401':
          description: Unauthorized access
        '404':
          description: Not Found
      security:
        - bearerAuth: []

  /v1/admin/partners/{secure_id}/doctors:
    get:
      tags:
        - Admin Partners Doctors 
      summary: Consultar Médicos Associados ao Parceiro - ROLES 'MASTER', 'ADMIN'
      description: Esta operação permite que usuários autenticados obtenham uma lista de médicos relacionados a um parceiro específico. Utilize esta consulta para acessar informações sobre os médicos associados a determinado parceiro no sistema.
      operationId: getAdminPartnerDoctors
      parameters:
        - name: search
          in: query
          description: Parâmetro de pesquisa
          required: false
          schema:
            type: string
        - name: page
          in: query
          description: Número da página
          required: false
          schema:
            type: integer
        - name: limit
          in: query
          description: Limite de itens por página
          required: false
          schema:
            type: integer
        - name: secure_id
          in: path
          description: Id do parceiro a ser obtido os médicos
          required: true
          schema:
            type: string
      responses:
        '200':
          description: Success
        '401':
          description: Unauthorized access
        '404':
          description: Not Found
      security:
        - bearerAuth: []
    post:
      tags:
        - Admin Partners Doctors
      summary: Registrar Médicos em Parceiro - ROLES 'MASTER', 'ADMIN'
      description: Esta operação permite que usuários autenticados cadastrem médicos associados a um parceiro no banco de dados. Utilize esta funcionalidade para incluir informações sobre médicos vinculados a um parceiro disponível no sistema.
      operationId: postAdminPartnersDoctors
      parameters:
        - name: secure_id
          in: path
          description: Id do parceiro a ser cadastrado os médicos
          required: true
          schema:
            type: string
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                doctor_id:
                  default: string
      security:
        - bearerAuth: []
      responses:
        '200':
          description: Success
        '401':
          description: Unauthorized access

  /v1/admin/partners/{secure_id}/doctors/{id}:
    delete:
      tags:
        - Admin Partners Doctors
      summary: Remover Médico Associado ao Parceiro - ROLES 'MASTER', 'ADMIN'
      description: Esta operação permite que usuários autenticados removam um médico cadastrado como associado a um parceiro específico. Lembre-se de que essa ação é irreversível e removerá permanentemente o médico da lista de associados do parceiro no sistema.
      operationId: deleteAdminPartnersDoctors
      parameters:
        - name: secure_id
          in: path
          description: Id do parceiro a remover o médico
          required: true
          schema:
            type: string
        - name: id
          in: path
          description: Id do do médico a ser removido
          required: true
          schema:
            type: string
      responses:
        '200':
          description: Success
        '401':
          description: Unauthorized access
        '404':
          description: Not Found
      security:
        - bearerAuth: []

  /v1/admin/appointments:
    get:
      tags:
        - Admin Appointments
      summary: Consultar Todos os Agendamentos do Parceiro - ROLES 'MASTER', 'ADMIN'
      description: Esta operação permite que parceiros autenticados obtenham uma lista completa de todos os agendamentos relacionados à sua conta. Utilize esta consulta para gerenciar e acompanhar os agendamentos associados ao parceiro de forma conveniente.
      operationId: getAdminAppointments
      parameters:
        - name: status
          in: query
          description: Parâmetro de pesquisa
          required: false
          schema:
            type: string
        - name: page
          in: query
          description: Número da página
          required: false
          schema:
            type: integer
        - name: limit
          in: query
          description: Limite de itens por página
          required: false
          schema:
            type: integer
      responses:
        '200':
          description: Success
        '401':
          description: Unauthorized access
      security:
        - bearerAuth: []

  /v1/admin/appointments/{id}:
    get:
      tags:
        - Admin Appointments
      summary: Consultar Agendamento por ID - ROLES 'MASTER', 'ADMIN'
      description: Esta operação permite que parceiros autenticados obtenham informações detalhadas sobre um agendamento específico com base no ID fornecido como parâmetro. Utilize esta consulta para acessar detalhes específicos de um agendamento relacionado ao parceiro no sistema.
      operationId: getAdminAppointment
      parameters:
        - name: id
          in: path
          description: Id do agendamento a ser obtido
          required: true
          schema:
            type: string
      responses:
        '200':
          description: Success
        '401':
          description: Unauthorized access
        '404':
          description: Not Found
      security:
        - bearerAuth: []

  /v1/admin/appointments-doctors:
    get:
      tags:
        - Admin Appointments Doctors
      summary: Consultar Lista de Agendamentos do Médico Logado - ROLES 'MASTER', 'PARTNER'
      description: Esta operação permite que médicos autenticados obtenham uma lista de agendamentos relacionados à sua conta. Utilize esta consulta para acessar informações sobre os agendamentos associados ao médico logado no sistema.
      operationId: getAdminAppointmentsDoctors
      parameters:
        - name: search
          in: query
          description: Parâmetro de pesquisa
          required: false
          schema:
            type: string
        - name: type
          in: query
          description: Parâmetro de pesquisa
          required: false
          schema:
            type: string
        - name: page
          in: query
          description: Número da página
          required: false
          schema:
            type: integer
        - name: limit
          in: query
          description: Limite de itens por página
          required: false
          schema:
            type: integer
      responses:
        '200':
          description: Success
        '401':
          description: Unauthorized access
      security:
        - bearerAuth: []
  /v1/admin/appointments-doctors/{id}:
    put:
      tags:
        - Admin Appointments Doctors
      summary: Marcar consulta como realizada - ROLES 'MASTER', 'PARTNER'
      description: Esta operação permite que médicos autenticados marquem uma consulta como realizada. Utilize esta função para atualizar o status de consultas associadas ao médico logado no sistema.
      operationId: putAdminAppointmentsDoctors
      parameters:
        - name: id
          in: path
          description: Id do agendamento a ser obtido
          required: true
          schema:
            type: string
      responses:
        '200':
          description: Success
        '401':
          description: Unauthorized access
      security:
        - bearerAuth: []

  /v1/admin/notifications:
    post:
      tags:
        - Admin Notifications
      summary: Enviar Notificações - ROLES 'MASTER', 'ADMIN'
      description: Esta operação permite que usuários autenticados enviem notificações por meio do sistema. Utilize esta funcionalidade para comunicar informações importantes.
      operationId: postAdminNotifications
      
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                title:
                  default: string
                text:
                  default: string
      security:
        - bearerAuth: []
      responses:
        '200':
          description: Success
        '401':
          description: Unauthorized access

  /v1/admin/schedules:
    get:
      tags:
        - Admin Schedules
      summary: Consultar Todos os Agendamentos - ROLES 'MASTER', 'ADMIN'
      description: Esta operação permite que usuários autenticados obtenham uma lista completa de todos os agendamentos no sistema. Utilize esta consulta para acessar informações sobre todos os agendamentos registrados.
      operationId: getAdminSchedules
      parameters:
        - name: status
          in: query
          description: Parâmetro de pesquisa
          required: false
          schema:
            type: string
        - name: page
          in: query
          description: Número da página
          required: false
          schema:
            type: integer
        - name: limit
          in: query
          description: Limite de itens por página
          required: false
          schema:
            type: integer
      responses:
        '200':
          description: Success
        '401':
          description: Unauthorized access
      security:
        - bearerAuth: []

  /v1/admin/schedules-create-dates:
    post:
      tags:
        - Admin Schedules
      summary: Atualizar Horário do Agendamento - ROLES 'MASTER', 'ADMIN'
      description: Esta operação permite que usuários autenticados atualizem o horário de um agendamento existente no sistema. Utilize esta funcionalidade para fazer ajustes nos horários agendados de forma conveniente.
      operationId: postAdminSchedules
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                scheduleSecureId:
                  default: string
                partnerSecureId:
                  default: string
                date:
                  default: DataTime
                query_value:
                  default: integer
      security:
        - bearerAuth: []
      responses:
        '200':
          description: Success
        '401':
          description: Unauthorized access

  /v1/admin/schedules-data/{id}:
    get:
      tags:
        - Admin Schedules
      summary: Consultar Dados do Parceiro para Agendamento por ID - ROLES 'MASTER', 'ADMIN'
      description: Você pode usar esta consulta para acessar detalhes específicos do parceiro em relação a um agendamento no sistema.
      operationId: getAdminScheduleData
      parameters:
        - name: id
          in: path
          description: Id do parceiro a ser obtido
          required: true
          schema:
            type: string
      responses:
        '200':
          description: Success
        '401':
          description: Unauthorized access
        '404':
          description: Not Found
      security:
        - bearerAuth: []
    put:
      tags:
        - Admin Schedules
      summary: Atualizar Dados da hora do Agendamento - ROLES 'MASTER', 'ADMIN'
      description: Essa operação permite que usuários com as funções 'MASTER' ou 'ADMIN' atualizem o horário de um agendamento existente.
      operationId: putAdminDatesSchedules
      parameters:
        - name: id
          in: path
          description: Id da hora do agendamento a ser alterada
          required: true
          schema:
            type: string
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                queryValue: 
                  default: 2500
      responses:
        '200':
          description: Success
        '401':
          description: Unauthorized access
        '404':
          description: Not Found
      security:
        - bearerAuth: []

  /v1/admin/schedules/{id}:
    get:
      tags:
        - Admin Schedules
      summary: Consultar Agendamento por ID - ROLES 'MASTER', 'ADMIN'
      description: Esta operação permite que usuários autenticados obtenham informações detalhadas sobre um agendamento específico com base no ID fornecido como parâmetro. Utilize esta consulta para acessar detalhes específicos de um agendamento no sistema.
      operationId: getAdminSchedule
      parameters:
        - name: id
          in: path
          description: Id do agendamento a ser obtido
          required: true
          schema:
            type: string
      responses:
        '200':
          description: Success
        '401':
          description: Unauthorized access
        '404':
          description: Not Found
      security:
        - bearerAuth: []
    put:
      tags:
        - Admin Schedules
      summary: Atualizar Dados de Agendamento - ROLES 'MASTER', 'ADMIN'
      description: Esta operação permite que usuários autenticados atualizem informações de um agendamento existente no banco de dados. Utilize esta funcionalidade para manter os dados do agendamento precisos e atualizados no sistema.
      operationId: putAdminSchedules
      parameters:
        - name: id
          in: path
          description: Id do agendamento a ser alterada
          required: true
          schema:
            type: string
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                status:
                  default: waiting_backoffice | waiting_patient | approved | canceled
      responses:
        '200':
          description: Success
        '401':
          description: Unauthorized access
        '404':
          description: Not Found
      security:
        - bearerAuth: []

  /v1/admin/dependents:
    get:
      tags:
        - Admin Dependents
      summary: Consultar Lista de Todos os Dependentes Cadastrados - ROLES 'MASTER', 'ADMIN'
      description: Esta operação permite que usuários autenticados obtenham uma lista completa de todos os dependentes registrados no banco de dados. Utilize esta consulta para acessar informações sobre os dependentes cadastrados no sistema.
      operationId: getAdminDependents
      parameters:
        - name: search
          in: query
          description: Parâmetro de pesquisa
          required: false
          schema:
            type: string
        - name: parent
          in: query
          description: Parâmetro de pesquisa
          required: false
          schema:
            type: string
        - name: page
          in: query
          description: Número da página
          required: false
          schema:
            type: integer
        - name: limit
          in: query
          description: Limite de itens por página
          required: false
          schema:
            type: integer
      responses:
        '200':
          description: Success
        '401':
          description: Unauthorized access
      security:
        - bearerAuth: []
    post:
      tags:
        - Admin Dependents
      summary: Registrar Novos Dependentes - ROLES 'MASTER', 'ADMIN'
      description: Esta operação permite que usuários autenticados cadastrem novos dependentes no banco de dados. Utilize esta funcionalidade para incluir informações sobre dependentes associados a um paciente disponível no sistema.
      operationId: postAdminDependents
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                parentSecureId:
                  default: string
                avatarSecureId:
                  default: string | undefined
                specialtiesSecureIds:
                  default: string[] | undefined
                userExists:
                  default: boolean | undefined
                email:
                  default: string
                password:
                  default: string | undefined
                name:
                  default: string
      security:
        - bearerAuth: []
      responses:
        '200':
          description: Success
        '401':
          description: Unauthorized access



  /v1/admin/dependents/{id}:
    get:
      tags:
        - Admin Dependents
      summary: Consultar Dependente por ID - ROLES 'MASTER', 'ADMIN'
      description: Esta operação permite que usuários autenticados obtenham informações detalhadas sobre um dependente específico com base no ID fornecido como parâmetro. Utilize esta consulta para acessar detalhes específicos de um dependente no sistema.
      operationId: getAdminDependent
      parameters:
        - name: id
          in: path
          description: Id do dependente a ser obtido
          required: true
          schema:
            type: string
      responses:
        '200':
          description: Success
        '401':
          description: Unauthorized access
        '404':
          description: Not Found
      security:
        - bearerAuth: []
    put:
      tags:
        - Admin Dependents
      summary: Atualizar Dados de Dependente - ROLES 'MASTER', 'ADMIN'
      description: Esta operação permite que usuários autenticados atualizem informações de um dependente cadastrado no banco de dados. Utilize esta funcionalidade para manter os dados do dependente precisos e atualizados no sistema.
      operationId: putAdminDependents
      parameters:
        - name: id
          in: path
          description: Id do dependente a ser alterada
          required: true
          schema:
            type: string
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                avatarSecureId:
                  default: string | undefined
                email:
                  default: string
                name:
                  default: string
      responses:
        '200':
          description: Success
        '401':
          description: Unauthorized access
        '404':
          description: Not Found
      security:
        - bearerAuth: []
    delete:
      tags:
        - Admin Dependents
      summary: Remover Dependente por ID - ROLES 'MASTER', 'ADMIN'
      description: Esta operação permite que usuários autenticados removam um dependente específico com base no ID fornecido como parâmetro. Lembre-se de que essa ação é irreversível e removerá permanentemente o dependente do sistema.
      operationId: deleteAdminDependents
      parameters:
        - name: id
          in: path
          description: Id do dependente a ser removido
          required: true
          schema:
            type: string
      responses:
        '200':
          description: Success
        '401':
          description: Unauthorized access
        '404':
          description: Not Found
      security:
        - bearerAuth: []

  /v1/admin/template-notifications:
    get:
      tags:
        - Admin Template Notifications
      summary: Consultar Lista de Todos os Templates de Notificações Cadastrados - ROLES 'MASTER', 'ADMIN'
      description: Esta operação permite que usuários autenticados obtenham uma lista completa de todos os templates de notificações registrados no banco de dados. Utilize esta consulta para acessar informações sobre os templates de notificações cadastrados no sistema.
      operationId: getAdminTemplateNotifications
      parameters:
        - name: search
          in: query
          description: Parâmetro de pesquisa
          required: false
          schema:
            type: string
        - name: type
          in: query
          description: Parâmetro de pesquisa
          required: false
          schema:
            type: string
        - name: name
          in: query
          description: Parâmetro de pesquisa
          required: false
          schema:
            type: string
        - name: page
          in: query
          description: Número da página
          required: false
          schema:
            type: integer
        - name: limit
          in: query
          description: Limite de itens por página
          required: false
          schema:
            type: integer
      responses:
        '200':
          description: Success
        '401':
          description: Unauthorized access
      security:
        - bearerAuth: []
    post:
      tags:
        - Admin Template Notifications
      summary: Registrar Novos Templates de Notificações - ROLES 'MASTER', 'ADMIN'
      description: Esta operação permite que usuários autenticados cadastrem novos templates de notificações no banco de dados.
      operationId: postAdminTemplateNotifications
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                name:
                  default: string
                type:
                  default: push | email | sms
                subject:
                  default: string
                content:
                  default: string
      security:
        - bearerAuth: []
      responses:
        '200':
          description: Success
        '401':
          description: Unauthorized access

  /v1/admin/template-notifications/{id}:
    get:
      tags:
        - Admin Template Notifications
      summary: Consultar Template por ID - ROLES 'MASTER', 'ADMIN'
      description: Esta operação permite que usuários autenticados obtenham informações detalhadas sobre um template de notificação específico com base no ID fornecido como parâmetro. Utilize esta consulta para acessar detalhes específicos de um template de notificação no sistema.
      operationId: getAdminTemplateNotification
      parameters:
        - name: id
          in: path
          description: Id do Template a ser obtido
          required: true
          schema:
            type: string
      responses:
        '200':
          description: Success
        '401':
          description: Unauthorized access
        '404':
          description: Not Found
      security:
        - bearerAuth: []
    put:
      tags:
        - Admin Template Notifications
      summary: Atualizar Template de Notificação - ROLES 'MASTER', 'ADMIN'
      description: Esta operação permite que usuários autenticados atualizem informações de um template de notificação cadastrado no banco de dados. Utilize esta funcionalidade para manter os dados do template  precisos e atualizados no sistema.
      operationId: putAdminTemplateNotifications
      parameters:
        - name: id
          in: path
          description: Id do Template a ser alterada
          required: true
          schema:
            type: string
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                subject:
                  default: string | undefined
                content:
                  default: string | undefined
      responses:
        '200':
          description: Success
        '401':
          description: Unauthorized access
        '404':
          description: Not Found
      security:
        - bearerAuth: []
    delete:
      tags:
        - Admin Template Notifications
      summary: Remover Template de Notificação por ID - ROLES 'MASTER', 'ADMIN'
      description: Esta operação permite que usuários autenticados removam um template de notificação específico com base no ID fornecido como parâmetro. Lembre-se de que essa ação é irreversível e removerá permanentemente o template do sistema.
      operationId: deleteTemplateNotifications
      parameters:
        - name: id
          in: path
          description: Id do Template a ser removido
          required: true
          schema:
            type: string
      responses:
        '200':
          description: Success
        '401':
          description: Unauthorized access
        '404':
          description: Not Found
      security:
        - bearerAuth: []

  /v1/admin/zenvia:
    post:
      tags:
        - Zenvia
      summary: Testar Envio de SMS via Zenvia - ROLES 'MASTER'
      description: Esta operação permite testar a implementação do Zenvia para o envio de mensagens SMS. Utilize esta rota para verificar a integração do Zenvia com o envio de SMS de forma autenticada.
      operationId: postAdminZenvia
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                content:
                  default: string
                phone:
                  default: string 
      security:
        - bearerAuth: []
      responses:
        '200':
          description: Success
        '401':
          description: Unauthorized access

  /v1/admin/onesignal-push:
    post:
      tags:
        - Onesignal
      summary: Testar Envio de Notificação Push via OneSignal - ROLES 'MASTER'
      description: Esta operação permite testar a implementação do OneSignal para o envio de notificações push. Utilize esta rota para verificar a integração do OneSignal com o envio de notificações push de forma autenticada.
      operationId: postAdminOnesignalPush
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                title:
                  default: string
                content:
                  default: string
                ids:
                  default: string[]
      security:
        - bearerAuth: []
      responses:
        '200':
          description: Success
        '401':
          description: Unauthorized access

  /v1/admin/onesignal-email:
    post:
      tags:
        - Onesignal
      summary: Testar Envio de Email via OneSignal - ROLES 'MASTER'
      description: Esta operação permite testar a implementação do OneSignal para o envio de emails. Utilize esta rota para verificar a integração do OneSignal com o envio de emails de forma autenticada.
      operationId: postAdminOnesignalEmail
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                subject:
                  default: string
                content:
                  default: string
                emails:
                  default: string[]
      security:
        - bearerAuth: []
      responses:
        '200':
          description: Success
        '401':
          description: Unauthorized access

  /v1/public/join-room:
    post:
      tags:
        - Twilio Vídeo
      summary: Testar Criação de Salas de vídeo - ROLES 'MASTER'
      description: Esta operação permite testar a implementação do Twilio vídeo para criação de salas de vídeo chamadas. Utilize esta rota para verificar a integração do Twilio vídeo de forma autenticada.
      operationId: postAdminTwilioVideo
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                roomName:
                  default: string
      security:
        - bearerAuth: []
      responses:
        '201':
          description: Created
        '401':
          description: Unauthorized access

components:
  securitySchemes:
    bearerAuth:
      type: http
      scheme: bearer
