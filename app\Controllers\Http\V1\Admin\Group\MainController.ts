import type { HttpContextContract } from '@ioc:Adonis/Core/HttpContext'
import Database from '@ioc:Adonis/Lucid/Database'
import Group from 'App/Models/Group'
import ActionLogChanges from 'App/Services/ActionLogChanges'

import { StoreValidator, UpdateValidator } from 'App/Validators/Admin/Group'

export default class GroupController {
	public async index({ response, request }: HttpContextContract) {
		const { page = 1, limit = 15, search } = request.only(['page', 'limit', 'search'])

		const groups = await Group.query()
			.select('id', 'secure_id', 'name')
			.where((builder) => {
				if (search) {
					builder.whereILike('name', `%${search}%`)
					builder.orWhereHas('accrediteds', builderAccredited => {
						builderAccredited.whereILike('email', `%${search}%`)
						builderAccredited.orWhereHas('userInfo', builderUserInfo => {
							builderUserInfo.whereILike('name', `%${search}%`)
						})
					})
				}
			})
			.paginate(page, limit)

		return response.ok(groups)
	}

	public async store({ request, response }: HttpContextContract) {
		const { ...data } = await request.validate(StoreValidator)

		await Database.transaction(async (trx) => {
			const newGroup = new Group()

			newGroup.merge({
				...data,
			})
			newGroup.useTransaction(trx)
			await newGroup.save()
		})

		return response.ok({
			type: 'success',
			message: 'Grupo criado com sucesso!',
		})
	}

	public async show({ response, params }: HttpContextContract) {
		const group = await Group.query()
			.select('id', 'secure_id', 'name')
			.where('secure_id', params.id)
			.firstOrFail()

		return response.ok(group)
	}

	public async update({ response, request, params, auth }: HttpContextContract) {
		const userLogged = auth.user!

		const dataRequest = await request.validate(UpdateValidator)

		const { ...data } = dataRequest

		const group = await Group.query().where('secure_id', params.id).firstOrFail()

		await Database.transaction(async (trx) => {
			await ActionLogChanges.saveLogGroupChanges({
				groupChange: group,
				groupChangedData: {
					...dataRequest
				},
				userLogged,
				trx
			})

			group.merge({
				...data,
			})
			group.useTransaction(trx)
			await group.save()
		})

		return response.ok({
			type: 'success',
			message: 'Grupo atualizado com sucesso!',
		})
	}

	public async destroy({ response, params }: HttpContextContract) {
		const group = await Group.query()
			.where('secure_id', params.id)
			.withCount('accrediteds')
			.firstOrFail()

		if (group.$extras.accrediteds_count > 0) {
			return response.badRequest({
				type: 'warning',
				message: 'Esse grupo possui usuários ou credenciados vinculados.',
			})
		}

		await group.delete()

		return response.ok({
			type: 'success',
			message: 'Grupo removido com sucesso!',
		})
	}
}
