import BaseSchema from '@ioc:Adonis/Lucid/Schema'

export default class extends BaseSchema {
	protected tableName = 'cities'

	public async up() {
		this.schema.createTable(this.tableName, (table) => {
			table.increments('id')
			table.string('nome')
			table
				.integer('uf')
				.unsigned()
				.references('id')
				.inTable('states')
				.onUpdate('CASCADE')
				.onDelete('CASCADE')

			table.dateTime('created_at', { useTz: true })
			table.dateTime('updated_at', { useTz: true })
		})
	}

	public async down() {
		this.schema.dropTable(this.tableName)
	}
}
