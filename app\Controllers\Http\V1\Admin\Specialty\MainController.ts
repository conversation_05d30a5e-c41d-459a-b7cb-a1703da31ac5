import type { HttpContextContract } from '@ioc:Adonis/Core/HttpContext'
import Database from '@ioc:Adonis/Lucid/Database'
import Specialty from 'App/Models/Specialty'
import Upload from 'App/Models/Upload'
import ActionLogChanges from 'App/Services/ActionLogChanges'

import { StoreValidator, UpdateValidator } from 'App/Validators/Admin/Specialty'

export default class SpecialtyController {
	public async index({ response, request }: HttpContextContract) {
		const { page = 1, limit = 15, search } = request.only(['page', 'limit', 'search'])

		const specialties = await Specialty.query()
			.select('id', 'secure_id', 'name', 'tags', 'label_advice', 'active')
			.where((builder) => {
				if (search) {
					// builder.whereRaw('MATCH (name,tags) AGAINST (LOWER(?) IN BOOLEAN MODE)', [`${search}`])
					builder.whereRaw(`LOWER(name) like LOWER('%${search}%')`)
					builder.orWhereRaw(`LOWER(tags) like LOWER('%${search}%')`)
				}
			})
			.orderBy('name', 'asc')
			.paginate(page, limit)

		return response.ok(specialties)
	}

	public async store({ request, response }: HttpContextContract) {
		const { thumbSecureId, ...data } = await request.validate(StoreValidator)

		await Database.transaction(async (trx) => {
			const newSpecialty = new Specialty()

			const thumb = await Upload.query().where('secure_id', thumbSecureId).firstOrFail()

			newSpecialty.merge({
				...data,
				thumbId: thumb.id,
			})
			newSpecialty.useTransaction(trx)
			await newSpecialty.save()
		})

		return response.ok({
			type: 'success',
			message: 'Especialidade criada com sucesso!',
		})
	}

	public async show({ response, params }: HttpContextContract) {
		const specialty = await Specialty.query()
			.select('id', 'thumb_id', 'secure_id', 'name', 'tags', 'label_advice')
			.where('secure_id', params.id)
			.preload('thumb', (builderThumb) => {
				builderThumb.select('id', 'secure_id', 'url', 'name')
			})
			.firstOrFail()

		return response.ok(specialty)
	}

	public async update({ response, request, params, auth }: HttpContextContract) {
		const userLogged = auth.user!

		const requestData = await request.validate(UpdateValidator)

		const { thumbSecureId, ...data } = requestData

		const specialty = await Specialty.query().where('secure_id', params.id).firstOrFail()

		await Database.transaction(async (trx) => {
			await ActionLogChanges.saveLogSpecialtyChanges({
				specialtyChange: specialty,
				specialtyChangedData: {
					...requestData
				},
				userLogged,
				trx
			})

			if (thumbSecureId) {
				const thumb = await Upload.query().where('secure_id', thumbSecureId).firstOrFail()

				specialty.merge({
					thumbId: thumb.id,
				})
			}

			specialty.merge({
				...data,
			})
			specialty.useTransaction(trx)
			await specialty.save()
		})

		return response.ok({
			type: 'success',
			message: 'Especialidade atualizada com sucesso!',
		})
	}

	public async destroy({ response, params }: HttpContextContract) {
		const specialty = await Specialty.query().where('secure_id', params.id).firstOrFail()

		await specialty.delete()

		return response.ok({
			type: 'success',
			message: 'Especialidade removida com sucesso!',
		})
	}
}
