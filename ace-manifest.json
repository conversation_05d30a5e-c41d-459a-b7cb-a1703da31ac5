{"commands": {"create:holidays": {"settings": {"loadApp": true, "stayAlive": false}, "commandPath": "./commands/CreateHolidays", "commandName": "create:holidays", "description": "Usa um arquivo json, do github, para popular a tabela holidays.", "args": [], "aliases": [], "flags": [{"name": "confirmation", "propertyName": "confirmation", "type": "boolean"}]}, "create:permissions": {"settings": {"loadApp": true, "stayAlive": false}, "commandPath": "./commands/CreatePermissions", "commandName": "create:permissions", "description": "Cria as permissions ainda não criadas e apaga as que foram removidas do controle de rules", "args": [], "aliases": [], "flags": [{"name": "confirmation", "propertyName": "confirmation", "type": "boolean"}]}, "create:roles": {"settings": {"loadApp": true, "stayAlive": false}, "commandPath": "./commands/CreateRoles", "commandName": "create:roles", "description": "<PERSON><PERSON> as roles ainda não criadas e a<PERSON><PERSON> as que foram removidas do controle de roles", "args": [], "aliases": [], "flags": [{"name": "confirmation", "propertyName": "confirmation", "type": "boolean"}]}, "create:templatenotifications": {"settings": {"loadApp": true, "stayAlive": false}, "commandPath": "./commands/CreateTemplateNotifications", "commandName": "create:templatenotifications", "description": "Popula a tabela de templatenotification com as notificações a serem atualizadas", "args": [], "aliases": [], "flags": [{"name": "confirmation", "propertyName": "confirmation", "type": "boolean"}]}, "create:user": {"settings": {"loadApp": true, "stayAlive": false}, "commandPath": "./commands/CreateUsers", "commandName": "create:user", "description": "<PERSON>ria um usuário com as roles que desejar", "args": [], "aliases": [], "flags": [{"name": "confirmation", "propertyName": "confirmation", "type": "boolean"}]}, "deploy": {"settings": {"loadApp": true, "stayAlive": false}, "commandPath": "./commands/Deploy", "commandName": "deploy", "description": "Sincroniza os dados da aplicação como as roles, permissões e templates de email.", "args": [], "aliases": [], "flags": []}, "import:appointments_history": {"settings": {"loadApp": true, "stayAlive": false}, "commandPath": "./commands/ImportAppointmentsHistory", "commandName": "import:appointments_history", "description": "", "args": [], "aliases": [], "flags": []}, "set:default_status_to_old_user_infos": {"settings": {"loadApp": true, "stayAlive": false}, "commandPath": "./commands/SetDefaultStatusToOldUserInfos", "commandName": "set:default_status_to_old_user_infos", "description": "Seta um status padrão para os usuários antigos, onde os status estão nulos.", "args": [], "aliases": [], "flags": [{"name": "confirmation", "propertyName": "confirmation", "type": "boolean"}]}, "sync:database": {"settings": {"loadApp": true, "stayAlive": false}, "commandPath": "./commands/SyncDatabase", "commandName": "sync:database", "description": "Pega os dados da planilha e insere no banco de dados.", "args": [], "aliases": [], "flags": []}, "update:schedules_city_state": {"settings": {"loadApp": true, "stayAlive": false}, "commandPath": "./commands/UpdateSchedulesCityState", "commandName": "update:schedules_city_state", "description": "Atualiza os campos city e state dos schedules a partir dos dados dos pacientes", "args": [], "aliases": [], "flags": [{"name": "default-city", "propertyName": "defaultCity", "type": "string", "description": "Cidade padrão para usar quando o paciente não tiver cidade definida"}, {"name": "default-state", "propertyName": "defaultState", "type": "string", "description": "Estado padrão para usar quando o paciente não tiver estado definido"}]}, "update:status_appointment": {"settings": {"loadApp": true, "stayAlive": false}, "commandPath": "./commands/UpdateStatusAppointment", "commandName": "update:status_appointment", "description": "Atualiza os status de agendamentos e solicitação de agendamentos de aguardando para aprovado e cancelados para cancelados pelo paciente.", "args": [], "aliases": [], "flags": [{"name": "confirmation", "propertyName": "confirmation", "type": "boolean"}]}, "dump:rcfile": {"settings": {}, "commandPath": "@adonisjs/core/build/commands/DumpRc", "commandName": "dump:rcfile", "description": "Dump contents of .adonisrc.json file along with defaults", "args": [], "aliases": [], "flags": []}, "list:routes": {"settings": {"loadApp": true, "stayAlive": true}, "commandPath": "@adonisjs/core/build/commands/ListRoutes/index", "commandName": "list:routes", "description": "List application routes", "args": [], "aliases": [], "flags": [{"name": "verbose", "propertyName": "verbose", "type": "boolean", "description": "Display more information"}, {"name": "reverse", "propertyName": "reverse", "type": "boolean", "alias": "r", "description": "Reverse routes display"}, {"name": "methods", "propertyName": "methodsFilter", "type": "array", "alias": "m", "description": "Filter routes by method"}, {"name": "patterns", "propertyName": "patternsFilter", "type": "array", "alias": "p", "description": "Filter routes by the route pattern"}, {"name": "names", "propertyName": "namesFilter", "type": "array", "alias": "n", "description": "Filter routes by route name"}, {"name": "json", "propertyName": "json", "type": "boolean", "description": "Output as JSON"}, {"name": "table", "propertyName": "table", "type": "boolean", "description": "Output as Table"}, {"name": "max-width", "propertyName": "max<PERSON><PERSON><PERSON>", "type": "number", "description": "Specify maximum rendering width. Ignored for JSON Output"}]}, "generate:key": {"settings": {}, "commandPath": "@adonisjs/core/build/commands/GenerateKey", "commandName": "generate:key", "description": "Generate a new APP_KEY secret", "args": [], "aliases": [], "flags": []}, "repl": {"settings": {"loadApp": true, "environment": "repl", "stayAlive": true}, "commandPath": "@adonisjs/repl/build/commands/AdonisRepl", "commandName": "repl", "description": "Start a new REPL session", "args": [], "aliases": [], "flags": []}, "db:seed": {"settings": {"loadApp": true}, "commandPath": "@adonisjs/lucid/build/commands/DbSeed", "commandName": "db:seed", "description": "Execute database seeders", "args": [], "aliases": [], "flags": [{"name": "connection", "propertyName": "connection", "type": "string", "description": "Define a custom database connection for the seeders", "alias": "c"}, {"name": "interactive", "propertyName": "interactive", "type": "boolean", "description": "Run seeders in interactive mode", "alias": "i"}, {"name": "files", "propertyName": "files", "type": "array", "description": "Define a custom set of seeders files names to run", "alias": "f"}, {"name": "compact-output", "propertyName": "compactOutput", "type": "boolean", "description": "A compact single-line output"}]}, "db:wipe": {"settings": {"loadApp": true}, "commandPath": "@adonisjs/lucid/build/commands/DbWipe", "commandName": "db:wipe", "description": "Drop all tables, views and types in database", "args": [], "aliases": [], "flags": [{"name": "connection", "propertyName": "connection", "type": "string", "description": "Define a custom database connection", "alias": "c"}, {"name": "drop-views", "propertyName": "dropViews", "type": "boolean", "description": "Drop all views"}, {"name": "drop-types", "propertyName": "dropTypes", "type": "boolean", "description": "Drop all custom types (Postgres only)"}, {"name": "force", "propertyName": "force", "type": "boolean", "description": "Explicitly force command to run in production"}]}, "db:truncate": {"settings": {"loadApp": true}, "commandPath": "@adonisjs/lucid/build/commands/DbTruncate", "commandName": "db:truncate", "description": "Truncate all tables in database", "args": [], "aliases": [], "flags": [{"name": "connection", "propertyName": "connection", "type": "string", "description": "Define a custom database connection", "alias": "c"}, {"name": "force", "propertyName": "force", "type": "boolean", "description": "Explicitly force command to run in production"}]}, "make:model": {"settings": {"loadApp": true}, "commandPath": "@adonisjs/lucid/build/commands/MakeModel", "commandName": "make:model", "description": "Make a new Lucid model", "args": [{"type": "string", "propertyName": "name", "name": "name", "required": true, "description": "Name of the model class"}], "aliases": [], "flags": [{"name": "migration", "propertyName": "migration", "type": "boolean", "alias": "m", "description": "Generate the migration for the model"}, {"name": "controller", "propertyName": "controller", "type": "boolean", "alias": "c", "description": "Generate the controller for the model"}, {"name": "factory", "propertyName": "factory", "type": "boolean", "alias": "f", "description": "Generate a factory for the model"}]}, "make:migration": {"settings": {"loadApp": true}, "commandPath": "@adonisjs/lucid/build/commands/MakeMigration", "commandName": "make:migration", "description": "Make a new migration file", "args": [{"type": "string", "propertyName": "name", "name": "name", "required": true, "description": "Name of the migration file"}], "aliases": [], "flags": [{"name": "connection", "propertyName": "connection", "type": "string", "description": "The connection flag is used to lookup the directory for the migration file"}, {"name": "folder", "propertyName": "folder", "type": "string", "description": "Pre-select a migration directory"}, {"name": "create", "propertyName": "create", "type": "string", "description": "Define the table name for creating a new table"}, {"name": "table", "propertyName": "table", "type": "string", "description": "Define the table name for altering an existing table"}]}, "make:seeder": {"settings": {}, "commandPath": "@adonisjs/lucid/build/commands/MakeSeeder", "commandName": "make:seeder", "description": "Make a new Seeder file", "args": [{"type": "string", "propertyName": "name", "name": "name", "required": true, "description": "Name of the seeder class"}], "aliases": [], "flags": []}, "make:factory": {"settings": {}, "commandPath": "@adonisjs/lucid/build/commands/MakeFactory", "commandName": "make:factory", "description": "Make a new factory", "args": [{"type": "string", "propertyName": "model", "name": "model", "required": true, "description": "The name of the model"}], "aliases": [], "flags": [{"name": "model-path", "propertyName": "modelPath", "type": "string", "description": "The path to the model"}, {"name": "exact", "propertyName": "exact", "type": "boolean", "description": "Create the factory with the exact name as provided", "alias": "e"}]}, "migration:run": {"settings": {"loadApp": true}, "commandPath": "@adonisjs/lucid/build/commands/Migration/Run", "commandName": "migration:run", "description": "Migrate database by running pending migrations", "args": [], "aliases": [], "flags": [{"name": "connection", "propertyName": "connection", "type": "string", "description": "Define a custom database connection", "alias": "c"}, {"name": "force", "propertyName": "force", "type": "boolean", "description": "Explicitly force to run migrations in production"}, {"name": "dry-run", "propertyName": "dryRun", "type": "boolean", "description": "Do not run actual queries. Instead view the SQL output"}, {"name": "compact-output", "propertyName": "compactOutput", "type": "boolean", "description": "A compact single-line output"}, {"name": "disable-locks", "propertyName": "disableLocks", "type": "boolean", "description": "Disable locks acquired to run migrations safely"}]}, "migration:rollback": {"settings": {"loadApp": true}, "commandPath": "@adonisjs/lucid/build/commands/Migration/Rollback", "commandName": "migration:rollback", "description": "Rollback migrations to a specific batch number", "args": [], "aliases": [], "flags": [{"name": "connection", "propertyName": "connection", "type": "string", "description": "Define a custom database connection", "alias": "c"}, {"name": "force", "propertyName": "force", "type": "boolean", "description": "Explictly force to run migrations in production"}, {"name": "dry-run", "propertyName": "dryRun", "type": "boolean", "description": "Do not run actual queries. Instead view the SQL output"}, {"name": "batch", "propertyName": "batch", "type": "number", "description": "Define custom batch number for rollback. Use 0 to rollback to initial state"}, {"name": "compact-output", "propertyName": "compactOutput", "type": "boolean", "description": "A compact single-line output"}, {"name": "disable-locks", "propertyName": "disableLocks", "type": "boolean", "description": "Disable locks acquired to run migrations safely"}]}, "migration:status": {"settings": {"loadApp": true}, "commandPath": "@adonisjs/lucid/build/commands/Migration/Status", "commandName": "migration:status", "description": "View migrations status", "args": [], "aliases": [], "flags": [{"name": "connection", "propertyName": "connection", "type": "string", "description": "Define a custom database connection", "alias": "c"}]}, "migration:reset": {"settings": {"loadApp": true}, "commandPath": "@adonisjs/lucid/build/commands/Migration/Reset", "commandName": "migration:reset", "description": "Rollback all migrations", "args": [], "aliases": [], "flags": [{"name": "connection", "propertyName": "connection", "type": "string", "description": "Define a custom database connection", "alias": "c"}, {"name": "force", "propertyName": "force", "type": "boolean", "description": "Explicitly force command to run in production"}, {"name": "dry-run", "propertyName": "dryRun", "type": "boolean", "description": "Do not run actual queries. Instead view the SQL output"}, {"name": "disable-locks", "propertyName": "disableLocks", "type": "boolean", "description": "Disable locks acquired to run migrations safely"}]}, "migration:refresh": {"settings": {"loadApp": true}, "commandPath": "@adonisjs/lucid/build/commands/Migration/Refresh", "commandName": "migration:refresh", "description": "Rollback and migrate database", "args": [], "aliases": [], "flags": [{"name": "connection", "propertyName": "connection", "type": "string", "description": "Define a custom database connection", "alias": "c"}, {"name": "force", "propertyName": "force", "type": "boolean", "description": "Explicitly force command to run in production"}, {"name": "dry-run", "propertyName": "dryRun", "type": "boolean", "description": "Do not run actual queries. Instead view the SQL output"}, {"name": "seed", "propertyName": "seed", "type": "boolean", "description": "Run seeders"}, {"name": "disable-locks", "propertyName": "disableLocks", "type": "boolean", "description": "Disable locks acquired to run migrations safely"}]}, "migration:fresh": {"settings": {"loadApp": true}, "commandPath": "@adonisjs/lucid/build/commands/Migration/Fresh", "commandName": "migration:fresh", "description": "Drop all tables and re-migrate the database", "args": [], "aliases": [], "flags": [{"name": "connection", "propertyName": "connection", "type": "string", "description": "Define a custom database connection", "alias": "c"}, {"name": "force", "propertyName": "force", "type": "boolean", "description": "Explicitly force command to run in production"}, {"name": "seed", "propertyName": "seed", "type": "boolean", "description": "Run seeders"}, {"name": "drop-views", "propertyName": "dropViews", "type": "boolean", "description": "Drop all views"}, {"name": "drop-types", "propertyName": "dropTypes", "type": "boolean", "description": "Drop all custom types (Postgres only)"}, {"name": "disable-locks", "propertyName": "disableLocks", "type": "boolean", "description": "Disable locks acquired to run migrations safely"}]}, "make:mailer": {"settings": {}, "commandPath": "@adonisjs/mail/build/commands/MakeMailer", "commandName": "make:mailer", "description": "Make a new mailer class", "args": [{"type": "string", "propertyName": "name", "name": "name", "required": true, "description": "Name of the mailer class"}], "aliases": [], "flags": []}, "swagger:generate": {"settings": {"loadApp": true}, "commandPath": "adonis5-swagger/build/commands/GenerateSwaggerFile", "commandName": "swagger:generate", "description": "Generate swagger file", "args": [], "aliases": [], "flags": []}}, "aliases": {}}