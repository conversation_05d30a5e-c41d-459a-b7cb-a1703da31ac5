import { schema, rules, CustomMessages } from '@ioc:Adonis/Core/Validator'
import { HttpContextContract } from '@ioc:Adonis/Core/HttpContext'

export default class StoreValidator {
	constructor(protected ctx: HttpContextContract) {}

	public schema = schema.create({
		userExists: schema.boolean.optional(),
		name: schema.string([rules.trim()]),
		email: schema.string([rules.trim()]),
		password: schema.string.optional([rules.trim(), rules.requiredWhen('userExists', '!=', true)]),
		legal_document_number: schema.string.optional([
			rules.trim(),
			rules.cpf(),
			rules.requiredWhen('userExists', '!=', true),
		]),
		gender: schema.enum.optional(['masculine', 'feminine'] as const),
		birth_date: schema.date.optional(),
		ddd_phone: schema.number.optional([rules.trim()]),
		phone: schema.number.optional([rules.trim()]),
		ddd_cell: schema.number.optional([rules.trim()]),
		cell: schema.number.optional([rules.trim()]),
		zip_code: schema.string.optional([rules.trim()]),
		street: schema.string.optional([rules.trim()]),
		number: schema.string.optional([rules.trim()]),
		complement: schema.string.optional([rules.trim()]),
		neighborhood: schema.string.optional([rules.trim()]),
		city: schema.string.optional([rules.trim()]),
		state: schema.string.optional([rules.trim()]),
		avatarSecureId: schema.string.optional([
			rules.trim(),
			rules.exists({ table: 'uploads', column: 'secure_id' }),
		]),
		origin: schema.string.optional([rules.trim()]),
		partnerSecureId: schema.string.optional([
			rules.trim(),
			rules.exists({ table: 'users', column: 'secure_id' }),
		]),
		isActive: schema.boolean.optional(),
	})

	private getFields = (field) => {
		const fieldI18N = this.ctx.i18n.formatMessage(`fields.${field}`)

		const isI18N = !fieldI18N.match(/translation/)

		if (isI18N && fieldI18N) {
			return fieldI18N
		} else {
			return field
		}
	}

	private getValidator = (rule, field) => {
		const fieldI18N = this.ctx.i18n.formatMessage(`validator.shared.${field}.${rule}`)
		const isI18N = fieldI18N.match(/translation/)

		if (isI18N === null && fieldI18N) {
			return `validator.shared.${field}.${rule}`
		} else {
			return `validator.shared.${rule}`
		}
	}

	public messages: CustomMessages = {
		'*': (field, rule, arrayExpressionPointer, options) => {
			try {
				return this.ctx.i18n.formatMessage(this.getValidator(rule, field), {
					field: this.getFields(field),
				})
			} catch (_) {
				return this.ctx.i18n.validatorMessages()['*'](field, rule, arrayExpressionPointer, options)
			}
		},
		'email.unique': 'Já existe um usuário associado a esse email.',
		'legal_document_number.unique': 'Já existe um usuário associado a esse cpf.',
	}
}
