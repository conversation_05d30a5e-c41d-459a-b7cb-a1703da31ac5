import type { HttpContextContract } from '@ioc:Adonis/Core/HttpContext'
import Env from '@ioc:Adonis/Core/Env'
import AccessToken, { VideoGrant } from 'twilio/lib/jwt/AccessToken'
import Twi<PERSON> from 'twilio';
import { v4 as uuidv4 } from 'uuid';

export default class MainController {
  public async joinRoom({ response, request }: HttpContextContract) {
    const { roomName } = request.only(['roomName'])

		const twilioClient = Twilio(
			Env.get('TWILIO_API_KEY_SID'),
			Env.get('TWILIO_API_KEY_SECRET'),
			{ accountSid: Env.get('TWILIO_ACCOUNT_SID') }
		)

		const findOrCreateRoom = async (roomName: string) => {
			try {
				await twilioClient.video.v1.rooms(roomName).fetch()
			} catch (error) {
				if (error.code == 20404) {
					await twilioClient.video.v1.rooms.create({
						uniqueName: roomName,
						type: "go", //type go é uma sala gratuita para até dois participantes
					})
				} else {
					throw error;
				}
			}
		}

    try {
      await findOrCreateRoom(roomName);

      const token = new AccessToken(
        Env.get('TWILIO_ACCOUNT_SID'),
        Env.get('TWILIO_API_KEY_SID'),
        Env.get('TWILIO_API_KEY_SECRET'),
        { identity: uuidv4() }
      )

      const videoGrant = new VideoGrant({
        room: roomName
      })

      token.addGrant(videoGrant)

      response.created({
        token: token.toJwt()
      })
    } catch (error) {
      response.badRequest({
        type: 'error'
      })
    }
  }
}
