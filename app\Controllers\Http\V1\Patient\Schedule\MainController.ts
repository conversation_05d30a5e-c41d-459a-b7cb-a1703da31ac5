import type { HttpContextContract } from '@ioc:Adonis/Core/HttpContext'
import Database from '@ioc:Adonis/Lucid/Database'
import Exam from 'App/Models/Exam'
import Schedule from 'App/Models/Schedule'
import ScheduleDatesRequest from 'App/Models/ScheduleDatesRequest'
import Specialty from 'App/Models/Specialty'
import Upload from 'App/Models/Upload'
import User from 'App/Models/User'
import NotificationServices from 'App/Services/NotificationServices'
import { StoreValidator } from 'App/Validators/Patient/Schedule'
import { DateTime } from 'luxon'

export default class ScheduleController {
	public async index({ request, response, auth }: HttpContextContract) {
		const {
			page = 1,
			limit = 5,
			status,
			statusScheduleDates,
		} = request.only(['page', 'limit', 'status', 'statusScheduleDates'])

		const userLogged = auth.user!
		const schedule = await Schedule.query()
			.where('userId', userLogged.id)
			.andWhere((builder) => {
				if (status) {
					builder.andWhereIn('status', status)
				}
			})
			.preload('specialty', (builder) => {
				builder.select('id', 'name')
			})
			.preload('exam', (builder) => {
				builder.select('id', 'name')
			})
			.preload('scheduleDatesRequests', (builder) => {
				builder.select('id', 'partner_id', 'date', 'partner_type', 'date_type', 'value', 'status')
				builder.preload('partner', (builderPartner) => {
					builderPartner.select('id')
					builderPartner.preload('userInfo', (builderUserInfo) => {
						builderUserInfo.select('name')
					})
				})
				if (statusScheduleDates) {
					builder.andWhereIn('status', statusScheduleDates)
				}
			})
			.preload('patient', (builder) => {
				builder.select('id')
				builder.preload('userInfo', (builderUserInfo) => {
					builderUserInfo.select('name')
				})
			})
			.paginate(page, limit)

		return response.ok(schedule)
	}

	public async store({ request, response, auth }: HttpContextContract) {
		const {
			patientSecureId,
			partnerSecureId,
			typeConsult,
			attendantType,
			dates,
			local,
			specialtyOrExamSecureId,
			imagesLabs
		} = await request.validate(StoreValidator)
		const userLogged = auth.user!

		let schedule: Schedule;

		await Database.transaction(async (trx) => {
			schedule = new Schedule()

			const patient = await User.query().where('secure_id', patientSecureId).firstOrFail()

			schedule.merge({
				userId: userLogged.id,
				patientId: patient.id,
				...local,
				status: 'waiting_backoffice',
				typeConsult,
			})

			schedule.useTransaction(trx)
			await schedule.save()

			if (typeConsult !== 'exam' && specialtyOrExamSecureId) {
				const specialty = await Specialty.query()
					.where('secure_id', specialtyOrExamSecureId)
					.firstOrFail()

				schedule.merge({
					specialtyId: specialty.id,
				})

				schedule.useTransaction(trx)
				await schedule.save()
			} else {
				const exam = await Exam.query().where('secure_id', specialtyOrExamSecureId).firstOrFail()

				schedule.merge({
					examId: exam.id,
				})

				schedule.useTransaction(trx)
				await schedule.save()

				const uploads = await Upload.query()
					.whereIn('secure_id', imagesLabs)

				await schedule.useTransaction(trx).related('uploads').sync(uploads.map(({ id }) => id))
			}

			let partnerId: number | null = null
			let partnerQueryValue: number | undefined
			if (partnerSecureId && attendantType !== 'helloMed') {
				const partner = await User.query()
					.where('secure_id', partnerSecureId)
					.preload('userInfo')
					.firstOrFail()

				partnerId = partner.id
				partnerQueryValue = partner.userInfo.queryValue
			}

			for await (const date of dates) {
				const scheduleDate = new ScheduleDatesRequest()

				scheduleDate.merge({
					partnerId,
					scheduleId: schedule.id,
					dateType: date.type,
					partnerType: attendantType,
					type: 'patient',
					status: 'to_check',
					date: date.date,
					value: date.value,
					queryValue: partnerQueryValue!
				})

				scheduleDate.useTransaction(trx)
				await scheduleDate.save()
			}
		})

		const infosNotification = await Database.query()
			.select(
				'schedules.id',
				'schedules.secure_id as scheduleSecureId',
				'schedules.type_consult as type',
				'schedules.status',
				'schedules.neighborhood',
				'schedules.city',
				'schedules.state',
				'schedules.date_canceled as dateCanceled',
				'schedules.type_canceled',
				'schedules.motive_canceled',
				'schedules.patient_id as patientId',
				'users.email as patientEmail',
				'responsable.email as responsableEmail',
				'user_infos.name as patientName',
				'user_infos.ddd_cell as patientDDDCell',
				'user_infos.cell as patientCell',
				'responsable_infos.ddd_cell as responsableDDDCell',
				'responsable_infos.cell as responsableCell',
				'user_infos.one_signal_key as patientOneSignalKey',
				'responsable_infos.one_signal_key as responsableOneSignalKey',
				'schedule_dates_requests.date as realizationDate',
				'partner_infos.id as idPartner',
				'partner_infos.name as namePartner',
				'partners.email as emailPartner',
				'specialties.name as partnerSpecialty',
				'exams.name as examName',
				'exams.tags as examTags',
			)
			.from('schedules')
			.where('schedules.secure_id', schedule!.secureId)
			.leftJoin('users', 'users.id', 'schedules.patient_id')
			.leftJoin('user_infos', 'user_infos.user_id', 'users.id')
			.leftJoin('schedule_dates_requests', 'schedule_dates_requests.schedule_id', 'schedules.id')
			.leftJoin('users as partners', 'partners.id', 'schedule_dates_requests.partner_id')
			.leftJoin('user_infos as partner_infos', 'partner_infos.user_id', 'partners.id')
			.leftJoin('users as responsable', 'responsable.id', 'users.parent_id')
			.leftJoin('user_infos as responsable_infos', 'responsable_infos.user_id', 'responsable.id')
			.leftJoin('user_specialties', 'user_specialties.user_id', 'partners.id')
			.leftJoin('specialties', 'specialties.id', 'user_specialties.specialty_id')
			.leftJoin('exams', 'exams.id', 'schedules.exam_id')
			.firstOrFail()

		const scheduleData = {
			patient: {
				id: infosNotification.patientId,
				name: infosNotification.patientName,
				email: infosNotification.responsableEmail ?? infosNotification.patientEmail,
				dddCell: infosNotification.responsableDDDCell ?? infosNotification.patientDDDCell,
				cell: infosNotification.responsableCell ?? infosNotification.patientCell,
				oneSignalKey: infosNotification.responsableOneSignalKey ?? infosNotification.patientOneSignalKey,
			},
			laboratory: {
				name: infosNotification.namePartner,
				exam: infosNotification.examName,
				preparation: infosNotification.partnerSpecialty,
				email: infosNotification.emailPartner
			},
			doctor: {
				id: infosNotification.idPartner,
				name: infosNotification.namePartner,
				specialty: infosNotification.partnerSpecialty,
				email: infosNotification.emailPartner,
			},
			schedule: {
				typeAppointment: infosNotification.type,
				date: infosNotification.realizationDate,
				realizationDate: infosNotification.realizationDate,
				cancelDate: infosNotification.dateCanceled,
			},
			place: {
				state: infosNotification.state,
				neighborhood: infosNotification.neighborhood,
				city: infosNotification.city,
			}
		}

		if (typeConsult == 'exam') {
			await NotificationServices.sendNotifications(scheduleData, 'examSolicitation')
		} else {
			await NotificationServices.sendNotifications(scheduleData, 'consultSolicitation')
		}

		return response.ok({
			type: 'success',
			message: 'Solicitação criada com sucesso!',
		})
	}

	public async show({ params, request, response, auth }: HttpContextContract) {
		const userLogged = auth.user!
		const { statusScheduleDates } = request.only(['statusScheduleDates'])

		const schedule = await Schedule.query()
			.select('id', 'patient_id', 'specialty_id', 'exam_id', 'secure_id', 'type_consult')
			.where('userId', userLogged.id)
			.andWhere('secureId', params.id)
			.preload('patient', (builder) => {
				builder.preload('userInfo', (builderPatient) => {
					builderPatient.select('name')
				})
			})
			.preload('specialty', (builder) => {
				builder.select('name')
			})
			.preload('exam', (builder) => {
				builder.select('name')
			})
			.preload('uploads', (builder) => {
				builder.select('url')
			})
			.preload('scheduleDatesRequests', (builder) => {
				builder.preload('partner', (builderPartner) => {
					builderPartner.select('id', 'secureId', 'avatarId')
					builderPartner.preload('avatar', (builderAvatar) => {
						builderAvatar.select('url')
					})
					builderPartner.preload('userInfo', (builderUserInfo) => {
						builderUserInfo.select(
							'name',
							'advice_register',
							'payment_methods',
							'query_value',
							'type_of_care',
							'ddd_phone',
							'phone',
							'ddd_cell',
							'cell',
							'street',
							'number',
							'complement',
							'neighborhood',
							'city',
							'state'
						)
					})
				})
				if (statusScheduleDates) {
					builder.andWhereIn('status', statusScheduleDates)
				}
				builder.orderBy('date', 'asc')
			})
			.firstOrFail()

		return response.ok(schedule)
	}

	public async destroy({ params, response }: HttpContextContract) {
		const schedule = await Schedule.query()
			.where('secure_id', params.id)
			.firstOrFail()

		await Database.transaction(async trx => {
			schedule.merge({
				status: 'canceled_by_patient',
				dateCanceled: DateTime.now(),
				typeCanceled: 'patient',
			})
			schedule.useTransaction(trx)
			await schedule.save()

			const infosNotification = await Database.query()
				.select(
					'schedules.id',
					'schedules.secure_id as scheduleSecureId',
					'schedules.type_consult as type',
					'schedules.status',
					'schedules.neighborhood',
					'schedules.city',
					'schedules.state',
					'schedules.date_canceled as dateCanceled',
					'schedules.type_canceled',
					'schedules.motive_canceled',
					'schedules.patient_id as patientId',
					'users.email as patientEmail',
					'responsable.email as responsableEmail',
					'user_infos.name as patientName',
					'user_infos.ddd_cell as patientDDDCell',
					'user_infos.cell as patientCell',
					'responsable_infos.ddd_cell as responsableDDDCell',
					'responsable_infos.cell as responsableCell',
					'user_infos.one_signal_key as patientOneSignalKey',
					'responsable_infos.one_signal_key as responsableOneSignalKey',
					'schedule_dates_requests.date as realizationDate',
					'schedule_dates_requests.partner_type as partnerType',
					'schedule_dates_requests.query_value as paymentDate',
					'partner_infos.id as idPartner',
					'partner_infos.name as namePartner',
					'partners.email as emailPartner',
					'specialties.name as partnerSpecialty',
					'exams.name as examName',
					'exams.tags as examTags',
				)
				.from('schedules')
				.where('schedules.id', schedule.id)
				.leftJoin('users', 'users.id', 'schedules.patient_id')
				.leftJoin('user_infos', 'user_infos.user_id', 'users.id')
				.leftJoin('schedule_dates_requests', 'schedule_dates_requests.schedule_id', 'schedules.id')
				.leftJoin('users as partners', 'schedules.id', 'schedule_dates_requests.schedule_id')
				.leftJoin('user_infos as partner_infos', 'partner_infos.user_id', 'partners.id')
				.leftJoin('users as responsable', 'responsable.id', 'users.parent_id')
				.leftJoin('user_infos as responsable_infos', 'responsable_infos.user_id', 'responsable.id')
				.leftJoin('user_specialties', 'user_specialties.user_id', 'partners.id')
				.leftJoin('specialties', 'specialties.id', 'user_specialties.specialty_id')
				.leftJoin('exams', 'exams.id', 'schedules.exam_id')
				.firstOrFail()

			const scheduleDataNotification = {
				patient: {
					id: infosNotification.patientId,
					name: infosNotification.patientName,
					email: infosNotification.responsableEmail ?? infosNotification.patientEmail,
					dddCell: infosNotification.responsableDDDCell ?? infosNotification.patientDDDCell,
					cell: infosNotification.responsableCell ?? infosNotification.patientCell,
					oneSignalKey: infosNotification.responsableOneSignalKey ?? infosNotification.patientOneSignalKey,
				},
				laboratory: {
					name: infosNotification.namePartner,
					exam: infosNotification.examName,
					preparation: infosNotification.partnerSpecialty,
					email: infosNotification.emailPartner
				},
				doctor: {
					id: infosNotification.idPartner,
					name: infosNotification.namePartner,
					specialty: infosNotification.partnerSpecialty,
					email: infosNotification.emailPartner,
				},
				schedule: {
					typeAppointment: infosNotification.type,
					date: schedule.createdAt.toString(),
					realizationDate: '',
					cancelDate: infosNotification.dateCanceled,
				},
				place: {
					state: infosNotification.state,
					neighborhood: infosNotification.neighborhood,
					city: infosNotification.city,
				}
			}

			if (infosNotification.type == 'exam') {
				await NotificationServices.sendNotifications(scheduleDataNotification, 'examCancelForPatient')
				await NotificationServices.sendNotifications(scheduleDataNotification, 'examCancelForPatientSendBackoffice')
			} else {
				await NotificationServices.sendNotifications(scheduleDataNotification, 'consultCancelForPatient')
				await NotificationServices.sendNotifications(scheduleDataNotification, 'consultCancelForPatientSendBackoffice')
			}
		})

		return response.ok({
			type: 'success',
			message: 'Agendamento cancelado com sucesso!',
		})
	}
}
