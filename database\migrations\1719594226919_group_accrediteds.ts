import BaseSchema from '@ioc:Adonis/Lucid/Schema'

export default class extends BaseSchema {
	protected tableName = 'group_accrediteds'

	public async up() {
		this.schema.createTable(this.tableName, (table) => {
			table.increments('id')
			table.integer('group_id')
				.unsigned()
				.references('id')
				.inTable('groups')
				.onUpdate('CASCADE')
				.onDelete('RESTRICT')
			table.integer('accredited_id')
				.unsigned()
				.references('id')
				.inTable('users')
				.onUpdate('CASCADE')
				.onDelete('RESTRICT')

			/**
			 * Uses timestamptz for PostgreSQL and DATETIME2 for MSSQL
			 */
			table.timestamp('created_at')
			table.timestamp('updated_at')
		})
	}

	public async down() {
		this.schema.dropTable(this.tableName)
	}
}
