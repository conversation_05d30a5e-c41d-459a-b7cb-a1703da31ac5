import type { HttpContextContract } from '@ioc:Adonis/Core/HttpContext'
import { schema } from '@ioc:Adonis/Core/Validator'
import Database from '@ioc:Adonis/Lucid/Database'

import Exam from 'App/Models/Exam'
import Role from 'App/Models/Role'
import User from 'App/Models/User'
import Group from 'App/Models/Group'
import Upload from 'App/Models/Upload'
import UserInfo from 'App/Models/UserInfo'
import Specialty from 'App/Models/Specialty'

import ActionLogChanges from 'App/Services/ActionLogChanges'

import { StoreValidator, UpdateValidator } from 'App/Validators/Admin/Accredited'

export default class AccreditedController {
	public async index({ response, request, auth }: HttpContextContract) {
		const schemaValidator = schema.create({
			page: schema.number.optional(),
			limit: schema.number.optional(),
			search: schema.string.optional(),
			type: schema.enum.optional(['patient', 'dependent', 'doctor', 'clinic', 'lab', 'hospital', 'admin', 'all'] as const),
			status: schema.enum.optional(['active', 'inactive', 'punctual'] as const),
		})

		const {
			page = 1,
			limit = 15,
			search,
			type,
			status
		} = await request.validate({
			schema: schemaValidator
		})

		const userLogged = auth.user!

		await userLogged.load('roles')


		const accrediteds = await User.query()
			.select('users.id', 'users.secure_id', 'users.email', 'users.type', 'users.show_accredited_in_app')
			.whereHas('roles', (builder) => {
				builder.whereIn('slug', ['accredited'])
			})
			.andWhere((builder) => {
				if (search) {
					builder.whereRaw(`LOWER(users.email) like LOWER('%${search}%')`)
					builder.orWhereHas('userInfo', builder => {
						builder.whereRaw(`LOWER(name) like LOWER('%${search}%')`)
						builder.orWhereRaw(`LOWER(zip_code) like LOWER('%${search}%')`)
					})
				}
			})
			.andWhere((builder) => {
				if (type && type != 'all') {
					builder.where('users.type', type)
				}

				if (status) {
					builder.whereHas('userInfo', builder => {
						builder.where('status', status)
					})
				}
			})
			.andWhereNot('users.id', userLogged.id)
			.preload('userInfo')
			.join('user_infos', 'user_infos.user_id', 'users.id')
			.orderBy('user_infos.name', 'asc')
			.paginate(page, limit)

		return response.ok(accrediteds)
	}

	public async store({ request, response, auth }: HttpContextContract) {
		const dataRequest = await request.validate(StoreValidator)

		const {
			adviceRegister,
			paymentMethods,
			queryValue,
			accreditedValue,
			type,
			typeOfCare,
			status,
			avatarSecureId,
			groupSecureId,
			specialtiesSecureIds,
			examsSecureIds,
			userExists,
			email,
			password,
			...dataUserInfo
		} = dataRequest

		const userLogged = auth.user!

		const queryValueAtt = queryValue * 100
		const accreditedValueAtt = accreditedValue * 100

		const user = await User.query()
			.where('email', email)
			.preload('roles')
			.preload('userInfo')
			.first()

		if (userExists && user && user.roles.find((role) => role.name === 'ACCREDITED')) {
			return response.badRequest({
				type: 'warning',
				message: 'Esse usuário já foi cadastrado como credenciado!',
			})
		}

		await Database.transaction(async (trx) => {
			if (userExists && user) {
				const role = await Role.query().where('name', 'ACCREDITED').firstOrFail()

				if (!user.roles.find((role) => role.name === 'ACCREDITED')) {
					await user.useTransaction(trx).related('roles').attach([role.id])
				}

				await ActionLogChanges.saveLogUserChanges({
					userChange: user,
					userChangedData: {
						...dataRequest
					},
					userLogged,
					trx
				})

				const userInfo = user.userInfo
				userInfo.merge({
					adviceRegister,
					paymentMethods,
					queryValue: queryValueAtt,
					accreditedValue: accreditedValueAtt,
					typeOfCare,
				})
				userInfo.useTransaction(trx)
				await userInfo.save()

				if (specialtiesSecureIds) {
					const specialties = await Specialty.query().whereIn('secure_id', specialtiesSecureIds)

					await user
						.useTransaction(trx)
						.related('specialties')
						.sync(specialties.map((specialty) => specialty.id))
				}

				if (examsSecureIds) {
					const exams = await Exam.query().whereIn('secure_id', examsSecureIds)

					await user
						.useTransaction(trx)
						.related('exams')
						.sync(exams.map((exam) => exam.id))
				}

				if (groupSecureId) {
					const group = await Group.query()
						.where('secure_id', groupSecureId)
						.firstOrFail()

					const existingRelation = await user
						.related('accreditedGroups')
						.query()
						.whereNot('group_id', group.id)
						.first()

					if (existingRelation) {
						await existingRelation.useTransaction(trx).related('accrediteds').detach([user.id])
					}

					await group.useTransaction(trx).related('accrediteds').attach([user.id])
				}
			} else {
				const newUser = new User()

				if (avatarSecureId) {
					const avatar = await Upload.query().where('secure_id', avatarSecureId).firstOrFail()

					newUser.merge({
						avatarId: avatar.id,
					})
				}

				newUser.merge({
					email,
					password,
					type,
				})
				newUser.useTransaction(trx)
				await newUser.save()

				const newUserInfo = new UserInfo()
				newUserInfo.merge({
					userId: newUser.id,
					adviceRegister,
					paymentMethods,
					queryValue: queryValueAtt,
					accreditedValue: accreditedValueAtt,
					typeOfCare,
					status,
					...dataUserInfo,
				})
				newUserInfo.useTransaction(trx)
				await newUserInfo.save()

				if (specialtiesSecureIds) {
					const specialties = await Specialty.query().whereIn('secure_id', specialtiesSecureIds)

					await newUser
						.useTransaction(trx)
						.related('specialties')
						.sync(specialties.map((specialty) => specialty.id))
				}

				if (examsSecureIds) {
					const exams = await Exam.query().whereIn('secure_id', examsSecureIds)

					await newUser
						.useTransaction(trx)
						.related('exams')
						.sync(exams.map((exam) => exam.id))
				}

				if (groupSecureId) {
					const group = await Group.query()
						.where('secure_id', groupSecureId)
						.firstOrFail()

					await group.useTransaction(trx).related('accrediteds').attach([newUser.id])
				}

				const rolesSearch = await Role.query().where('name', 'ACCREDITED')
				await newUser
					.useTransaction(trx)
					.related('roles')
					.sync(rolesSearch.map((role) => role.id))
			}
		})

		return response.ok({
			type: 'success',
			message: 'Credenciado cadastrado com sucesso!',
		})
	}

	public async show({ response, params }: HttpContextContract) {
		const user = await User.query()
			.select('id', 'avatar_id', 'secure_id', 'email', 'type')
			.where('secure_id', params.id)
			.andWhereHas('roles', (builder) => {
				builder.whereIn('slug', ['accredited'])
			})
			.preload('avatar', (builderAvatar) => {
				builderAvatar.select('id', 'secure_id', 'url', 'name')
			})
			.preload('userInfo')
			.preload('specialties', (builderSpecialty) => {
				builderSpecialty.select('id', 'name', 'secure_id')
			})
			.preload('exams', (builderExam) => {
				builderExam.select('id', 'name', 'secure_id')
			})
			.preload('accreditedGroups', builderGroup => {
				builderGroup.select('id', 'secure_id', 'name')
			})
			.firstOrFail()

		return response.ok(user)
	}

	public async update({ response, request, params, auth }: HttpContextContract) {
		const dataRequest = await request.validate(UpdateValidator)

		const {
			adviceRegister,
			typeOfCare,
			type,
			status,
			paymentMethods,
			queryValue,
			accreditedValue,
			avatarSecureId,
			groupSecureId,
			specialtiesSecureIds,
			examsSecureIds,
			email,
			password,
			...dataUser
		} = dataRequest

		const userLogged = auth.user!

		const queryValueAtt = queryValue! * 100
		const accreditedValueAtt = accreditedValue! * 100

		await userLogged.load('roles')

		const user = await User.query()
			.where('secure_id', params.id)
			.andWhereHas('roles', (builder) => {
				builder.whereIn('slug', ['accredited'])
			})
			.preload('userInfo')
			.firstOrFail()

		await Database.transaction(async (trx) => {
			await ActionLogChanges.saveLogUserChanges({
				userChange: user,
				userChangedData: {
					...dataRequest
				},
				userLogged,
				trx
			})

			if (avatarSecureId) {
				const avatar = await Upload.query().where('secure_id', avatarSecureId).firstOrFail()

				user.merge({
					avatarId: avatar.id,
				})
			}

			if (specialtiesSecureIds) {
				const specialties = await Specialty.query().whereIn('secure_id', specialtiesSecureIds)

				await user
					.useTransaction(trx)
					.related('specialties')
					.sync(specialties.map((specialty) => specialty.id))
			}

			if (examsSecureIds) {
				const exams = await Exam.query().whereIn('secure_id', examsSecureIds)

				await user
					.useTransaction(trx)
					.related('exams')
					.sync(exams.map((exam) => exam.id))
			}

			if (groupSecureId) {
				const group = await Group.query()
					.where('secure_id', groupSecureId)
					.firstOrFail()

				const existingRelation = await user
					.related('accreditedGroups')
					.query()
					.whereNot('group_id', group.id)
					.first()

				if (existingRelation) {
					await existingRelation.useTransaction(trx).related('accrediteds').detach([user.id])
				}

				await group.useTransaction(trx).related('accrediteds').attach([user.id])
			}

			user.merge({ email, password, type })
			user.useTransaction(trx)
			await user.save()

			const userInfo = user.userInfo
			userInfo.merge({
				adviceRegister,
				typeOfCare,
				paymentMethods,
				status,
				queryValue: queryValueAtt,
				accreditedValue: accreditedValueAtt,
				...dataUser,
			})
			userInfo.useTransaction(trx)
			await userInfo.save()
		})

		const typeReturn = type || user.type
		let messageReturn

		switch (typeReturn) {
			case 'doctor':
				messageReturn = 'Médico atualizado com sucesso!'
				break

			case 'clinic':
				messageReturn = 'Clínica atualizada com sucesso!'
				break

			case 'lab':
				messageReturn = 'Laboratório atualizado com sucesso!'
				break

			default:
				messageReturn = 'Credenciado atualizado com sucesso!'
				break
		}

		return response.ok({
			type: 'success',
			message: messageReturn,
		})
	}

	public async destroy({ response, params, auth }: HttpContextContract) {
		const userLogged = auth.user!

		await userLogged.load('roles')

		const user = await User.query()
			.where('secure_id', params.id)
			.andWhereHas('roles', (builder) => {
				builder.whereIn('slug', ['accredited'])
			})
			.firstOrFail()

		user.merge({ deleted: true })
		await user.save()

		return response.ok({
			type: 'success',
			message: 'Credenciado removido com sucesso!',
		})
	}
}
