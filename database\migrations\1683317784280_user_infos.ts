import BaseSchema from '@ioc:Adonis/Lucid/Schema'

export default class extends BaseSchema {
	protected tableName = 'user_infos'

	public async up() {
		this.schema.createTable(this.tableName, (table) => {
			table.increments('id')
			table
				.integer('user_id')
				.unsigned()
				.references('id')
				.inTable('users')
				.onUpdate('CASCADE')
				.onDelete('CASCADE')
			table.string('name')
			table.string('legal_document_number')
			table.enu('gender', ['masculine', 'feminine'])
			table.date('birth_date')
			table.integer('ddd_phone').unsigned()
			table.integer('phone').unsigned()
			table.integer('ddd_cell').unsigned()
			table.integer('cell').unsigned()
			table.string('zip_code')
			table.string('street')
			table.string('number')
			table.string('complement')
			table.string('neighborhood')
			table.string('city')
			table.string('state')

			table.dateTime('created_at', { useTz: true })
			table.dateTime('updated_at', { useTz: true })
		})
	}

	public async down() {
		this.schema.dropTable(this.tableName)
	}
}
