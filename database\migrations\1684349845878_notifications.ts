import BaseSchema from '@ioc:Adonis/Lucid/Schema'

export default class extends BaseSchema {
	protected tableName = 'notifications'

	public async up() {
		this.schema.createTable(this.tableName, (table) => {
			table.increments('id')
			table.string('secure_id')
			table
				.integer('user_id')
				.unsigned()
				.references('id')
				.inTable('users')
				.onUpdate('CASCADE')
				.onDelete('CASCADE')
			table.string('title')
			table.text('text')
			table.enu('status', ['read', 'not_read'])
			table.dateTime('send_in')
			table.dateTime('read_at')

			table.dateTime('created_at')
			table.dateTime('updated_at')
		})
	}

	public async down() {
		this.schema.dropTable(this.tableName)
	}
}
