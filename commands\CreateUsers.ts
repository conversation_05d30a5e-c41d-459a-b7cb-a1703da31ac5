import { BaseCommand, flags } from '@adonisjs/core/build/standalone'
import Database from '@ioc:Adonis/Lucid/Database'
import Role from 'App/Models/Role'

export default class CreateUser extends BaseCommand {
  public static commandName = 'create:user'

  public static title = 'Criar usuário'
  public static description = 'Cria um usuário com as roles que desejar'

  @flags.boolean()
	public confirmation: boolean = true

  public static settings = {
    loadApp: true,
    stayAlive: false,
  }

  public async run() {
    const { default: User } = await import('App/Models/User')
    const { default: UserInfo } = await import('App/Models/UserInfo')
    try {
      this.ui.sticker()
        .add(this.colors.bold(this.colors.cyan(CreateUser.title)))
        .add('')
        .add(this.colors.gray(CreateUser.description))
        .render()

      const roles = await Role.query()

      const email = await this.prompt.ask('Informe o email do usuário.', { validate: (value) => !!value })
      const name = await this.prompt.ask('Informe o nome do usuário.', { validate: (value) => !!value })
      const password = await this.prompt.secure('Informe a senha do usuário.', { validate: (value) => !!value })
      const rolesNames = await this.prompt.multiple('Selecione as roles que o usuário vai possuir', roles.map(role => role.name))

      await Database.transaction(async trx => {
        const user = new User()
        const userInfo = new UserInfo()

        user.merge({
          email,
          password,
          type: 'admin',
        })
        user.useTransaction(trx)
        await user.save()

        userInfo.merge({
          userId: user.id,
          name,
        })
        userInfo.useTransaction(trx)
        await userInfo.save()

        const roles = await Role.query()
          .whereIn('name', rolesNames)

        await user.useTransaction(trx).related('roles').sync(roles.map(role => role.id))
      })

      await Database.manager.closeAll()
      this.logger.log(`${this.colors.green('Conta criada com sucesso!')} ${this.ui.icons.tick}`)
    } catch (error) {
      this.logger.logError(error)
      this.logger.log(`${this.colors.red('Ocorreu um erro ao criar o usuário')} ${this.ui.icons.cross}`)
    }
  }
}
