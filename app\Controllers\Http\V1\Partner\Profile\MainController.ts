import type { HttpContextContract } from '@ioc:Adonis/Core/HttpContext'
import Database from '@ioc:Adonis/Lucid/Database'
import Upload from 'App/Models/Upload'
import User from 'App/Models/User'
import ActionLogChanges from 'App/Services/ActionLogChanges'

import { UpdateValidator } from 'App/Validators/Admin/Partner'

export default class PartnerController {
  public async index({ response, auth }: HttpContextContract) {
    const userLogged = auth.user!

    const partner = await User.query()
      .where('secure_id', userLogged.secureId)
      .select('users.id', 'users.secure_id', 'users.email', 'users.type')
      .whereHas('roles', (builder) => {
        builder.whereIn('slug', ['partner'])
      })
      .preload('userInfo')
      .join('user_infos', 'user_infos.user_id', 'users.id')
      .first()

    return response.ok(partner)
  }

  public async update({ response, request, auth }: HttpContextContract) {
    const dataRequest = await request.validate(UpdateValidator)

    const {
      avatarSecureId,
      email,
      password,
      ...dataUser
    } = dataRequest

    const userLogged = auth.user!

    const user = await User.query()
      .where('secure_id', userLogged.secureId)
      .andWhereHas('roles', (builder) => {
        builder.whereIn('slug', ['partner'])
      })
      .preload('userInfo')
      .firstOrFail()

    await Database.transaction(async (trx) => {
      await ActionLogChanges.saveLogUserChanges({
        userChange: user,
        userChangedData: {
          ...dataRequest
        },
        userLogged,
        trx
      })

      if (avatarSecureId) {
        const avatar = await Upload.query().where('secure_id', avatarSecureId).firstOrFail()

        user.merge({
          avatarId: avatar.id,
        })
      }

      user.merge({ email, password })
      user.useTransaction(trx)
      await user.save()

      const userInfo = user.userInfo
      userInfo.merge({
        ...dataUser,
      })
      userInfo.useTransaction(trx)
      await userInfo.save()
    })

    return response.ok({
      type: 'success',
      message: 'Parceiro atualizado com sucesso!',
    })
  }
}