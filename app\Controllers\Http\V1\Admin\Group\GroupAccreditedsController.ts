import type { HttpContextContract } from '@ioc:Adonis/Core/HttpContext'
import User from 'App/Models/User'

export default class GroupAccreditedsController {
	public async index({ response, params, request }: HttpContextContract) {
		const { secure_id } = params
		const { page = 1, limit = 20, search } = request.only(['page', 'limit', 'search'])

		const users = await User.query()
			.whereHas('accreditedGroups', builder => {
				builder.where('secure_id', secure_id)
			})
			.andWhere((builder) => {
				if (search) {
					builder.whereILike('email', `%${search}%`)
					builder.orWhereHas('userInfo', builder => {
						builder.whereILike('name', `%${search}%`)
					})
				}
			})
			.preload('userInfo', builder => {
				builder.select('name')
			})
			.join('user_infos', 'user_infos.user_id', 'users.id')
			.orderBy('user_infos.name', 'asc')
			.select('users.*')
			.paginate(page, limit)

		return response.ok(users)
	}

	// public async store({ response, request, params, auth }: HttpContextContract) {
	// 	const userLogged = auth.user!

	// 	const { secure_id } = params
	// 	const { userSecureId } = request.only(['userSecureId'])

	// 	const group = await Group.query()
	// 		.where('secure_id', secure_id)
	// 		.preload('accrediteds')
	// 		.first()

	// 	if (!group) {
	// 		return response.notFound({
	// 			type: 'warning',
	// 			message: 'Grupo não encontrado.',
	// 		})
	// 	}

	// 	const user = await User.query()
	// 		.where('secure_id', userSecureId)
	// 		.first()

	// 	if (!user) {
	// 		return response.notFound({
	// 			type: 'warning',
	// 			message: 'Credenciado não encontrado',
	// 		})
	// 	}

	// 	const existingRelation = await group
	// 		.related('accrediteds')
	// 		.query()
	// 		.where('accredited_id', user.id)
	// 		.first()

	// 	if (existingRelation) {
	// 		return response.badRequest({
	// 			type: 'warning',
	// 			message: 'Credenciado já associado à grupo.',
	// 		})
	// 	}

	// 	await Database.transaction(async trx => {
	// 		await ActionLogChanges.saveLogGroupChanges({
	// 			groupChange: group,
	// 			groupChangedData: {
	// 				accreditedsSecureIds: [...group.accrediteds.map(accreditedOld => accreditedOld.secureId), user.secureId]
	// 			},
	// 			userLogged,
	// 			trx
	// 		})

	// 		await group.useTransaction(trx).related('accrediteds').attach([user.id])
	// 	})

	// 	return response.ok({
	// 		type: 'success',
	// 		message: 'Credenciado adicionado com sucesso!',
	// 	})
	// }

	// public async destroy({ response, params, auth }: HttpContextContract) {
	// 	const userLogged = auth.user!
	// 	const { secure_id, id } = params

	// 	const group = await Group.query()
	// 		.where('secure_id', secure_id)
	// 		.preload('accrediteds')
	// 		.first()

	// 	if (!group) {
	// 		return response.notFound({
	// 			type: 'warning',
	// 			message: 'Grupo não encontrado.',
	// 		})
	// 	}

	// 	const user = await User.query()
	// 		.where('secure_id', id)
	// 		.first()

	// 	if (!user) {
	// 		return response.notFound({
	// 			type: 'warning',
	// 			message: 'Credenciado não encontrado.',
	// 		})
	// 	}

	// 	const existingRelation = await group
	// 		.related('accrediteds')
	// 		.query()
	// 		.where('accredited_id', user.id)
	// 		.first()

	// 	if (!existingRelation) {
	// 		return response.badRequest({
	// 			type: 'warning',
	// 			message: 'Credenciado não associado à grupo.',
	// 		})
	// 	}

	// 	await Database.transaction(async trx => {
	// 		await ActionLogChanges.saveLogGroupChanges({
	// 			groupChange: group,
	// 			groupChangedData: {
	// 				accreditedsSecureIds: [...group.accrediteds.filter(accreditedFilter => accreditedFilter.secureId !== user.secureId).map(accreditedOld => accreditedOld.secureId)]
	// 			},
	// 			userLogged,
	// 			trx
	// 		})

	// 		await group.related('accrediteds').detach([user.id])
	// 	})

	// 	return response.ok({
	// 		type: 'success',
	// 		message: 'Credenciado removido com sucesso do grupo!',
	// 	})
	// }
}
