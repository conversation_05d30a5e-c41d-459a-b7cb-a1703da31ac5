import Route from '@ioc:Adonis/Core/Route'
import { isRoles } from 'App/Contants/Roles'

Route.group(() => {
	Route.resource('list/specialties', 'V1/Admin/List/ListSpecialtiesController').middleware({
		index: ['auth', `${isRoles(['MASTER', 'ADMIN'])}`],
	})

	Route.resource('list/patients', 'V1/Admin/List/ListPatientsController').middleware({
		index: ['auth', `${isRoles(['MASTER', 'ADMIN'])}`],
	})

	Route.resource('list/partners', 'V1/Admin/List/ListPartnersController').middleware({
		index: ['auth', `${isRoles(['MASTER', 'ADMIN'])}`],
	})

	Route.resource('list/exams', 'V1/Admin/List/ListExamsController').middleware({
		index: ['auth', `${isRoles(['MASTER', 'ADMIN'])}`],
	})

	Route.resource('list/accrediteds', 'V1/Admin/List/ListAccreditedsController').middleware({
		index: ['auth', `${isRoles(['MASTER', 'ADMIN'])}`],
	})

	Route.resource('list/groups', 'V1/Admin/List/ListGroupsController').middleware({
		index: ['auth', `${isRoles(['MASTER', 'ADMIN'])}`],
	})

	Route.get(
		'list/patients-by-cpf/:patient/:cpf',
		'V1/Admin/List/ListPatientsController.listByCPF'
	).middleware(['auth', `${isRoles(['MASTER', 'ADMIN'])}`])
})
