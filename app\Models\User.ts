import { DateTime } from 'luxon'
import Hash from '@ioc:Adonis/Core/Hash'
import {
	column,
	beforeSave,
	BaseModel,
	beforeCreate,
	belongsTo,
	BelongsTo,
	manyToMany,
	ManyToMany,
	hasMany,
	HasMany,
	HasOne,
	hasOne,
	beforeFetch,
	beforeFind,
	ModelQueryBuilderContract,
} from '@ioc:Adonis/Lucid/Orm'

import { v4 as uuid } from 'uuid'
import Role from './Role'
import Permission from './Permission'
import Upload from './Upload'
import Specialty from './Specialty'
import UserInfo from './UserInfo'
import Exam from './Exam'
import ActionLog from './ActionLog'
import Group from './Group'

type UserQuery = ModelQueryBuilderContract<typeof User>

export default class User extends BaseModel {

	@column({ isPrimary: true, serializeAs: null })
	public id: number

	@column()
	public secureId: string

	@column({ serializeAs: null })
	public parentId: number

	@column({ serializeAs: null })
	public avatarId: number

	@column()
	public email: string

	@column({ serializeAs: null })
	public password: string

	@column()
	public rememberMeToken: string | null

	@column()
	public type: 'patient' | 'dependent' | 'doctor' | 'clinic' | 'lab' | 'hospital' | 'admin'

	@column({ serialize: (value) => Boolean(value) })
	public showAccreditedInApp: boolean

	@column({ serialize: (value) => Boolean(value) })
	public isFirstAccess: boolean

	@column({ serializeAs: null })
	public deleted: boolean

	@column({ serializeAs: 'isActive' })
	public is_active: boolean

	@belongsTo(() => User, {
		foreignKey: 'parentId',
		localKey: 'id',
	})
	public parent: BelongsTo<typeof User>

	@hasMany(() => User, {
		foreignKey: 'parentId',
		localKey: 'id',
	})
	public dependents: HasMany<typeof User>

	@belongsTo(() => Upload, {
		foreignKey: 'avatarId',
		localKey: 'id',
	})
	public avatar: BelongsTo<typeof Upload>

	@hasOne(() => UserInfo, {
		foreignKey: 'userId',
	})
	public userInfo: HasOne<typeof UserInfo>

	@hasMany(() => ActionLog, {
		foreignKey: 'chargedId',
		localKey: 'id',
		onQuery: query => query.where('type', 'user')
	})
	public logs: HasMany<typeof ActionLog>

	@manyToMany(() => Role, {
		pivotTable: 'user_roles',
	})
	public roles: ManyToMany<typeof Role>

	@manyToMany(() => Permission, {
		pivotTable: 'user_permissions',
	})
	public permissions: ManyToMany<typeof Permission>

	@manyToMany(() => Specialty, {
		pivotTable: 'user_specialties',
	})
	public specialties: ManyToMany<typeof Specialty>

	@manyToMany(() => Exam, {
		pivotTable: 'user_exams',
	})
	public exams: ManyToMany<typeof Exam>

	@manyToMany(() => User, { ///
		pivotTable: 'clinic_doctor',
		localKey: 'id',
		pivotForeignKey: 'clinic_id',
		relatedKey: 'id',
		pivotRelatedForeignKey: 'doctor_id',
	})
	public doctors: ManyToMany<typeof User>;

	@manyToMany(() => User, { ///
		pivotTable: 'clinic_doctor',
		localKey: 'id',
		pivotForeignKey: 'doctor_id',
		relatedKey: 'id',
		pivotRelatedForeignKey: 'clinic_id',
	})
	public clinic: ManyToMany<typeof User>;

	@manyToMany(() => User, { ///
		pivotTable: 'user_partners',
		localKey: 'id',
		pivotForeignKey: 'user_id',
		relatedKey: 'id',
		pivotRelatedForeignKey: 'partner_id',
	})
	public partners: ManyToMany<typeof User>

	@manyToMany(() => User, { ///
		pivotTable: 'user_partners',
		localKey: 'id',
		pivotForeignKey: 'partner_id',
		relatedKey: 'id',
		pivotRelatedForeignKey: 'user_id',
	})
	public patients: ManyToMany<typeof User>

	@manyToMany(() => Group, {
		pivotTable: 'group_accrediteds',
		localKey: 'id',
		pivotForeignKey: 'accredited_id',
		relatedKey: 'id',
		pivotRelatedForeignKey: 'group_id',
	})
	public accreditedGroups: ManyToMany<typeof Group>

	@column.dateTime({
		autoCreate: true,
		serialize: (value: DateTime) => {
			if (value) return value.toFormat('yyyy-MM-dd HH:mm:ss').replace(/\s+/g, 'T')
			return
		},
	})
	public createdAt: DateTime

	@column.dateTime({
		autoCreate: true,
		autoUpdate: true,
		serialize: (value: DateTime) => {
			if (value) return value.toFormat('yyyy-MM-dd HH:mm:ss').replace(/\s+/g, 'T')
			return
		},
	})
	public updatedAt: DateTime

	@beforeCreate()
	public static async createUUID(model: User) {
		model.secureId = uuid()
	}

	@beforeSave()
	public static async hashPassword(user: User) {
		if (user.$dirty.password) {
			user.password = await Hash.make(user.password)
		}
	}

	@beforeFetch()
	public static withoutSoftDeletes(query: UserQuery) {
		query.where('users.deleted', false)
	}

	@beforeFind()
	public static withoutSoftDeletesFind(query: UserQuery) {
		query.where('users.deleted', false)
	}
}
