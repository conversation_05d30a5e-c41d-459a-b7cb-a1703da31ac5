import type { HttpContextContract } from '@ioc:Adonis/Core/HttpContext'
import Database from '@ioc:Adonis/Lucid/Database'
import Role from 'App/Models/Role'
import User from 'App/Models/User'
import UserInfo from 'App/Models/UserInfo'
import ActionLogChanges from 'App/Services/ActionLogChanges'

import { StoreValidator } from 'App/Validators/Admin/Partner/Import'

export default class ImportController {
	public async store({ request, response, auth }: HttpContextContract) {
		const { users } = await request.validate(StoreValidator)

		const userLogged = auth.user!

		await Database.transaction(async (trx) => {
			for await (const currentUser of users) {
				const {
					email,
					password,
					...dataUserInfo
				} = currentUser

				const user = await User.query()
					.where('email', email)
					.preload('roles')
					.preload('userInfo')
					.first()

				if (user) {
					const role = await Role.query().where('name', 'PARTNER').firstOrFail()

					if (!user.roles.find((role) => role.name === 'PARTNER')) {
						await user.useTransaction(trx).related('roles').attach([role.id])
					}

					await ActionLogChanges.saveLogUserChanges({
						userChange: user,
						userChangedData: {
							...currentUser
						},
						userLogged,
						trx
					})

					user.merge({
						password: password ? password : dataUserInfo.legalDocumentNumber.replace(/[^0-9]/g, '')
					})
					user.useTransaction(trx)
					await user.save()

					const userInfo = user.userInfo
					userInfo.merge({
						...dataUserInfo
					})
					userInfo.useTransaction(trx)
					await userInfo.save()
				} else {
					const newUser = new User()

					newUser.merge({
						email,
						password: password ? password : dataUserInfo.legalDocumentNumber.replace(/[^0-9]/g, ''),
						isFirstAccess: password ? false : true
					})
					newUser.useTransaction(trx)
					await newUser.save()

					const newUserInfo = new UserInfo()
					newUserInfo.merge({
						typeDocument: 'cpf',
						userId: newUser.id,
						...dataUserInfo,
					})
					newUserInfo.useTransaction(trx)
					await newUserInfo.save()

					const rolesSearch = await Role.query().where('name', 'PARTNER')
					await newUser
						.useTransaction(trx)
						.related('roles')
						.sync(rolesSearch.map((role) => role.id))
				}
			}
		})

		return response.ok({
			type: 'success',
			message: 'Parceiros importados com sucesso!',
		})
	}
}
