import type { HttpContextContract } from '@ioc:Adonis/Core/HttpContext'
import Permission from 'App/Models/Permission'

export default class PermissionsController {
	public async index({ response, logger }: HttpContextContract) {
		try {
			const permissions = await Permission.query().select(
				'id',
				'name',
				'slug',
				'description',
				'group'
			)

			return response.ok(permissions)
		} catch (error) {
			logger.error(error)
			return response.badRequest({
				type: 'error',
				message: 'Ocorreu um problema ao buscar permissões.',
			})
		}
	}
}
