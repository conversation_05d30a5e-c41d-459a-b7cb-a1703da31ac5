import type { HttpContextContract } from '@ioc:Adonis/Core/HttpContext'
import Appointment from 'App/Models/Appointment'
import Database from '@ioc:Adonis/Lucid/Database'

export default class DoctorController {
	public async index({ request, response, auth }: HttpContextContract) {
		const { page = 1, limit = 5, status, typeConsult } = request.only(['page', 'limit', 'status', 'typeConsult'])
		const userLogged = auth.user!

		const appointments = await Appointment.query()
			.where('partner_id', userLogged.id)
			.andWhere('partnerType', 'doctor')
			.andWhere((builder) => {
				if (status) {
					builder.andWhereIn('status', status)
				}
			}).andWhere((builder) => {
				if(typeConsult){
					builder.whereHas('schedule', builder => {
						builder.whereILike('typeConsult', `%${typeConsult}%`)
					})
				}
			})
			.preload('specialty', (builder) => {
				builder.select('id', 'name')
			})
			.preload('exam', (builder) => {
				builder.select('id', 'name')
			})
			.preload('patient', (builder) => {
				builder.select('id')
				builder.preload('userInfo', (builderUserInfo) => {
					builderUserInfo.select('name')
				})
			})
			.preload('partner', (builder) => {
				builder.select('id')
				builder.preload('userInfo', (builderUserInfo) => {
					builderUserInfo.select('name')
				})
			})
			.preload('schedule', builder => {
        if(typeConsult){
					builder.andWhere('typeConsult', typeConsult)
				}
				builder.preload('uploads', (builder) => {
					builder.select('url')
				})
			})
			.orderBy('created_at', 'desc')
			.paginate(page, limit)

		return response.ok(appointments)
	}

	public async update({ response, params }: HttpContextContract) {
		await Database.transaction(async (trx) => {
			const appointmentStatus = await Appointment.query().where('secure_id', params.id).firstOrFail()

			appointmentStatus.merge({
				status: 'realized'
			})
			appointmentStatus.useTransaction(trx)
			await appointmentStatus.save()
		})

		return response.ok({
			type: 'success',
			message: 'Status atualizado com sucesso!',
		})
	}

}
