import { permissions, SYSTEM_PERMISSIONS } from 'App/Utils/Permissions'

const permissionsObject = [...permissions]

export type SystemPermissions = (typeof SYSTEM_PERMISSIONS)[number]

type Permission = { name: string; slug: SystemPermissions; group: string; description: string }

type Permissions = { [key in SystemPermissions]: Permission }

export const PERMISSION: Permissions = SYSTEM_PERMISSIONS.reduce((permissions, permission) => {
	return Object.assign(permissions, {
		[permission]: permissionsObject.find(
			(permissionObject) => permissionObject.slug === permission
		),
	})
}, {}) as Permissions

export function isPermissions(rolesNames: SystemPermissions[]) {
	const rolesSlugs = rolesNames.map((roleName) => PERMISSION[roleName].slug)

	return `permission:${rolesSlugs.join(',')}`
}

export function allPermissions() {
	const allPermissionsSlugs = Object.values(PERMISSION).map(({ slug }) => slug)

	return `permission:${allPermissionsSlugs.join(',')}`
}
