import BaseSchema from '@ioc:Adonis/Lucid/Schema'

export default class extends BaseSchema {
  protected tableName = 'user_infos'

  public async up() {
    this.schema.alterTable(this.tableName, (table) => {
      table.integer('accredited_value').after('query_value')
    })
  }

  public async down() {
    this.schema.alterTable(this.tableName, (table) => {
      table.dropColumn('accredited_value')
    })
  }
}
