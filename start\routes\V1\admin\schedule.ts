import Route from '@ioc:Adonis/Core/Route'
import { isPermissions } from 'App/Contants/Permissions'
import { isRoles } from 'App/Contants/Roles'

Route.group(() => {
	Route.resource('schedules', 'V1/Admin/Schedule/MainController').middleware({
		index: ['auth', `${isRoles(['MASTER', 'ADMIN'])}`, isPermissions(['schedule_view'])],
		store: ['auth', `${isRoles(['MASTER', 'ADMIN'])}`, isPermissions(['schedule_create'])],
		show: ['auth', `${isRoles(['MASTER', 'ADMIN'])}`, isPermissions(['schedule_view'])],
		update: ['auth', `${isRoles(['MASTER', 'ADMIN'])}`, isPermissions(['schedule_edit'])],
	})
	Route.post('schedules-dates', 'V1/Admin/Schedule/MainController.updateDates').middleware([
		'auth',
		`${isRoles(['MASTER', 'ADMIN'])}`,
		isPermissions(['schedule_edit']),
	])

	Route.get('schedules-data/:id', 'V1/Admin/Schedule/MainController.showData').middleware([
		'auth',
		`${isRoles(['MASTER', 'ADMIN'])}`,
		isPermissions(['schedule_edit']),
	])

	Route.post('schedules-create-dates', 'V1/Admin/Schedule/MainController.createDates').middleware([
		'auth',
		`${isRoles(['MASTER', 'ADMIN'])}`,
		isPermissions(['schedule_edit']),
	])

	Route.put('schedules-data/:id', 'V1/Admin/Schedule/MainController.updateDataForDates').middleware([
		'auth',
		`${isRoles(['MASTER', 'ADMIN'])}`,
		isPermissions(['schedule_edit']),
	])
})
