import type { HttpContextContract } from '@ioc:Adonis/Core/HttpContext'
import Database from '@ioc:Adonis/Lucid/Database'
import TemplateNotifications from 'App/Models/TemplateNotifications'
import { StoreValidator, UpdateValidator } from 'App/Validators/Admin/TemplateNotification'

export default class MainController {
	public async index({ request, response }: HttpContextContract) {
		const { page = 1, limit = 20, search, type, name } = request.only(['page', 'limit', 'search', 'type', 'name'])

		const templateNotification = await TemplateNotifications.query()
			.where((builder) => {
				if (search) {
					builder.whereILike('name', `%${search}%`)
				}
				if (name) {
					builder.where('name', name)
				}
				if (type) {
					builder.where('type', type)
				}
			})
			.paginate(page, limit)

		response.ok(templateNotification)
	}

	public async store({ request, response }: HttpContextContract) {
		const { name, type, subject, content, email } = await request.validate(StoreValidator)

		await Database.transaction(async (trx) => {
			const newTemplateNotification = new TemplateNotifications()

			newTemplateNotification.merge({
				name,
				type,
				subject,
				content,
				email,
			})

			newTemplateNotification.useTransaction(trx)
			await newTemplateNotification.save()
		})

		response.ok({
			type: 'success',
			message: 'Template de notificação criado com sucesso!',
		})

	}

	public async show({ response, params }: HttpContextContract) {
		const templateNotification = await TemplateNotifications.query()
			.where('secure_id', params.id)
			.firstOrFail()

		response.ok(templateNotification)
	}

	public async update({ request, response, params }: HttpContextContract) {
		const { subject, content, email } = await request.validate(UpdateValidator)

		const templateNotification = await TemplateNotifications.query()
			.where('secure_id', params.id)
			.firstOrFail()

		if (templateNotification.type === 'sms' && content!.length > 160) {
			return response.badRequest({
				type: 'error',
				message: 'O tamanho máximo permitido é de 160 caracteres',
			})
		}

		await Database.transaction(async (trx) => {
			templateNotification.merge({
				subject,
				content,
				email,
			})

			templateNotification.useTransaction(trx)
			await templateNotification.save()
		})

		response.ok({
			type: 'success',
			message: 'Template de notificação atualizado com sucesso!',
		})
	}

	public async destroy({ response, params }: HttpContextContract) {
		const templateNotification = await TemplateNotifications.query()
			.where('secure_id', params.id)
			.firstOrFail()

		await Database.transaction(async (trx) => {
			templateNotification.useTransaction(trx)
			await templateNotification.delete()
		})

		response.ok({
			type: 'warning',
			message: 'Template de notificação excluído com sucesso!',
		})
	}
}
