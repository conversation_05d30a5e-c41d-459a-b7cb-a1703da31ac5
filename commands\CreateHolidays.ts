import { BaseCommand, flags } from '@adonisjs/core/build/standalone'
import Database from '@ioc:Adonis/Lucid/Database'
// import { format, parseISO } from 'date-fns';
// import * as fs from 'fs';
import { DateTime } from 'luxon';

type HolidayJsonData = {
	date: Date;
	description: string;
}

export default class CreateHoliday extends BaseCommand {
  public static commandName = 'create:holidays'

  public static title = 'Criar feriados, usando json'
  public static description = 'Usa um arquivo json, do github, para popular a tabela holidays.'

  @flags.boolean()
	public confirmation: boolean = true

  public static settings = {
    loadApp: true,
    stayAlive: false,
  }

	private convertToHolidayJsonData(data: Record<string, string>): HolidayJsonData[] {
		const entries = Object.entries(data)

		const holidayList = [];

		for (const data of entries) {
			if (data[0] === 'default') {
				break
			}

			const holiday = {
				date: new Date(data[0]),
				description: data[1],
			}
			holidayList.push(holiday as never)
		}

		return holidayList as HolidayJsonData[];
	}

	// private writeJsonToFile(filename: string, data: HolidayJsonData[]) {
	// 	const jsonData = JSON.stringify(data, null, 2);

	// 	fs.writeFileSync(filename, jsonData, 'utf8');
	// }


  public async run() {
		const { default: Holiday } = await import('App/Models/Holiday')
		const holidaysData = await import('../raw-holiday.json');

		try {
      this.ui.sticker()
        .add(this.colors.bold(this.colors.cyan(CreateHoliday.title)))
        .add('')
        .add(this.colors.gray(CreateHoliday.description))
        .add('')
        .add(this.colors.red('O arquivo json, obtido no github, deve estar na raiz do projeto!'))
        .render()

			this.ui.sticker()
        .add(this.colors.yellow('Convertendo arquivo: raw-holiday.json'))
        .add('')
        .render()

			const holidaysFormatted = this.convertToHolidayJsonData(holidaysData as never);

			this.ui.sticker()
        .add(this.colors.green('Arquivo convertido com sucesso!'))
        .add('')
        .render()

			// Caso necessário converter para um arquivo json usar a linha de baixo
			// this.writeJsonToFile('holidays.json', holidaysFormatted);

      await Database.transaction(async trx => {
				for (const formattedHday of holidaysFormatted) {
					const holiday = new Holiday();
					const dateDate = DateTime.fromJSDate(new Date(formattedHday.date.toString()))
					.toUTC()
					.toFormat('yyyy-MM-dd HH:mm:ss')

					holiday.merge({
						//@ts-ignore
						date: dateDate,
						description: formattedHday.description,
					})

					holiday.useTransaction(trx)
					await holiday.save()
				}
      })

      await Database.manager.closeAll()
      this.logger.log(`${this.colors.green('Feriados adicionados com sucesso!')} ${this.ui.icons.tick}`)
    } catch (error) {
      this.logger.logError(error)
      this.logger.log(`${this.colors.red('Ocorreu um erro ao criar o usuário')} ${this.ui.icons.cross}`)
    }
  }
}
