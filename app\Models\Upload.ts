import { DateTime } from 'luxon'
import { BaseModel, <PERSON>One, beforeCreate, column, hasOne } from '@ioc:Adonis/Lucid/Orm'

import { v4 as uuid } from 'uuid'
import User from './User'

export default class Upload extends BaseModel {
	@column({ isPrimary: true, serializeAs: null })
	public id: number

	@column()
	public secureId: string

	@column({ serializeAs: null })
	public userId: number

	@column()
	public name: string

	@column()
	public type: 'image' | 'pdf'

	@column()
	public fileName: string

	@column()
	public fileType: string

	@column()
	public bucket: string

	@column()
	public url: string

	@hasOne(() => User, {
		foreignKey: 'id',
		localKey: 'userId',
	})
	public user: HasOne<typeof User>

	@column.dateTime({
		autoCreate: true,
		serialize: (value: DateTime) => {
			if (value) return value.toFormat('yyyy-MM-dd HH:mm:ss').replace(/\s+/g, 'T')
			return
		},
	})
	public createdAt: DateTime

	@column.dateTime({
		autoCreate: true,
		autoUpdate: true,
		serialize: (value: DateTime) => {
			if (value) return value.toFormat('yyyy-MM-dd HH:mm:ss').replace(/\s+/g, 'T')
			return
		},
	})
	public updatedAt: DateTime

	@beforeCreate()
	public static async createUUID(model: Upload) {
		model.secureId = uuid()
	}
}
