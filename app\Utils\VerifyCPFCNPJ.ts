export function CNPJValidation(value: string | number) {
	if (!value) return false

	// Aceita receber o valor como string, número ou array com todos os dígitos
	const isString = typeof value === 'string'
	const validTypes = isString || Number.isInteger(value) || Array.isArray(value)

	// Elimina valor em formato inválido
	if (!validTypes) return false

	// Filtro inicial para entradas do tipo string
	if (isString) {
		// Limita ao máximo de 18 caracteres, para CNPJ formatado
		if (value.length > 18) return false

		// Teste Regex para veificar se é uma string apenas dígitos válida
		const digitsOnly = /^\d{14}$/.test(value)
		// Teste Regex para verificar se é uma string formatada válida
		const validFormat = /^\d{2}.\d{3}.\d{3}\/\d{4}-\d{2}$/.test(value)

		// Se o formato é válido, usa um truque para seguir o fluxo da validação
		if (digitsOnly || validFormat) true
		// Se não, retorna inválido
		else return false
	}

	// Guarda um array com todos os dígitos do valor
	const match = value.toString().match(/\d/g)
	const numbers = Array.isArray(match) ? match.map(Number) : []

	// Valida a quantidade de dígitos
	if (numbers.length !== 14) return false

	// Elimina inválidos com todos os dígitos iguais
	const items = [...new Set(numbers)]
	if (items.length === 1) return false

	// Cálculo validador
	const calc = (x) => {
		const slice = numbers.slice(0, x)
		let factor = x - 7
		let sum = 0

		for (let i = x; i >= 1; i--) {
			const n = slice[x - i]
			sum += n * factor--
			if (factor < 2) factor = 9
		}

		const result = 11 - (sum % 11)

		return result > 9 ? 0 : result
	}

	// Separa os 2 últimos dígitos de verificadores
	const digits = numbers.slice(12)

	// Valida 1o. dígito verificador
	const digit0 = calc(12)
	if (digit0 !== digits[0]) return false

	// Valida 2o. dígito verificador
	const digit1 = calc(13)
	return digit1 === digits[1]
}

export function CPFValidation(cpf: string) {
	cpf = cpf.replace(/\D+/g, '')
	let Soma = 0
	let Resto: number
	if (cpf == '00000000000') return false

	if (/^(\d)\1+$/.test(cpf)) {
    return false;
  }

	for (let i = 1; i <= 9; i++) Soma = Soma + parseInt(cpf.substring(i - 1, i)) * (11 - i)
	Resto = (Soma * 10) % 11

	if (Resto == 10 || Resto == 11) Resto = 0
	if (Resto != parseInt(cpf.substring(9, 10))) return false

	Soma = 0
	for (let i = 1; i <= 10; i++) Soma = Soma + parseInt(cpf.substring(i - 1, i)) * (12 - i)
	Resto = (Soma * 10) % 11

	if (Resto == 10 || Resto == 11) Resto = 0
	if (Resto != parseInt(cpf.substring(10, 11))) return false
	return true
}
