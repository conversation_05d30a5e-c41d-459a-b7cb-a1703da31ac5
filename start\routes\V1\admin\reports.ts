import Route from '@ioc:Adonis/Core/Route';
import { isPermissions } from 'App/Contants/Permissions';
import { isRoles } from 'App/Contants/Roles';

Route.group(() => {
	Route.get('schedules-and-appointments', 'V1/Admin/Reports/SchedulesAndAppointments.index').middleware([
		'auth',
		`${isRoles(['MASTER', 'ADMIN'])}`,
		isPermissions(['report_appointments_view']),
	]);

	Route.get(
		'schedules-and-appointments-export',
		'V1/Admin/Reports/SchedulesAndAppointments.getExportData'
	).middleware([
		'auth',
		`${isRoles(['MASTER', 'ADMIN'])}`,
		isPermissions(['report_appointments_view']),
	])

	Route.get(
		'schedules-and-appointments-dependencies-get-city/:stateUFS',
		'V1/Admin/Reports/SchedulesAndAppointments.getDependencyCitiesByStateUFS'
	).middleware([
		'auth',
		`${isRoles(['MASTER', 'ADMIN'])}`,
		isPermissions(['report_appointments_view']),
	])
}).prefix('reports');
