import Route from '@ioc:Adonis/Core/Route'
import { isPermissions } from 'App/Contants/Permissions'
import { isRoles } from 'App/Contants/Roles'

Route.group(() => {
	Route.resource('dependents', 'V1/Admin/Dependent/MainController').middleware({
		index: ['auth', `${isRoles(['MASTER', 'ADMIN'])}`, isPermissions(['patients_view'])],
		store: ['auth', `${isRoles(['MASTER', 'ADMIN'])}`, isPermissions(['patients_create'])],
		update: ['auth', `${isRoles(['MASTER', 'ADMIN'])}`, isPermissions(['patients_edit'])],
		destroy: ['auth', `${isRoles(['MASTER', 'ADMIN'])}`, isPermissions(['patients_delete'])],
	})
})
