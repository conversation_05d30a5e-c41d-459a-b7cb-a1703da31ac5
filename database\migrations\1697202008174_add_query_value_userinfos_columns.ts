import BaseSchema from '@ioc:Adonis/Lucid/Schema'

export default class extends BaseSchema {
  protected tableName = 'user_infos'

  public async up () {
    this.schema.alterTable(this.tableName, (table) => {
      table.integer('query_value').after('state')
    })
  }

  public async down () {
    this.schema.alterTable(this.tableName, (table) => {
      table.dropColumn('query_value')
    })
  }
}
