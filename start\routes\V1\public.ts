import Route from '@ioc:Adonis/Core/Route'

Route.group(() => {
	Route.post('sign-up', 'V1/Public/SignUpController.store')
	Route.get('specialties', 'V1/Public/SpecialtyController.index')
	Route.get('attendants', 'V1/Public/AttendantController.index')
	Route.get('exams', 'V1/Public/ExamsController.index')
	Route.get('states', 'V1/Public/CitiesController.indexStates')
	Route.get('cities', 'V1/Public/CitiesController.indexCities')
	Route.post('join-room', 'V1/Admin/Twilio/TwilioController.joinRoom')
}).prefix('public')
