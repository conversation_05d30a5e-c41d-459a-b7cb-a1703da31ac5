import BaseSchema from '@ioc:Adonis/Lucid/Schema'

export default class extends BaseSchema {
	protected tableName = 'appointments'

	public async up() {
		this.schema.createTable(this.tableName, (table) => {
			table.increments('id')
			table.string('secure_id')
			table
				.integer('user_id')
				.unsigned()
				.references('id')
				.inTable('users')
				.onUpdate('CASCADE')
				.onDelete('RESTRICT')
			table
				.integer('patient_id')
				.unsigned()
				.references('id')
				.inTable('users')
				.onUpdate('CASCADE')
				.onDelete('RESTRICT')
			table
				.integer('partner_id')
				.unsigned()
				.references('id')
				.inTable('users')
				.onUpdate('CASCADE')
				.onDelete('RESTRICT')
			table
				.integer('specialty_id')
				.unsigned()
				.references('id')
				.inTable('specialties')
				.onUpdate('CASCADE')
				.onDelete('RESTRICT')
			table
				.integer('exam_id')
				.unsigned()
				.references('id')
				.inTable('exams')
				.onUpdate('CASCADE')
				.onDelete('RESTRICT')
			table
				.integer('user_by_canceled_id')
				.unsigned()
				.references('id')
				.inTable('users')
				.onUpdate('CASCADE')
				.onDelete('RESTRICT')
			table
				.integer('schedule_id')
				.unsigned()
				.references('id')
				.inTable('schedules')
				.onUpdate('CASCADE')
				.onDelete('RESTRICT')
			table.enu('partner_type', ['doctor', 'clinic', 'lab'])
			table.dateTime('date', { useTz: true })
			table.enu('status', ['waiting', 'canceled', 'realized'])
			table.dateTime('date_canceled', { useTz: true })
			table.enu('type_canceled', ['patient', 'partner', 'backoffice'])
			table.text('motive_canceled')

			table.dateTime('created_at', { useTz: true })
			table.dateTime('updated_at', { useTz: true })
		})
	}

	public async down() {
		this.schema.dropTable(this.tableName)
	}
}
