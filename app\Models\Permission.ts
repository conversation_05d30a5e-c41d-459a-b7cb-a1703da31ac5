import { DateTime } from 'luxon'
import { BaseModel, beforeCreate, column } from '@ioc:Adonis/Lucid/Orm'
import { SystemPermissions } from 'App/Contants/Permissions'
import { v4 as uuid } from 'uuid'

export default class Permission extends BaseModel {
	@column({ isPrimary: true, serializeAs: null })
	public id: number

	@column()
	public secureId: string

	@column()
	public name: string

	@column()
	public slug: SystemPermissions

	@column()
	public group: string

	@column()
	public description: string

	@column.dateTime({
		autoCreate: true,
		serialize: (value: DateTime) => {
			if (value) return value.toFormat('yyyy-MM-dd HH:mm:ss').replace(/\s+/g, 'T')
			return
		},
	})
	public createdAt: DateTime

	@column.dateTime({
		autoCreate: true,
		autoUpdate: true,
		serialize: (value: DateTime) => {
			if (value) return value.toFormat('yyyy-MM-dd HH:mm:ss').replace(/\s+/g, 'T')
			return
		},
	})
	public updatedAt: DateTime

	@beforeCreate()
	public static async createUUID(model: Permission) {
		model.secureId = uuid()
	}
}
