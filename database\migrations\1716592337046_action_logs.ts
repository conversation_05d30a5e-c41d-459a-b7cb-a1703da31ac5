import BaseSchema from '@ioc:Adonis/Lucid/Schema'

export default class extends BaseSchema {
	protected tableName = 'action_logs'

	public async up() {
		this.schema.createTable(this.tableName, (table) => {
			table.increments('id')
			table.integer('user_id')
				.unsigned()
				.references('id')
				.inTable('users')
				.onUpdate('CASCADE')
				.onDelete('SET NULL')
			table.integer('charged_id')
			table.enum('type', ['user', 'specialty', 'exam'])
			table.timestamp('date')
			table.text('charged_data', 'longtext')

			/**
			 * Uses timestamptz for PostgreSQL and DATETIME2 for MSSQL
			 */
			table.timestamp('created_at')
			table.timestamp('updated_at')
		})
	}

	public async down() {
		this.schema.dropTable(this.tableName)
	}
}
