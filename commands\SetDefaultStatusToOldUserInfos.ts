import { BaseCommand, flags } from '@adonisjs/core/build/standalone'
import Database from '@ioc:Adonis/Lucid/Database'
import UserInfo from 'App/Models/UserInfo'

export default class SetDefaultStatusToOldUserInfos extends BaseCommand {
  /**
   * Command name is used to run the command
   */
  public static commandName = 'set:default_status_to_old_user_infos'

	public static title = 'Modificar status padrão para usuários antigos'

  /**
   * Command description is displayed in the "help" output
   */
  public static description = 'Seta um status padrão para os usuários antigos, onde os status estão nulos.'

	@flags.boolean()
	public confirmation: boolean = true

  public static settings = {
    loadApp: true,
    stayAlive: false,
  }

  public async run() {
    this.ui.sticker()
			.add(this.colors.bold(this.colors.cyan(SetDefaultStatusToOldUserInfos.title)))
			.add('')
			.add(this.colors.gray(SetDefaultStatusToOldUserInfos.description))
			.add('')
			.render()

		await Database.transaction(async (trx) => {
			await UserInfo.query()
				.whereNull('status')
				.update({ status: 'active' })
				.useTransaction(trx)

				trx.commit()
		})

  }
}
