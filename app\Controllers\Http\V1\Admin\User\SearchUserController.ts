import { HttpContextContract } from '@ioc:Adonis/Core/HttpContext'
import User from 'App/Models/User'

export default class SearchUserController {
	public async show({ response, params, auth }: HttpContextContract) {
		const userLogged = auth.user!

		const user = await User.query()
			.where('email', params.email)
			.andWhereNot('id', userLogged.id)
			.andWhereHas('roles', (builder) => {
				builder.whereNotIn('slug', ['master'])
			})
			.preload('userInfo')
			.firstOrFail()

		return response.ok({
			name: user.userInfo.name,
			secure_id: user.secureId,
		})
	}
}
