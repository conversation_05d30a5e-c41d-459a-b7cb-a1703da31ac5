import { DateTime } from "luxon";
import { v4 as uuid } from 'uuid';
import { BaseModel, beforeCreate, column, HasOne, hasOne } from "@ioc:Adonis/Lucid/Orm";

import Schedule from "./Schedule";

export default class Observations extends BaseModel {
	@column({ isPrimary: true, serializeAs: null })
	public id: number

	@column({ serializeAs: 'secureId' })
	public secure_id: string

	@column({ serializeAs: null })
	public schedule_id: number

	@column()
	public observation: string

	@column({ serializeAs: null })
	public is_active: boolean

	@column.dateTime({
		autoCreate: true,
		serialize: (value: DateTime) => {
			if (value) return value.toFormat('yyyy-MM-dd HH:mm:ss').replace(/\s+/g, 'T')
			return
		},
	})
	public createdAt: DateTime

	@column.dateTime({
		autoCreate: true,
		autoUpdate: true,
		serialize: (value: DateTime) => {
			if (value) return value.toFormat('yyyy-MM-dd HH:mm:ss').replace(/\s+/g, 'T')
			return
		},
	})
	public updatedAt: DateTime

	// XXX: Foi invertido os valores de localKey e foreignKey
	@hasOne(() => Schedule, {
		foreignKey: 'id',
		localKey: 'schedule_id',
	})
	public schedule: HasOne<typeof Schedule>

	@beforeCreate()
	public static async createUUID(model: Observations) {
		if (!model.secure_id) {
			model.secure_id = uuid()
		}
	}
}
