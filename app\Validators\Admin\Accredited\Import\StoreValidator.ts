import { schema, rules, CustomMessages } from '@ioc:Adonis/Core/Validator'
import { HttpContextContract } from '@ioc:Adonis/Core/HttpContext'

export default class StoreValidator {
	constructor(protected ctx: HttpContextContract) { }

	public schema = schema.create({
		users: schema.array().members(schema.object().members({
			name: schema.string([rules.trim()]),
			email: schema.string([rules.trim()]),
			legalDocumentNumber: schema.string.optional([rules.trim()]),
			dddCell: schema.number([rules.trim()]),
			cell: schema.number([rules.trim()]),
			birthDate: schema.date.optional(),
			adviceRegister: schema.string([rules.trim()]),
			paymentMethods: schema.string.optional([rules.trim()]),
			typeOfCare: schema.enum.optional((['in_person', 'video_call', 'both'] as const)),
			type: schema.enum(['doctor', 'clinic', 'hospital', 'lab'] as const),
			password: schema.string.optional([rules.trim()]),
			zipCode: schema.string.optional([rules.trim()]),
			street: schema.string.optional([rules.trim()]),
			number: schema.string.optional([rules.trim()]),
			complement: schema.string.optional([rules.trim()]),
			neighborhood: schema.string.optional([rules.trim()]),
			city: schema.string([rules.trim()]),
			state: schema.string([rules.trim()]),
			queryValue: schema.number([rules.trim()]),
			specialtiesNames: schema.string.optional(),
			examsNames: schema.string.optional(),
		}))
	})

	private getFields = (field) => {
		const fieldI18N = this.ctx.i18n.formatMessage(`fields.${field}`)

		const isI18N = !fieldI18N.match(/translation/)

		if (isI18N && fieldI18N) {
			return fieldI18N
		} else {
			return field
		}
	}

	private getValidator = (rule, field) => {
		const fieldI18N = this.ctx.i18n.formatMessage(`validator.shared.${field}.${rule}`)
		const isI18N = fieldI18N.match(/translation/)

		if (isI18N === null && fieldI18N) {
			return `validator.shared.${field}.${rule}`
		} else {
			return `validator.shared.${rule}`
		}
	}

	public messages: CustomMessages = {
		'*': (field, rule, arrayExpressionPointer, options) => {
			try {
				return this.ctx.i18n.formatMessage(this.getValidator(rule, field), {
					field: this.getFields(field),
				})
			} catch (_) {
				return this.ctx.i18n.validatorMessages()['*'](field, rule, arrayExpressionPointer, options)
			}
		},
		'users.*.name.required': 'Nome é um campo obrigatório',
		'users.*.email.required': 'E-mail é um campo obrigatório',
		'users.*.legalDocumentNumber.required': 'Documento legal é um campo obrigatório',
		'users.*.dddCell.required': 'Celular é um campo obrigatório',
		'users.*.cell.required': 'Celular é um campo obrigatório',
		'users.*.birthDate.required': 'Data de nascimento é um campo obrigatório',
		'users.*.adviceRegister.required': 'Registro é um campo obrigatório',
		'users.*.paymentMethods.required': 'Méotods de pagamento é um campo obrigatório',
		'users.*.typeOfCare.required': 'Tipo de atendimento é um campo obrigatório',
		'users.*.status.required': 'Status é um campo obrigatório',
		'users.*.type.required': 'Tipo é um campo obrigatório',
	}
}
