import Route from '@ioc:Adonis/Core/Route'
import { isPermissions } from 'App/Contants/Permissions'
import { isRoles } from 'App/Contants/Roles'

Route.group(() => {
	Route.resource('users', 'V1/Admin/User/MainController').middleware({
		index: ['auth', `${isRoles(['MASTER', 'ADMIN'])}`, isPermissions(['users_view'])],
		store: ['auth', `${isRoles(['MASTER', 'ADMIN'])}`, isPermissions(['users_create'])],
		update: ['auth', `${isRoles(['MASTER', 'ADMIN'])}`, isPermissions(['users_edit'])],
		destroy: ['auth', `${isRoles(['MASTER', 'ADMIN'])}`, isPermissions(['users_delete'])],
	})

	Route.resource('user-permissions', 'V1/Admin/User/UpdatePermissionsController').middleware({
		update: ['auth', `${isRoles(['MASTER', 'ADMIN'])}`, isPermissions(['users_edit'])],
	})

	Route.get('search-user/:email', 'V1/Admin/User/SearchUserController.show').middleware([
		'auth',
		`${isRoles(['MASTER', 'ADMIN'])}`,
	])
})
