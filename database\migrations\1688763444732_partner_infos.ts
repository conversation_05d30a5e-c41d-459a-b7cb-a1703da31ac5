import BaseSchema from '@ioc:Adonis/Lucid/Schema'

export default class extends BaseSchema {
  protected tableName = 'partner_infos'

  public async up() {
    this.schema.dropTable(this.tableName)
  }

  public async down() {
    this.schema.createTable(this.tableName, (table) => {
      table.increments('id').primary()
      table
        .integer('user_id')
        .unsigned()
        .references('id')
        .inTable('users')
        .onUpdate('CASCADE')
        .onDelete('CASCADE')
      table.string('advice_register')
      table.text('payment_methods')
      table.enu('type_of_care', ['in_person', 'video_call', 'both'])
      table.enu('gender', ['masculine', 'feminine'])
      table.integer('ddd_phone').unsigned()
      table.integer('phone').unsigned()
      table.integer('ddd_cell')
      table.integer('cell')
      table.string('street')
      table.string('number')
      table.string('complement')
      table.string('neighborhood')
      table.string('zip_code')
      table.string('city')
      table.string('state')

      table.dateTime('created_at')
      table.dateTime('updated_at')
    })
  }
}
