import type { HttpContextContract } from '@ioc:Adonis/Core/HttpContext'
import Database from '@ioc:Adonis/Lucid/Database'
import Role from 'App/Models/Role'
import Schedule from 'App/Models/Schedule'
import Upload from 'App/Models/Upload'
import User from 'App/Models/User'
import UserInfo from 'App/Models/UserInfo'
import ActionLogChanges from 'App/Services/ActionLogChanges'

import { StoreValidator, UpdateValidator } from 'App/Validators/Admin/Patient'

type PatientDatabaseDTO = {
	secure_id: string;
	email: string;
	userInfo: {
		name: string;
		ddd_cell: string;
		cell: string;
		legal_document_number: string;
		birth_date: string;
	}

	isActive: boolean;

	partners: PartnerDatabaseDTO[];
}

type PartnerDatabaseDTO = {
	secure_id: string;
	userInfo: {
		name: string
	}
}

export default class PatientController {
	public async index({ response, request, auth }: HttpContextContract) {
		const { page = 1, limit = 15, search, status } = request.only(['page', 'limit', 'search', 'status'])

		const userLogged = auth.user!
		const rawPatients = await User.query()
			.select('users.id', 'users.secure_id', 'users.email', 'users.is_active')
			.join('user_roles', 'users.id', 'user_roles.user_id')
			.join('roles', 'roles.id', 'user_roles.role_id')
			.where('roles.slug', 'patient')
			.andWhere((builder) => {
				if (status === 'active') {
					builder.andWhere('users.is_active', true)
				} else if (status === 'inactive') {
					builder.andWhere('users.is_active', false)
				}

				if (search) {
					builder.whereRaw(`LOWER(users.email) like LOWER('%${search}%')`)
					builder.orWhereHas('userInfo', builder => {
						builder.whereRaw(`LOWER(name) like LOWER('%${search}%')`)
						builder.orWhereRaw(`LOWER(legal_document_number) like LOWER('%${search}%')`)
						builder.orWhereRaw(`LOWER(zip_code) like LOWER('%${search}%')`)
						builder.orWhere(builder => {
							builder.whereRaw(`LOWER(CONCAT(LOWER(ddd_cell), '', LOWER(cell))) LIKE LOWER(?)`, [`%${search}%`]);
							builder.orWhereRaw(`LOWER(CONCAT(LOWER(ddd_cell), ' ', LOWER(cell))) LIKE LOWER(?)`, [`%${search}%`]);
						})
					})

					if (status === 'active') {
						builder.andWhere('users.is_active', true)
					} else if (status === 'inactive') {
						builder.andWhere('users.is_active', false)
					}
				}
			})
			.preload('partners', (partnerBuilder) => {
				partnerBuilder
					.select('secure_id', 'id')
					.preload('userInfo', (nestedBuilder) => {
						nestedBuilder.select('name')
					}
					)
			})
			.andWhereNot('users.id', userLogged.id)
			.andWhere('users.type', 'patient')
			.andWhereNot('users.deleted', true)
			.preload('userInfo', builder => {
				builder.select('id', 'name', 'ddd_cell', 'cell', 'legal_document_number', 'birth_date')
			})
			.join('user_infos', 'user_infos.user_id', 'users.id')
			// .orderBy('user_infos.name', 'asc')
			.paginate(page, limit)

		const serializedPatients = rawPatients.serialize();

		const meta = serializedPatients.meta;
		const patients = serializedPatients.data.map((patient: PatientDatabaseDTO) => {
			const firstPartner = patient.partners[0];

			const partner = firstPartner && firstPartner?.secure_id ? {
				secureId: firstPartner?.secure_id,
				name: firstPartner?.userInfo?.name,
			} : undefined;

			return {
				secure_id: patient.secure_id,
				email: patient?.email,
				userInfo: {
					name: patient?.userInfo?.name,
					ddd_cell: patient?.userInfo?.ddd_cell,
					cell: patient?.userInfo?.cell,
					legal_document_number: patient?.userInfo?.legal_document_number,
					birthDate: patient?.userInfo?.birth_date,
				},
				isActive: patient.isActive,
				partner: partner,
			}
		})

		return response.ok({ meta: meta, data: patients })
	}

	public async store({ request, response }: HttpContextContract) {
		const {
			avatarSecureId,
			partnerSecureId,
			userExists,
			email,
			password,
			isActive,
			...dataUserInfo
		} = await request.validate(
			StoreValidator
		)

		const user = await User.query().where('email', email).preload('roles').first()

		if (userExists && user && user.roles.find((role) => role.name === 'PATIENT')) {
			return response.badRequest({
				type: 'warning',
				message: 'Esse usuário já foi cadastrada como paciente!',
			})
		}

		await Database.transaction(async (trx) => {
			if (userExists && user) {
				const role = await Role.query().where('name', 'PATIENT').firstOrFail()

				if (!user.roles.find((role) => role.name === 'PATIENT')) {
					await user.useTransaction(trx).related('roles').attach([role.id])
				}
			} else {
				const newUser = new User()

				if (avatarSecureId) {
					const avatar = await Upload.query().where('secure_id', avatarSecureId).firstOrFail()

					newUser.merge({
						avatarId: avatar.id,
					})
				}

				newUser.merge({
					email,
					password,
					is_active: isActive,
				})
				newUser.useTransaction(trx)
				await newUser.save()

				const newUserInfo = new UserInfo()
				newUserInfo.merge({
					...dataUserInfo,
					userId: newUser.id,
				})
				newUserInfo.useTransaction(trx)
				await newUserInfo.save()

				const rolesSearch = await Role.query().where('name', 'PATIENT')
				await newUser
					.useTransaction(trx)
					.related('roles')
					.sync(rolesSearch.map((role) => role.id))


				if (partnerSecureId) {
					const partner = await User.query()
						.where('secure_id', partnerSecureId)
						.firstOrFail()

					await partner.useTransaction(trx).related('patients').attach([newUser.id])
				}
			}
		})

		return response.ok({
			type: 'success',
			message: 'Paciente criado com sucesso!',
		})
	}

	public async show({ response, params }: HttpContextContract) {
		const patient = await User.query()
			.select('id', 'avatar_id', 'secure_id', 'email', 'is_active')
			.where('secure_id', params.id)
			.andWhereHas('roles', (builder) => {
				builder.whereIn('slug', ['patient'])
			})
			.andWhere('type', 'patient')
			.andWhereNot('deleted', true)
			.preload('userInfo')
			.preload('avatar', (builderAvatar) => {
				builderAvatar.select('id', 'secure_id', 'url', 'name')
			})
			.preload('dependents', dependentBuilder => {
				dependentBuilder.select('id', 'secure_id', 'email')
				dependentBuilder.preload('userInfo', (nestedBuilder) => {
					nestedBuilder.select('name', 'legal_document_number', 'birth_date')
				})
			})
			.preload('partners', builderPartner => {
				builderPartner.select('id', 'avatar_id', 'secure_id', 'email')
				builderPartner.preload('userInfo')
			})
			.firstOrFail()

		return response.ok(patient)
	}

	public async update({ response, request, params, auth }: HttpContextContract) {
		const userLogged = auth.user!

		const dataRequest = await request.validate(UpdateValidator)

		const {
			avatarSecureId,
			partnerSecureId,
			email,
			password,
			isActive,
			...dataUser
		} = dataRequest

		const user = await User.query()
			.where('secure_id', params.id)
			.andWhereHas('roles', (builder) => {
				builder.whereIn('slug', ['patient'])
			})
			.andWhereNull('parent_id')
			.andWhereNot('deleted', true)
			.preload('userInfo')
			.preload('partners')
			.firstOrFail()

		await Database.transaction(async (trx) => {
			await ActionLogChanges.saveLogUserChanges({
				userChange: user,
				userChangedData: {
					...dataRequest
				},
				userLogged,
				trx
			})

			if (avatarSecureId) {
				const avatar = await Upload.query().where('secure_id', avatarSecureId).firstOrFail()

				user.merge({
					avatarId: avatar.id,
				})
			}

			user.merge({ email, password, is_active: isActive })
			user.useTransaction(trx)
			await user.save()

			const userInfo = user.userInfo
			userInfo.merge({ ...dataUser })
			userInfo.useTransaction(trx)
			await userInfo.save()

			if (partnerSecureId) {
				const partner = await User.query()
					.where('secure_id', partnerSecureId)
					.firstOrFail()

				if (user.partners[0] && user.partners[0].id !== partner.id) {
					await user.useTransaction(trx).related('partners').detach([user.partners[0].id])
				}

				await partner.useTransaction(trx).related('patients').attach([user.id])
			}
		})

		return response.ok({
			type: 'success',
			message: 'Paciente atualizado com sucesso!',
		})
	}

	public async destroy({ response, params }: HttpContextContract) {
		const patient = await User.query()
			.where('secure_id', params.id)
			.andWhereHas('roles', (builder) => {
				builder.whereIn('slug', ['patient'])
			})
			.andWhere('type', 'patient')
			.firstOrFail()

		patient.merge({ deleted: true })
		await patient.save()

		return response.ok({
			type: 'success',
			message: 'Paciente removido com sucesso!',
		})
	}

	public async inactivate({ response, params }: HttpContextContract) {
		const patientSecureId = params.patientSecureId

		const patient = await User.query()
			.where('secure_id', patientSecureId)
			.andWhereHas('roles', (builder) => {
				builder.whereIn('slug', ['patient'])
			})
			.andWhere('type', 'patient')
			.firstOrFail()

		patient.merge({ is_active: false })
		await patient.save()

		return response.ok({
			type: 'success',
			message: 'Paciente inativado com sucesso!',
		})
	}

	public async activate({ request, response, params }: HttpContextContract) {
		const patientSecureId = params.patientSecureId;
		const { isActive } = request.only(['isActive']);

		if (isActive) {
			const patient = await User.query()
				.where('secure_id', patientSecureId)
				.andWhereHas('roles', (builder) => {
					builder.whereIn('slug', ['patient'])
				})
				.andWhere('type', 'patient')
				.firstOrFail()

			patient.merge({ is_active: true })
			await patient.save()

			return response.ok({
				type: 'success',
				message: 'Paciente ativado com sucesso!',
			})
		}

	}

	public async history({ request, response, params, auth }: HttpContextContract) {
		// Status reperesenta a união dos status de schedule e de appointment.
		// Field respresenta o campo que será utilizado para a busca.
		const {
			page = 1,
			limit = 15,
			search,
			status,
			// field,
			// direction
		} = request.only([
			'page',
			'limit',
			'search',
			'status',
		]);

		const patientSecureID = await params.patientSecureId;

		const userLogged = auth.user!;

		const rawPatientData = await User.query()
			.select('id', 'secure_id')

			.where('secure_id', patientSecureID)
			.andWhereHas('roles', (builder) => {
				builder.whereIn('slug', ['patient'])
			})
			.andWhere('type', 'patient')

			.andWhereNot('deleted', true)
			.andWhereNot('users.id', userLogged.id)

			.firstOrFail()

		const patientID = rawPatientData.$attributes.id;

		const rawScheduleData = await Schedule.query()
			.select(
				'id',
				'secure_id',
				'patient_id',

				'specialty_id',
				'exam_id',

				'status',

				'created_at'
			)
			.where('patient_id', patientID)

			.preload('specialty', (specialtyBuilder) => {
				specialtyBuilder.select('name')
			})
			.preload('exam', (examBuilder) => {
				examBuilder.select('name')
			})
			.preload('appointment', (appointmentBuilder) => {
				appointmentBuilder.select('partner_id', 'date', 'secure_id', 'status')
					.preload('partner', (partnerBuilder) => {
						partnerBuilder.select('secure_id', 'id')
							.preload('userInfo', (partnerUserInfoBuilder) => {
								partnerUserInfoBuilder.select('name')
							})
					})
				// if (status?.[0]) {
				// 	appointmentBuilder.whereIn('status', status)
				// }
			})
			.andWhere((andWhereBuilder) => {
				if (search) {
					andWhereBuilder.whereRaw(`LOWER(schedules.secure_id) like LOWER('%${search}%')`)
					andWhereBuilder.orWhereHas('specialty', specialtyBuilder => {
						specialtyBuilder.whereRaw(`LOWER(name) like LOWER('%${search}%')`)
					})
					andWhereBuilder.orWhereHas('exam', examBuilder => {
						examBuilder.whereRaw(`LOWER(name) like LOWER('%${search}%')`)
					})
					andWhereBuilder.orWhereHas('appointment', appointmentBuilder => {
						appointmentBuilder.orWhereHas('partner', partnerBuilder => {
							partnerBuilder.whereRaw(`LOWER(users.email) like LOWER('%${search}%')`)
							partnerBuilder.orWhereHas('userInfo', partnerUserInfoBuilder => {
								partnerUserInfoBuilder.whereRaw(`LOWER(name) like LOWER('%${search}%')`)
							})
						})
					})
				}

				if (status?.[0] === 'approved') {
					andWhereBuilder.andWhere((builder) => {
						builder.orWhereHas('appointment', (app) => {
							app.orWhereIn('status', status)
						})
					})
				}

				if (status?.[0]) {
					andWhereBuilder.andWhere((builder) => {
						builder.orWhereIn('status', status)
						builder.orWhereHas('appointment', (app) => {
							app.orWhereIn('status', status)
						})
					})
				}
			})
			// .andWhere((andWhereBuilder) => {
			// 	if (status && status.length > 0) {
			// 		andWhereBuilder.whereIn('status', status)
			// 	}
			// })
			.paginate(page, limit)

		const serializedSchedules = rawScheduleData.serialize();

		const data = serializedSchedules.data.map((item) => {
			const specialty = item?.specialty?.name ? item?.specialty?.name : undefined;
			const exam = item?.exam?.name ? item?.exam?.name : undefined;
			const partner = item?.appointment?.partner;

			return {
				secureId: item.secure_id,
				status: item?.appointment?.status ? item?.appointment?.status : item.status,
				solicitationDate: item.created_at,
				type: item?.appointment ? 'appointment' : 'schedule',

				appointmentSecureId: item.appointment?.secure_id,
				appointmentDate: item.appointment?.date,

				partner: {
					secureId: partner?.secure_id,
					name: partner?.userInfo?.name,
				},

				specialtyOrExam: specialty ? specialty : exam,
			}
		});

		const meta = serializedSchedules.meta;

		return response.ok({
			meta: meta,
			data: data
		})
	}
}
