import Database from '@ioc:Adonis/Lucid/Database';
import type { HttpContextContract } from '@ioc:Adonis/Core/HttpContext';

import Schedule from 'App/Models/Schedule';
import Observations from 'App/Models/Observations';

import StoreValidator from 'App/Validators/Admin/Observation/StoreValidation';
import UpdateValidator from 'App/Validators/Admin/Observation/UpdateValidator';

import ActionLogChanges from 'App/Services/ActionLogChanges';
import ActionLog from 'App/Models/ActionLog';

export default class ObservationController {
	public async index() {}

	public async show() {}

	public async store({ request, response, auth }: HttpContextContract) {
		const { observation, scheduleSecureId, from } = await request.validate(StoreValidator);

		const userLogged = auth.user!;
		await userLogged.load('roles');

		const rawSchedule = (
			await Schedule.query()
				.where('secureId', scheduleSecureId)
				.firstOrFail()
			)


		const scheduleId = rawSchedule.$attributes.id;

		await Database.transaction(async (trx) => {
			const newObservation = new Observations();
			newObservation.merge({
				observation,
				schedule_id: scheduleId,
			})
			newObservation.useTransaction(trx)
			await newObservation.save()

			await ActionLogChanges.saveLogObservationChanges({
				observation: newObservation,
				observationChangedData: {
					status: 'created'
				},
				userLogged,
				trx,
				from: from as ActionLog['type']
			})
		})

		return response.status(201).json({});

		// const rawSchedule = (
		// 	await Schedule.query()
		// 		.select('id')
		// 		.where('secureId', scheduleSecureId)
		// 		.firstOrFail()
		// 	)

		// const scheduleId = rawSchedule.$attributes.id;

		// const newObservation = new Observations();

		// await Database.transaction(async (trx) => {
		// 	newObservation.merge({
		// 		observation,
		// 		schedule_id: scheduleId,
		// 	})

		// 	newObservation.useTransaction(trx)
		// 	await newObservation.save()
		// })

		// return response.status(201).json({
		// 	message: 'Observação criada com sucesso',
		// 	data: newObservation
		// })
	}

	public async update({ request, response, params, auth }: HttpContextContract) {
		const { observation, from } = await request.validate(UpdateValidator);

		const userLogged = auth.user!;
		await userLogged.load('roles');
		const observationSecureId = params.id;

		const rawObservation = (
			await Observations.query()
				.where('secure_id', observationSecureId)
				.firstOrFail()
			)

		await Database.transaction(async (trx) => {
			rawObservation.merge({
				observation
			})
			rawObservation.useTransaction(trx)
			await rawObservation.save()

			await ActionLogChanges.saveLogObservationChanges({
				observation: rawObservation,
				observationChangedData: {
					status: 'updated'
				},
				userLogged,
				trx,
				from: from as ActionLog['type']
			})
		})


		return response.status(200).json({
			message: 'Observação atualizada com sucesso',
			data: rawObservation
		})
	}

	// public async destroy({ response, params, auth }: HttpContextContract) {
	// 	const observationSecureId = params.id;
	// 	const userLogged = auth.user!;
	// 	await userLogged.load('roles');

	// 	const rawObservation = (
	// 		await Observations.query()
	// 			.where('secure_id', observationSecureId)
	// 			.firstOrFail()
	// 	)

	// 	await Database.transaction(async (trx) => {
	// 		rawObservation.merge({
	// 			is_active: false
	// 		})
	// 		rawObservation.useTransaction(trx)
	// 		await rawObservation.save()

	// 		await ActionLogChanges.saveLogObservationChanges({
	// 			observation: rawObservation,
	// 			observationChangedData: {
	// 				status: 'deleted'
	// 			},
	// 			userLogged,
	// 			trx
	// 		})
	// 	})

	// 	return response.status(200).json({
	// 		message: 'Observação deletada com sucesso',
	// 		data: rawObservation
	// 	})
	// }

	// public async deleteFromSchedule({ response, params, auth }: HttpContextContract) {
	// 	const observationSecureId = params.observationSecureId;
	// 	const userLogged = auth.user!;
	// 	await userLogged.load('roles');

	// 	const rawObservation = (
	// 		await Observations.query()
	// 			.where('secure_id', observationSecureId)
	// 			.firstOrFail()
	// 	)

	// 	await Database.transaction(async (trx) => {
	// 		rawObservation.merge({
	// 			is_active: false
	// 		})
	// 		rawObservation.useTransaction(trx)
	// 		await rawObservation.save()

	// 		await ActionLogChanges.saveLogObservationChanges({
	// 			observation: rawObservation,
	// 			observationChangedData: {
	// 				status: 'deleted'
	// 			},
	// 			userLogged,
	// 			trx,
	// 			from: 'schedule'
	// 		})
	// 	})

	// 	return response.status(200).json({
	// 		message: 'Observação deletada com sucesso',
	// 		data: rawObservation
	// 	})
	// }

	// public async deleteFromAppointment({ response, params, auth }: HttpContextContract) {
	// 	const observationSecureId = params.observationSecureId;
	// 	const userLogged = auth.user!;
	// 	await userLogged.load('roles');

	// 	const rawObservation = (
	// 		await Observations.query()
	// 			.where('secure_id', observationSecureId)
	// 			.firstOrFail()
	// 	)

	// 	await Database.transaction(async (trx) => {
	// 		rawObservation.merge({
	// 			is_active: false
	// 		})
	// 		rawObservation.useTransaction(trx)
	// 		await rawObservation.save()

	// 		await ActionLogChanges.saveLogObservationChanges({
	// 			observation: rawObservation,
	// 			observationChangedData: {
	// 				status: 'deleted'
	// 			},
	// 			userLogged,
	// 			trx,
	// 			from: 'appointment'
	// 		})
	// 	})

	// 	return response.status(200).json({
	// 		message: 'Observação deletada com sucesso',
	// 		data: rawObservation
	// 	})
	// }

	// public async deleteFromUser({ response, params, auth }: HttpContextContract) {
	// 	const observationSecureId = params.observationSecureId;
	// 	const userLogged = auth.user!;
	// 	await userLogged.load('roles');

	// 	const rawObservation = (
	// 		await Observations.query()
	// 			.where('secure_id', observationSecureId)
	// 			.firstOrFail()
	// 	)

	// 	await Database.transaction(async (trx) => {
	// 		rawObservation.merge({
	// 			is_active: false
	// 		})
	// 		rawObservation.useTransaction(trx)
	// 		await rawObservation.save()

	// 		await ActionLogChanges.saveLogObservationChanges({
	// 			observation: rawObservation,
	// 			observationChangedData: {
	// 				status: 'deleted'
	// 			},
	// 			userLogged,
	// 			trx,
	// 			from: 'user'
	// 		})
	// 	})

	// 	return response.status(200).json({
	// 		message: 'Observação deletada com sucesso',
	// 		data: rawObservation
	// 	})
	// }

	public async destroy({ response, params, auth }: HttpContextContract) {
		const observationSecureId = params.observationSecureId;
		const type = params.type;

		const userLogged = auth.user!;
		await userLogged.load('roles');

		const rawObservation = (
			await Observations.query()
				.where('secure_id', observationSecureId)
				.firstOrFail()
		)

		await Database.transaction(async (trx) => {
			rawObservation.merge({
				is_active: false
			})
			rawObservation.useTransaction(trx)
			await rawObservation.save()

			await ActionLogChanges.saveLogObservationChanges({
				observation: rawObservation,
				observationChangedData: {
					status: 'deleted'
				},
				userLogged,
				trx,
				from: type as ActionLog['type']
			})
		})

		return response.status(200).json({
			message: 'Observação deletada com sucesso',
			data: rawObservation
		})
	}

	public async getByScheduleSecureId({ response, params }: HttpContextContract) {
		const scheduleSecureId = params.scheduleSecureId;

		const rawObservation = (
			await Observations.query()
			.whereHas('schedule', (scheduleBuilder) => {
				if (scheduleSecureId) {
					scheduleBuilder.where('secure_id', scheduleSecureId)
				}
			})
			.andWhere('is_active', true)
		)

		return response.status(200).json({ observations: rawObservation })
	}
}
